import { create } from 'zustand';
import { getTokenCheckResult } from '../../../service/auth';

/**
 * Token类型枚举
 */
export enum TokenTypeEnum {
    /** 登录token */
    LOGIN = 1,
    /** 分享token(历史类型) */
    SHARE_HISTORY = 2,
    /** 分享链接token */
    SHARE_LINK = 3
}

interface SafetyModeState {
    /** 是否处于安全模式 */
    safeMode: boolean;
    /** 安全模式过期时间戳 */
    safeModeExpireTime?: number;
    /** 倒计时定时器ID */
    countdownTimer?: NodeJS.Timeout;
    /** 是否正在加载 */
    loading: boolean;
    /** 是否正在检查安全模式状态，防止重复请求 */
    isCheckingStatus: boolean;
    /** 是否是访客模式 */
    isVisitor: boolean;
}

const initialState: SafetyModeState = {
    safeMode: false,
    safeModeExpireTime: undefined,
    countdownTimer: undefined,
    loading: false,
    isCheckingStatus: false,
    isVisitor: false
};

export class SafetyModeData {
    useDataStore = create<SafetyModeState>()((set, get) => ({
        ...initialState,
    }));

    // 获取安全模式状态
    async getSafetyMode(token?: string) {
        try {
            this.useDataStore.setState({ loading: true });
            const authToken =
                token ||
                (await window.localStorage.getItem('AUTH_TOKEN')) ||
                '';

            if (!authToken) {
                this.useDataStore.setState({ safeMode: false, loading: false });
                return;
            }

            const userInfo = await getTokenCheckResult({ token: authToken });

            this.useDataStore.setState({
                safeMode: userInfo.safeMode,
                safeModeExpireTime: userInfo.safeModeExpireTime,
                loading: false,
                isVisitor: userInfo.tokenType === TokenTypeEnum.SHARE_LINK
            });

            // 如果处于安全模式且有过期时间，开始倒计时
            if (userInfo.safeMode && userInfo.safeModeExpireTime) {
                this.startCountdown(authToken);
            } else {
                // 如果不在安全模式，停止倒计时
                this.stopCountdown();
            }
        } catch (error) {
            console.log(error);
            this.useDataStore.setState({ safeMode: false, loading: false });
        }
    }

    // 设置安全模式状态
    setSafeMode(safeMode: boolean) {
        this.useDataStore.setState({ safeMode });
    }

    // 开始倒计时
    startCountdown(token: string) {
        const { countdownTimer, safeModeExpireTime } =
            this.useDataStore.getState();

        // 清除之前的定时器
        if (countdownTimer) {
            clearInterval(countdownTimer);
        }

        if (!safeModeExpireTime) return;

        const timer = setInterval(async () => {
            //后端返回时间戳不带毫秒
            const currentTime = Date.now() / 1000;

            const { safeModeExpireTime: currentExpireTime, isCheckingStatus } =
                this.useDataStore.getState();

            if (!currentExpireTime || currentTime >= currentExpireTime) {
                // 防止重复请求
                if (isCheckingStatus) {
                    return;
                }

                // 时间到了，重新调用接口检查状态
                this.useDataStore.setState({ isCheckingStatus: true });
                try {
                    await this.getSafetyMode();
                } finally {
                    this.useDataStore.setState({ isCheckingStatus: false });
                }

                // 清除当前定时器，避免重复执行
                this.stopCountdown();
            }
        }, 1000); // 每秒检查一次

        this.useDataStore.setState({ countdownTimer: timer });
    }
    // 停止倒计时
    stopCountdown() {
        const { countdownTimer } = this.useDataStore.getState();
        if (countdownTimer) {
            clearInterval(countdownTimer);
            this.useDataStore.setState({ countdownTimer: undefined });
        }
    }

    // 重置状态
    reset() {
        const { countdownTimer } = this.useDataStore.getState();
        if (countdownTimer) {
            clearInterval(countdownTimer);
        }

        this.useDataStore.setState({
            safeMode: false,
            safeModeExpireTime: undefined,
            countdownTimer: undefined,
            loading: false,
            isCheckingStatus: false,
            isVisitor: false
        });
    }

    destroy() {
        this.useDataStore?.setState(initialState, true);
    }
}

const useSafetyModeStore = new SafetyModeData();
export { useSafetyModeStore };
