declare namespace Request {
    export interface Response<DataType> {
        success: boolean;
        message: Error;
        code: number;
        status?: 'SUCCESS'; // 地图解析需要
        langKey: string;
        data: DataType;
        sid?: string;
        errorVar: string[];
        headers: Record<string, string>;
    }
    export type ResponsePageList<DataType> = Response<{
        list: DataType[];
        total: number;
        page: number;
        pageSize: number;
        hasNextPage?: boolean;
    }>;
}
