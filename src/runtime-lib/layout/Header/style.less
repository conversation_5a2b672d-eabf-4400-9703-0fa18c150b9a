@import (reference) '~@streamax/poppy-themes/starry/index.less';
@import (reference) '~@streamax/poppy-themes/starry/abroad.less';
@import '../../styles/themeStyle.less';

// header区域右侧功能间距
@header-btn-gap: 24px;

.starry-layout-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
    background: @background-color-wrapper;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
    &-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        width: 100%;
        height: 64px;
    }
    &-left-wrapper {
        display: flex;
        flex: 1;
        align-items: center;
        overflow: hidden;
        .use-app-info-app {
            max-width: 30%;
            .app-name {
                flex: 1;
            }
        }
    }
    &-right-wrapper {
        display: flex;
        align-items: center;
        color: @text-color-theme-style;
        .material-language-select-content {
            margin-right: 6px;
        }
    }
    &-help-center-icon {
        margin-right: @header-btn-gap;
        color: @text-color-theme-style-8;
        font-size: 18px;
        cursor: pointer;
    }
    &-download-icon {
        margin-right: @header-btn-gap;
        cursor: pointer;
        .anticon {
            color: @text-color-theme-style-8;
        }
    }
    &-message-count {
        display: flex;
        align-items: center;
        margin-right: @header-btn-gap;
        cursor: pointer;
    }
    &-message-count-icon {
        color: @text-color-theme-style-8;
    }
    &-search,
    &-bell {
        margin-right: 16px;
        padding: 8px;
        cursor: pointer;
    }
    &-user {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
        .security-identifier {
            position: absolute;
            top: -4px;
            left: -3px;
            font-size: 40px;
            color: transparent;
        }        
    }
    &-logout {
        margin-left: @header-btn-gap;
        cursor: pointer;
    }
    &-username {
        margin-right: 8px;
        margin-left: 12px;
        color: @text-color-theme-style-8;
        &-dropicon {
            color: @text-color-theme-style-4;
        }
    }
    &-console {
        display: flex;
        align-items: center;
        color: @text-color-theme-style-10;
        white-space: nowrap;
        cursor: pointer;
        &:after {
            display: inline-block;
            width: 1px;
            height: 16px;
            margin-left: 16px;
            background-color: @text-color-theme-style-4;
            content: ' ';
        }
        .console-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 6px;
        }
        .console-icon:hover{
            background-color:@starry-bg-color-component-hover;
        }
        .active {
            background-color: @starry-bg-color-component-hover;
        }
    }
    &-console-small {
        &:after {
            width: 0 !important;
        }
    }
    &-entry-list {
        width: 360px;
        max-height: 448px;
        margin-bottom: 0;
        padding-left: 0;
        overflow: auto;
        background-color: @starry-bg-color-component-elevated;
        box-shadow: @abroad-shadow-2;
        .entry-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 48px;
            padding: 0 25px;
            color: @text-color-theme-style-10;
            // opacity: 1 !important;
            // visibility: visible !important;
            & > img {
                width: auto;
                height: 16px;
                margin-right: 14px;
            }
            .entry-name {
                flex: 1;
                overflow: hidden;
                font-weight: 400;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .entry-collect-flag {
                visibility: hidden;
            }
            .entry-collect-flag.collected {
                visibility: visible;
            }
            .icon-drag-wrapper {
                visibility: hidden;
            }
            &:hover {
                color: @text-color-theme-style-10;
                background-color: @primary-color-light;
                cursor: pointer;
                .entry-collect-flag {
                    visibility: visible;
                }
                .icon-drag-wrapper {
                    visibility: visible;
                }
                .entry-name {
                    font-weight: 500;
                }
            }
        }
        .entry-item::abroad {
            border-radius: @border-radius-8;
        }
    }
    &-entry-list::abroad {
        padding: 4px;
    }
    &-collected-entrys {
        flex: 1;
        overflow: hidden;
    }
    .starry-layout-header-right-tenant-exchange {
        box-sizing: border-box;
        min-width: 160px;
        max-width: 260px;
        height: 32px;
        margin-right: @header-btn-gap !important;
        margin-left: -4px;
        color: @text-color-theme-style-8;
        border: none;
        border-radius: 2px;
        .poppy-select-arrow {
            color: @text-color-theme-style-8;
        }
        .poppy-select-selection-item {
            color: @text-color-theme-style-6 !important;
            line-height: 30px !important;
        }
    }
    .menu-drawer-trigger {
        margin-right: 24px;
        color: @text-color-theme-style;
        font-size: 20px;
    }
    .keyword-search-trigger-container {
        margin-right: @header-btn-gap;
    }
}
body[data-starry-theme-style='mix'].starry-theme-style-mix {
    .starry-layout-header-console{
        .console-icon:hover{
            background-color:rgba(255,255,255,0.12);
        }
        .active {
            background-color: rgba(255, 255, 255, 0.12);
        }
    }
}
body[data-starry-theme-style='dark'].starry-theme-style-dark {
    .starry-layout-header-console{
        .console-icon:hover{
            background-color:rgba(255,255,255,0.12);
        }
        .active {
            background-color: rgba(255, 255, 255, 0.12);
        }
    }
}

.starry-layout-header-right-tenant-exchange::abroad {
    border-radius: @border-radius-6;
    .poppy-select-selector {
        height: 32px !important;
    }
}

.entrys-dropdown {
    top: 64px !important;
    user-select: none;
}
.poppy-drawer-content {
    position: relative;
    .closeIcon {
        position: absolute;
        top: 24px;
        right: 16px;
        z-index: 10;
    }
}

.entry-item-dragging {
    z-index: 1100;
    display: flex;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 0 25px;
    color: @text-color-theme-style-8;
    background: @background-color-wrapper;
    & > img {
        width: auto;
        height: 16px;
        margin-right: 14px;
    }
    .entry-name {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .entry-collect-flag {
        visibility: visible;
    }
    .icon-drag-wrapper {
        visibility: visible;
    }
}

.@{prefix}-light {
    .starry-layout-header {
        border-bottom: 1px solid #dadde1;
        box-shadow: none;
        &-user {
            border-radius: 50%;
        }
        &-console {
            color: rgba(0, 0, 0, 0.85) !important;
            .active {
                background-color: rgb(226, 227, 230);
            }
        }
        &-entry-list {
            background-color: #fff;
            box-shadow: 0 3px 14px 2px rgba(0, 0, 0, 0.05),
                0 8px 10px 1px rgba(0, 0, 0, 0.06),
                0 5px 5px -3px rgba(0, 0, 0, 0.1);
            .entry-item {
                color: rgba(0, 0, 0, 0.85) !important;
                &:hover {
                    background-color: rgba(0, 0, 0, 0.06);
                }
            }
        }
        &-right-tenant-exchange {
            color: @text-color-theme-style-8;
            background-color: rgba(0, 0, 0, 0.04) !important;
            &:hover {
                background-color: rgba(0, 0, 0, 0.1) !important;
            }
            &.poppy-select-open {
                .poppy-select-selection-item {
                    color: @text-color-theme-style-8;
                }
            }
        }
        .right-wrapper-opt-icon {
            display: flex;
            align-items: center;
            .anticon {
                color: @starry-text-color-primary;
            }
        }
    }
}

.@{prefix}-mix,
.@{prefix}-dark {
    .starry-layout-header {
        &-right-tenant-exchange {
            background: rgba(255, 255, 255, 0.08) !important;
            &:hover {
                background-color: #ffffff1f !important;
            }
        }
        &-entry-list {
            background-color: #454f5b;
            box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05),
                0px 8px 10px 1px rgba(0, 0, 0, 0.06),
                0px 5px 5px -3px rgba(0, 0, 0, 0.1);
            .entry-item {
                color: @text-color-theme-style-10 !important;
                &:hover {
                    background: #ffffff1f;
                }
            }
        }
    }
}
.header-person-info-tooltip {
    .poppy-tooltip-inner {
        padding: 0 !important;
        background-color: transparent;
    }
    .poppy-select-selector {
        color: #333 !important;
    }
    .poppy-select-selection-item {
        margin-right: 0 !important;
        color: #333 !important;
    }
    .poppy-select-arrow {
        color: #333 !important;
    }
    .poppy-popover-content {
        .poppy-popover-inner-content{
            padding: 0;
        }
    }
}
