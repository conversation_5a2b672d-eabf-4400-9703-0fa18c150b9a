/*
 * @LastEditTime: 2025-07-29 09:20:27
 */
import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Button, message } from '@streamax/poppy';
import { InfoCircleOutlined } from '@ant-design/icons';
import { otpUnbind } from '@/service/user';
import i18n from '../../../../../../../i18n';
import './index.scoped.less';
import InfoBack from '@/components/InfoBack';
import { StarryAbroadFormItem } from '@/runtime-lib';

interface OTPUnbindFormProps {
    userId: string;
}

const OTPUnbindForm: React.FC<OTPUnbindFormProps> = (
    { userId },
    ref,
) => {
    const [form] = Form.useForm();

    const handleSubmit = async () => {
        const values = await form.validateFields();
        await otpUnbind({
            otpCode: values.otpCode,
            userId,
        });
        message.success(i18n.t('message', '操作成功'));
    };
    useImperativeHandle(ref, () => {
        return {
            submit: handleSubmit,
        };
    });

    return (
        <div className="otp-unbind-content">
            <div className="otp-unbind-form">
                <div className="unbind-confirmation-info">
                    <InfoBack
                        title={i18n.t(
                            'message',
                            '是否确定解绑该二次验证方式？解绑验证方式前，打开【OTP】输入验证码',
                        )}
                    />
                </div>
                <Form form={form} layout="vertical">
                    <StarryAbroadFormItem
                        name="otpCode"
                        label={i18n.t('name', 'OTP 验证码')}
                        rules={[
                            {
                                required: true,
                                message: i18n.t('message', '请输入动态密码'),
                            },
                        ]}
                    >
                        <Input placeholder="OTP" autoComplete="off" />
                    </StarryAbroadFormItem>
                </Form>
            </div>
          
        </div>
    );
};

export default forwardRef(OTPUnbindForm);
