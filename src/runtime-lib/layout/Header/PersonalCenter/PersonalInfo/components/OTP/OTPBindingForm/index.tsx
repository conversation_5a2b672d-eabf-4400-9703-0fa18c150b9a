import React, {
    useState,
    useEffect,
    forwardRef,
    useImperativeHandle,
    useRef,
} from 'react';
import { Form, Input, Button, message, Spin, Tooltip } from '@streamax/poppy';
import { ReloadOutlined } from '@ant-design/icons';
import {
    otpGenerateQRCode,
    otpGenerateQRCodeLink,
    otpConfirmBinding,
    otpConfirmBindingLink,
} from '@/service/user';
import { QRCodeSVG as QRCode } from 'qrcode.react';
import i18n from '../../../../../../../i18n';
import './index.scoped.less';
import { StarryAbroadFormItem } from '@/runtime-lib';
import { getDomainName } from '@/runtime-lib/utils/commonFun';
// 重命名i18n，这是为了避免被词条工具提取。这里的词条是作为系统词条存在，需手动维护。
const i18next = i18n;
export enum USE_SCENARIO_TYPE {
    /***分享链接验证**/
    LINK = 'link',
    /**系统内验证*/
    SYSTEM = 'system',
}
/***倒计时每秒***/
const COUNT_DOWN_TIME = 1000;
/*需要全局消息的错误码*/ 
const OTP_NOT_BIND_CODE = 120020199;
interface OTPBindingFormProps {
    userId: string;
    type?: USE_SCENARIO_TYPE;
}

interface QRCodeData {
    bindingCode: string;
    qrCodeUrl: string;
    expireTime: number;
}

const OTPBindingForm: React.FC<OTPBindingFormProps> = (
    { userId, type },
    ref,
) => {
    const [form] = Form.useForm();
    const [qrCodeData, setQrCodeData] = useState<QRCodeData | null>(null);
    const [qrCodeLoading, setQrCodeLoading] = useState(false);
    const [isExpired, setIsExpired] = useState(false);
    const [timeLeft, setTimeLeft] = useState(0);
    const [showSecret, setShowSecret] = useState(false);
    const timerRef = useRef<any>(null);
    // Generate QR code on component mount
    useEffect(() => {
        if (userId) {
            generateQRCode();
        }
    }, [userId]);

    // Timer for QR code expiration
    useEffect(() => {
        if (!qrCodeData) return;
        const updateTimer = () => {
            const now = Math.floor(Date.now() / COUNT_DOWN_TIME);
            const remaining = Math.max(0, qrCodeData.expireTime - now);
            setTimeLeft(remaining);
            setIsExpired(remaining <= 0);
        };
        updateTimer();
        timerRef.current && clearInterval(timerRef.current);
        timerRef.current = setInterval(updateTimer, COUNT_DOWN_TIME);
    }, [qrCodeData]);

    const getSecret = () => {
        if (qrCodeData?.qrCodeUrl) {
            // 创建一个 URL 对象
            const url = new URL(qrCodeData.qrCodeUrl);
            // 获取查询参数部分
            const searchParams = new URLSearchParams(url.search);
            return searchParams.get('secret');
        }
        return '';
    };
    const generateQRCode = async () => {
        try {
            setQrCodeLoading(true);
            let otpGenerateCode = otpGenerateQRCode;
            if (type === USE_SCENARIO_TYPE.LINK) {
                otpGenerateCode = otpGenerateQRCodeLink;
            }
            const data = await otpGenerateCode({
                domainName: getDomainName(),
                userId,
            });
            setQrCodeData(data);
            setIsExpired(false);
        } finally {
            setQrCodeLoading(false);
        }
    };

    const handleRefreshQRCode = () => {
        generateQRCode();
    };

    const handleSubmit = async () => {
        const values = await form.validateFields();
        let otpConfirm = otpConfirmBinding;
        if (type === USE_SCENARIO_TYPE.LINK) {
            otpConfirm = otpConfirmBindingLink;
        }
        try {
            await otpConfirm({
                domainName: getDomainName(),
                bindingCode: qrCodeData?.bindingCode,
                otpCode: values.otpCode,
                userId,
            }, { showFailedMessage: false });
            message.success(i18next.t('@i18n:otp.validate.page.operate.success', '操作成功'));
        } catch (error: { code: number; message: string }) {
            if (error?.code === OTP_NOT_BIND_CODE) {
                error?.message && message.error(error?.message);
            } else {
                form.setFields([
                    {
                        name: 'otpCode',
                        errors: [i18next.t('@i18n:otp.validate.page.validate.fail', '动态密码无效，请重试')],
                    },
                ]);
            }
            throw error;
        }
    };
    useImperativeHandle(ref, () => {
        return {
            submit: handleSubmit,
        };
    });

    const renderQRcode = () => {
        return qrCodeData ? (
            <div className="qr-code-wrapper">
                <div className={isExpired ? 'expired' : ''}>
                    <QRCode size={124} value={qrCodeData.qrCodeUrl} />
                </div>
                {isExpired && (
                    <div className="refresh-overlay">
                        <Tooltip title={i18next.t('@i18n:otp.validate.page.refresh', '刷新')}>
                            <Button
                                type="link"
                                shape="circle"
                                icon={<ReloadOutlined />}
                                onClick={handleRefreshQRCode}
                            />
                        </Tooltip>
                    </div>
                )}
            </div>
        ) : null;
    };

    return (
        <div className="otp-binding-form">
            <div className="otp-binding-header">
                <h3>{i18next.t('@i18n:otp.validate.page.qr.code.title', '请打开【第三方 OTP】扫码绑定')}</h3>
            </div>
            <div className="qr-code-section">
                <div className="qr-code-container">
                    {qrCodeLoading ? (
                        <div className="qr-code-loading">
                            <Spin />
                        </div>
                    ) : (
                        renderQRcode()
                    )}
                </div>
            </div>

            <div className="binding-instructions-content">
                <div className="binding-instructions-content-row">
                    {i18next.t(
                        '@i18n:otp.validate.page.validate.describe.one',
                        ' ① 使用第三方 OTP 扫描上面的二维码完成添加。无法扫描？{operate} 并在 OTP 中输入验证密钥完成添加',
                        {
                            operate: (
                                <a
                                    onClick={() => {
                                        setShowSecret(true);
                                    }}
                                >
                                    {i18next.t('@i18n:otp.validate.page.validate.click.open.secret', '请点击查看验证密钥')}
                                </a>
                            ),
                        },
                    )}
                </div>
                {showSecret ? (
                    <div className="binding-instructions-content-row">
                        <StarryAbroadFormItem>
                            <Input
                                addonBefore={i18next.t('@i18n:otp.validate.page.validate.input.addon.before.secret', '验证密钥')}
                                disabled={true}
                                value={getSecret()}
                            />
                        </StarryAbroadFormItem>
                    </div>
                ) : null}
                <div className="binding-instructions-content-row">
                    {i18next.t(
                        '@i18n:otp.validate.page.validate.describe.two',
                        '② 添加后，请把 OTP 中提供的6位验证码填入到以下输入框，即可完成绑定',
                    )}
                </div>
                <div className="binding-instructions-content-row">
                    <Form form={form} layout="vertical">
                        <StarryAbroadFormItem
                            name="otpCode"
                            label={''}
                            rules={[
                                {
                                    required: true,
                                    message: i18next.t(
                                        '@i18n:otp.validate.page.validate.input.required.rule.message',
                                        '请输入动态密码',
                                    ),
                                },
                            ]}
                        >
                            <Input
                                placeholder={i18next.t(
                                    '@i18n:otp.validate.page.validate.input.required.rule.message',
                                    '请输入动态密码',
                                )}
                                maxLength={6}
                                autoComplete="off"
                            />
                        </StarryAbroadFormItem>
                    </Form>
                </div>
            </div>
            <div className="support-info">
                {i18next.t('@i18n:otp.validate.page.otp.support', '支持第三方OTP：Microsoft Authenticator')}
            </div>
        </div>
    );
};

export default forwardRef(OTPBindingForm);
