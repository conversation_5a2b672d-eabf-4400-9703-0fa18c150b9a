import React, { useState, useContext } from 'react';
import { Form, Input, Button, Alert, Space } from '@streamax/poppy';
import i18n from '../../../../../i18n';
import { getAppGlobalData } from '../../../../../global-data';
import context from '../context';
import VerifyCode from './VerifyCode';
import { getVerifyCode, doVerifyCode } from '../../../../../service';
import { illegalCharacter } from '../../../../../utils/validator';
import './Validate.less';

const clsPrefix = 'personal-center_validate';

export default () => {
    const { phoneNumber, areaCode } = getAppGlobalData('APP_USER_INFO') || {};

    const [submitLoading, setSubmitLoading] = useState<boolean>(false);
    const { redirect } = useContext(context);

    const [form] = Form.useForm();
    const handleVerifyCode = (callback: any) => {
        form.validateFields(['phoneNumber']).then(() => {
            getVerifyCode({ phoneNumber, bizType: 2, areaCode }).then(() =>
                callback(),
            );
        });
    };
    const goToInfo = () => {
        redirect('info', i18n.t('name', '个人信息'));
    };
    const handleSubmit = (values: any) => {
        setSubmitLoading(true);
        doVerifyCode({
            ...values,
            bizType: 2,
            phoneNumber,
            areaCode,
        })
            .then(() => {
                redirect('bind', i18n.t('name', '更改手机号'));
            })
            .finally(() => {
                setSubmitLoading(false);
            });
    };
    return (
        <div className={`${clsPrefix}-container`}>
            <Alert
                message={i18n.t('message', '为确保本人操作，请先进行身份校验')}
                type="info"
                showIcon
            />
            <Form
                layout="vertical"
                form={form}
                initialValues={{
                    phoneNumber: `+${areaCode} ${phoneNumber.replace(
                        /(\d{3})\d{4}(\d{4})/,
                        '$1****$2',
                    )}`,
                }}
                style={{ width: '340px' }}
                onFinish={handleSubmit}
            >
                <Form.Item
                    label={i18n.t('name', '手机号')}
                    name="phoneNumber"
                    rules={[{ required: true }]}
                >
                    <Input disabled />
                </Form.Item>
                <Form.Item
                    label={i18n.t('name', '验证码')}
                    name="verifyCode"
                    rules={[
                        { required: true },
                        { validator: illegalCharacter },
                    ]}
                >
                    <Space>
                        <Input
                            placeholder={i18n.t('message', '请输入验证码')}
                            maxLength={6}
                        />
                        <VerifyCode onClick={handleVerifyCode} />
                    </Space>
                </Form.Item>
                <Form.Item>
                    <Space>
                        <Button onClick={goToInfo}>
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            {i18n.t('action', '下一步')}
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </div>
    );
};
