import { useState, useEffect, useMemo } from 'react';
import i18n from '../../../../i18n';
import { getAppGlobalData } from '../../../../global-data';

import './style.scoped.less';
import { InfoBack } from '@/runtime-lib';
import { fetchLicensePointInfo } from '@/service/system';
import { useRequest } from '@streamax/hooks';
import { message, Spin } from '@streamax/poppy';

/**
 * 客户环境（App）要求配置开关
 */
enum EnumAppSwitch {
    /**
     * 开启
     */
    on = 1,
    /**
     * 关闭
     */
    off = 0,
};

/**
 * 客户环境（Pc）要求配置开关
 */
enum EnumPcSwitch {
    /**
     * 开启
     */
    on = 1,
    /**
     * 关闭
     */
    off = 0,
};

/**
 * 客户环境（Pc）要求配置开关
 */
enum EnumSystemRequire {
    /**
     * 有要求
     */
    on = 1,
    /**
     * 无要求
     */
    off = 0,
};

/**
 * 客户环境（Pc）要求配置类型
 */
enum EnumPcRequireType {
    /**
     * 通用
     */
    general = 1,
    /**
     * 动态
     */
    dynamic = 2,
};

interface IEnvironmentConfig {
    /*
     * app环境要求配置开关
     */
    "appSwitch": 1 | 0,
    /**
     * app环境要求配置
     */ 
    "appConfig": {
        /**
         * IOS最低版本
         */
        "iosVersion": string
        /**
         * android最低版本
         */
        "androidVersion": string
        /**
         * "架构支持"
         */
        "framework": string
    },
    /**
     * pc环境要求配置开关
     */
    "pcSwitch":  0 | 1,
    /**
     * pc环境要求配置
     */
    "pcConfig": {
        "configType": 1 | 2, 
        "hardwareConfig": [{
            "vehicleNum": [number, number | null] ,
            "cpu": string,
            "memory": string,
            "graphicsCard": string,
            "graphicsCardResolution": string,
            "networkConfig": [
                {
                /**
                 * 通道数量要求
                 */
                "channelNum": number,
                /**
                 * 带宽要求，单位Mbps
                 */
                "bandwidth": number
                }
            ],
            /**
             * windows最低版本要求，0-无要求 1-有要求
             */
            "windowsConfig": number, 
            /**
             * windows最低版本
             */
            "windowsVersion": string,
            /**
             * mac最低版本要求，0-无要求 1-有要求
             */
            "macConfig": 0, 
            /**
             * mac最低版本
             */
            "macVersion": string,
            /**
             * 浏览器类型
             */
            "browserType": string,
            /**
             * 浏览器最低版本
             */
            "browserVersion": string
        }]
    }
}

const clsPrefix = 'client-config-require';

export default () => {
    const { data, loading, error } = useRequest(fetchLicensePointInfo);
    const { pointConfig: _pointConfig } = data || {};
    const pointConfig = useMemo(() => {
        // _pointConfig为json序列化后的字符串
        if (_pointConfig as string) {
            try {
                return JSON.parse(_pointConfig);
            } catch (error) {
                console.error('JSON.parse resolve pointConfig fail', error, _pointConfig);
            }
        }
        return {};
    }, [_pointConfig]);
    const environmentConfig: IEnvironmentConfig = pointConfig.environmentConfig || {};
    const { appSwitch, pcSwitch, appConfig, pcConfig } = environmentConfig;
    
    // 整体是否可见
    // 这里使用双等号做枚举判定，是为了确保后端返回的数据类型为string和number均能判定通过
    const visible = appSwitch == EnumAppSwitch.on || pcSwitch == EnumPcSwitch.on;
    useEffect(() => {
        if (error) {
            message.error(error);
        }
    }, [error]);

    if (loading) {
        return <div className={clsPrefix}>
            <Spin spinning={loading} />
        </div>
    }
    if (!visible) {
        return null;
    };
    
    return (
        <div className={clsPrefix}>
            <div className={`${clsPrefix}-title`}>{i18n.t('name', '运行环境要求')}</div>
            <InfoBack title={i18n.t('name', '低于要求的环境配置可能导致平台性能下降或功能异常，为保证平台稳定运行及流畅体验，请确保您的设备满足以下条件')} />
            {
                // 这里使用双等号做枚举判定，是为了确保后端返回的数据类型为string和number均能判定通过
                appSwitch == EnumAppSwitch.on && (
                    <div className={`${clsPrefix}-section`}>
                        <div className={`${clsPrefix}-section-title`}>{i18n.t('name', 'APP环境要求')}</div>
                        <div className={`${clsPrefix}-section-row`}>
                            <div className={`${clsPrefix}-container`}>
                                <div className={`${clsPrefix}-item`}>
                                    <div className={`${clsPrefix}-item-title`}>
                                        <span className={`${clsPrefix}-item-title-value`}>
                                            {i18n.t('name', 'iOS设备')}
                                        </span>
                                    </div>
                                    <div className={`${clsPrefix}-item-row`}>
                                        <span className={`${clsPrefix}-item-row-label`}>
                                            {i18n.t('name', 'iOS最低版本')}
                                        </span>
                                        :
                                        <span className={`${clsPrefix}-item-row-value`}>{appConfig?.iosVersion}</span>
                                    </div>
                                </div>
                                <div className={`${clsPrefix}-item`}>
                                    <div className={`${clsPrefix}-item-title`}>
                                        <span className={`${clsPrefix}-item-title-value`}>
                                            {i18n.t('name', 'Android设备')}
                                        </span>
                                    </div>
                                    <div className={`${clsPrefix}-item-row`}>
                                        <span className={`${clsPrefix}-item-row-label`}>
                                            {i18n.t('name', 'Android最低版本')}
                                        </span>
                                        :
                                        <span className={`${clsPrefix}-item-row-value`}>{appConfig?.androidVersion}</span>
                                    </div>
                                    <div className={`${clsPrefix}-item-row`}>
                                        <span className={`${clsPrefix}-item-row-label`}>
                                            {i18n.t('name', '架构支持')}
                                        </span>
                                        :
                                        <span className={`${clsPrefix}-item-row-value`}>{appConfig?.framework}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }
            {
                // 这里使用双等号做枚举判定，是为了确保后端返回的数据类型为string和number均能判定通过
                pcSwitch == EnumPcSwitch.on && (
                    <div className={`${clsPrefix}-section`}>
                        <div className={`${clsPrefix}-section-title`}>{i18n.t('name', 'PC端环境要求')}</div>
                        {
                            pcConfig?.hardwareConfig.map((config) => (
                                <div className={`${clsPrefix}-section-group`} key={config.vehicleNum?.join('-')}>
                                    {
                                        pcConfig?.configType == EnumPcRequireType.dynamic && (
                                            <div className={`${clsPrefix}-section-info`}>
                                                {
                                                    i18n.t('name', '当您管理的车辆数量在{range}范围内，其中N为车辆数，运行环境最低要求如下', {
                                                        range: !!config.vehicleNum[1]
                                                            ? `${config.vehicleNum[0]} < N ≤ ${config.vehicleNum[1]}` 
                                                            : `N > ${config.vehicleNum[0]}`
                                                    })
                                                }
                                            </div>
                                        )
                                    }
                                    <div className={`${clsPrefix}-section-row`}>
                                        <div className={`${clsPrefix}-container`}>
                                            <div className={`${clsPrefix}-item`}>
                                                <div className={`${clsPrefix}-item-title`}>
                                                    <span className={`${clsPrefix}-item-title-value`}>
                                                        {i18n.t('name', '硬件配置')}
                                                    </span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', 'CPU(或同级CPU)')}
                                                    </span>
                                                    {/* 这里冒号单独写，是为了label区域出现换行时，冒号仍旧保留在第一行右侧的效果 */}
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>{config.cpu}</span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', '内存')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>{config.memory}</span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', '显卡')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>{config.graphicsCard}</span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', '显示屏分辨率')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>{config.graphicsCardResolution}</span>
                                                </div>
                                            </div>
                                            <div className={`${clsPrefix}-item`}>
                                                <div className={`${clsPrefix}-item-title`}>
                                                    <span className={`${clsPrefix}-item-title-value`}>
                                                        {i18n.t('name', '网络配置')}
                                                    </span>
                                                </div>
                                                {
                                                    config.networkConfig.map((netConfig, index) => (
                                                        <div className={`${clsPrefix}-item-row`} key={netConfig.channelNum}>
                                                            <span className={`${clsPrefix}-item-row-label`}>
                                                                {i18n.t('name', '带宽(独享)')}
                                                            </span>
                                                            :
                                                            <span className={`${clsPrefix}-item-row-value`}>
                                                                {
                                                                    `${netConfig.bandwidth} Mbps (
                                                                        ${i18n.t('name', '支持{channelNum}路直通', 
                                                                        { channelNum: netConfig.channelNum })}
                                                                    )`
                                                                }
                                                            </span>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className={`${clsPrefix}-section-row`}>
                                        <div className={`${clsPrefix}-container`}>
                                            <div className={`${clsPrefix}-item`}>
                                                <div className={`${clsPrefix}-item-title`}>
                                                    <span className={`${clsPrefix}-item-title-value`}>
                                                        {i18n.t('name', '操作系统与软件配置')}
                                                    </span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', 'Windows最低版本')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>
                                                        {
                                                            // 这里使用双等号做枚举判定，是为了确保后端返回的数据类型为string和number均能判定通过
                                                            config.windowsConfig == EnumSystemRequire.on ? config.windowsVersion : i18n.t('name', '无要求')
                                                        }
                                                    </span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', 'macOS最低版本')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>
                                                        {
                                                            // 这里使用双等号做枚举判定，是为了确保后端返回的数据类型为string和number均能判定通过
                                                            config.macConfig == EnumSystemRequire.on ? config.macVersion : i18n.t('name', '无要求')
                                                        }
                                                    </span>
                                                </div>
                                                <div className={`${clsPrefix}-item-row`}>
                                                    <span className={`${clsPrefix}-item-row-label`}>
                                                        {i18n.t('name', '浏览器最低版本')}
                                                    </span>
                                                    :
                                                    <span className={`${clsPrefix}-item-row-value`}>
                                                        {`${config.browserType}-${config.browserVersion}`}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                )
            }
        </div>
    );
};
