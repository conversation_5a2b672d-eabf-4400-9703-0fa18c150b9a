import { request } from '../../../../request';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;
//批量删除主任务
export const deleteTaskMainBatch = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/imexport/task/batch/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export const gssFetchFileDownloadUrl = async (params?: any, headers?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/gss/v1/file/download/url',
        params: { validTime: 7, ...params },
        headers,
    });
    if (!success || code != successCode) {
        throw message;
    }
    return data;
};

// 应用分页查询
export const fetchApplicationPageList = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/application/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询导出模型类型
export const fetchExportModelType = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/task/imexport/type/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
