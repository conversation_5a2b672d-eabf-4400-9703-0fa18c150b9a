/*
 * @LastEditTime: 2022-09-14 10:35:42
 */
const taskTypesStr = `
    7-词条导出-true
    12-国际化-菜单/页面/操作-导出-true
    14-国际化-应用-导出-true
    16-国际化-角色-导出-true
    18-国际化-报警等级-导出-true
    20-国际化-报警分类-导出-true
    22-国际化-策略-导出-true
    24-国际化-基础协议-导出-true
    26-国际化-租户协议-导出-true
    28-国际化-报警类型-导出-true
    30-国际化-主题颜色-导出-true
    33-登陆日志-true
    34-设备轨迹-true
    37-国际化-服务管理-导出-true
    38-国际化-车辆状态-导出-true
    40-国际化-车辆状态配置-导出-true
    42-国际化-车辆状态配置项配置-导出-true
    42-国际化-车辆状态配置配置项-导出-true
    201-国际化-车辆-导出-true
    202-国际化-车队-导出-true
    203-国际化-司机-导出-true
    204-国际化-用户-导出-true
    401-[货运]GPS详情-导出-true
    402-[货运]最新GPS-导出-true
    403-[货运]最新车辆状态-导出-true
    31-ft-true
    35-ft-true
    47-ft-true
    48-ft-true
    404-ft-true
    405-ft-true
    406-ft-true
    407-ft-true
    205-证据视频-true
    210-导出264-true
    50-国际化-标签导出-true
    28-国际化-报警类型导出-true
    502-rt-true
    504-rt-true
    506-rt-true
    507-rt-true
    651-lsad-车辆导出-true
    653-lsad-车组导出-true
    654-lsad-app用户导出-true
`;
// 导出类型标志（为true的是要导出的）
export const taskTypes = taskTypesStr
    .split(/[\t\n ]+/)
    .filter((i) => i.endsWith('-true'))
    .map((i) => i.split('-')[0])
    .join(',');


/**
 * 任务类型：1-导入任务；2-导出任务
 */
export enum ImportExportTaskType {
    IMPORT = 1,
    EXPORT = 2,
}
