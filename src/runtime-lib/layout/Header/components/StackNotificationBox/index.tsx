import { useEffect } from 'react';
import { OpenPersonalCenter } from '@/runtime-lib/layout/Header/PersonalCenter';
import { render } from 'react-dom';
import StackBox from './StackBox';
import { useDataStore } from '@/runtime-lib/MessageModule/MessageNotification/hooks/useDataStore';

type StackNotificationBoxProps = {
    //打开个人中心
    openPersonalCenter: OpenPersonalCenter;
};
const StackNotificationBox = (props: StackNotificationBoxProps) => {
    const { openPersonalCenter } = props;
    const resetStore = useDataStore((state) => state.resetStore)
    window.openPersonalCenter = openPersonalCenter;
    useEffect(() => {
        //挂载到body，避免服务切换时销毁
        if (!document.querySelector('#stack')) {
            resetStore();
            const box = document.createElement('div');
            box.id = 'stack';
            document.querySelector('body')?.appendChild(box);
            render(<StackBox />, document.querySelector('#stack'));
        }
    }, []);

    return <></>;
};
export default StackNotificationBox;
