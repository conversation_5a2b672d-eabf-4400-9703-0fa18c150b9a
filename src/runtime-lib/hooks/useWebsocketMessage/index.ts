/*
 * @LastEditTime: 2024-05-21 09:55:33
 */
import { useState, useEffect, useRef } from 'react';
import websocketMessage from './controller';
import type {
    BaseWebsocketInstance,
    IBaseWebsocket,
    EventMap,
    SubscribeResult,
    CustomSendType,
    SendOtherConfig,
} from './type';
import { B_MESSAGE_NOTICE, B_SYSTEM_NOTICE } from './constant';
import useWebSocketReadyStateStore from './useWebSocketReadyStateStore';

type AddEvent = <T extends keyof EventMap>(
    name: T,
    callBack: EventMap[T],
    config?: {
        destroyAutoRemove?: boolean;
    },
) => void;

type Subscribe = (
    topic: string,
    callBack: (data?: any) => void,
    config?: {
        destroyAutoRemove?: boolean;
        customSend?: CustomSendType;
        retryTimes?: number; // 重试次数
        pushDelayTime?: number;
    },
) => Promise<SubscribeResult>;

interface UseWebsocketMessage extends Omit<BaseWebsocketInstance, 'init' | 'close'> {
    addEvent: AddEvent;
    subscribe: Subscribe;
    readyState: WebSocket['readyState'] | undefined;
}

const useWebsocketMessage: () => UseWebsocketMessage = () => {
    const wsm = websocketMessage();
    const [readyState, setReadyState] = useState<WebSocket['readyState']>();

    const autoRemoveEventMap = useRef<IBaseWebsocket['eventMap']>({});
    const autoUnsubscribeMap = useRef<IBaseWebsocket['subscribeMap']>({});

    useEffect(() => {
        const updateReadyState = () => setReadyState(wsm.getState());
        wsm.addEvent('onClose', updateReadyState);
        wsm.addEvent('onOpen', updateReadyState);
        wsm.addEvent('onError', updateReadyState);
        return () => {
            wsm?.removeEvent('onClose', updateReadyState);
            wsm?.removeEvent('onOpen', updateReadyState);
            wsm?.removeEvent('onError', updateReadyState);
            // 移除所有的监听事件
            for (const key in autoRemoveEventMap.current) {
                // @ts-ignore
                (autoRemoveEventMap.current[key] || []).forEach((callBack) =>
                    // @ts-ignore
                    wsm.removeEvent(key, callBack),
                );
            }
            // 取消订阅
            for (const topic in autoUnsubscribeMap.current) {
                // @ts-ignore
                (autoUnsubscribeMap.current[topic] || []).forEach((callBack) => {
                    // 系统公告类，不取消订阅
                    if (topic !== B_SYSTEM_NOTICE) {
                        wsm.unsubscribe(topic, callBack);
                    }
                });
            }
        };
    }, []);

    useEffect(() => {
        setReadyState(wsm?.getState());
    }, [wsm?.getState()]);

    const _addEvent: AddEvent = (name, callBack, config) => {
        const { destroyAutoRemove = true } = config || {};
        if (destroyAutoRemove) {
            if (!autoRemoveEventMap.current[name]) autoRemoveEventMap.current[name] = [];
            autoRemoveEventMap.current[name]?.push(callBack);
        }

        wsm?.addEvent(name, callBack);
    };

    const _subscribe: Subscribe = (topic, callBack, config): Promise<SubscribeResult> => {
        const { destroyAutoRemove = true, ...otherConfig } = config || {};
        if (destroyAutoRemove) {
            if (!autoUnsubscribeMap.current[topic]) autoUnsubscribeMap.current[topic] = [];
            autoUnsubscribeMap.current[topic]?.push(callBack);
        }
        return wsm?.subscribe(topic, callBack, otherConfig);
    };

    return {
        readyState,
        send: (message: any[], callback: (res: any) => void, otherConfig: SendOtherConfig = {}) =>
            wsm?.send(message, callback, otherConfig),
        subscribe: _subscribe,
        unsubscribe: (topic, callBack, otherConfig) =>
            wsm?.unsubscribe(topic, callBack, otherConfig),
        addEvent: _addEvent,
        removeEvent: (name, callBack) => wsm?.removeEvent(name, callBack),
        getState: () => wsm?.getState(),
    };
};
export {
    websocketMessage,
    useWebSocketReadyStateStore,
    BaseWebsocketInstance
}; 
export default useWebsocketMessage;
