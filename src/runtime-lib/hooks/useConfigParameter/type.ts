/*
 * @LastEditTime: 2024-11-14 19:26:43
 */
/**
 * 系统参数
 */
export enum SystemParamsEnum {
	
}

/**
 * 租户参数
 */
export enum TenantParamsEnum {
	/**
	 * dark模式控制是否展示
	 */
	THEME_STYLE_DARK= "THEME.STYLE.DARK",
	MENU_ACTIVE_TYPE='MENU.ACTIVE.TYPE',
    PERSON_PHONE_SHOW='PERSON.PHONE.SHOW',
	PLAYBACK_BALCKBOX_PLAY_MODE='PLAYBACK.BALCKBOX.PLAY.MODE',
	PLAYBACK_CHANNEL_VIDEO_DISPLAY_MODE_CONFIG='PLAYBACK.CHANNEL.VIDEO.DISPLAY.MODE.CONFIG',
    HEADER_TAB_DOWNLOAD = 'HEADER.TAB.DOWNLOAD',
}


/**
 * 参数类型
 */
export type ParameterType = `${SystemParamsEnum}` & `${TenantParamsEnum}`
