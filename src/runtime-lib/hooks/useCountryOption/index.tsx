import { useEffect, useState } from 'react';
import { queryTimezonePage } from '../../service/activation-process';
import i18n from '../../i18n';
import { TIME_ZONE_UTC_OPTIONS } from '../../utils/constant';

import { arrayToTree } from './../../utils/general';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

export type TimeZoneItem = {
    areaId: string;
    areaName: string;
    areaCode: string;
    areaTimeZone: number;
    level: number;
    parentCode?: string | null;
    parentName?: string | null;
    summerTimeState: number;
    enableState: number;
    createUser: string;
    createTime: number;
    updateUser: string;
    updateTime: number;
    value: string;
    label: string;
    path: string[];
    utc: string;
};
// 获取国家地区配置
export const useCountryOption = () => {
    const [countryTree, setCountryTree] = useState<TimeZoneItem[]>([]);
    const [countryOption, setCountryOption] = useState<TimeZoneItem[]>([]);
    const [countryMap, setCountryMap] = useState<Map<string, TimeZoneItem>>(
        new Map(),
    );

    const countryUtc = (countryItem: any) => {
        return `${i18n.t(
            `@i18n:@areaTimeZone__${countryItem.areaCode}`,
            countryItem.areaName,
        )} (${
            TIME_ZONE_UTC_OPTIONS.find(
                (timeZone: any) => timeZone.value == countryItem.areaTimeZone,
            )?.label || ''
        })`;
    };

    const onlyCountryUtc = (countryItem: TimeZoneItem) => {
        return `${
            TIME_ZONE_UTC_OPTIONS.find(
                (timeZone: any) => timeZone.value == countryItem.areaTimeZone,
            )?.label
        }`;
    };
    useEffect(() => {
        // 获取国家地区配置
        queryTimezonePage({
            page: 1,
            pageSize: 1e6,
            complexSort: 'orderBy areaName asc',
        }).then((res) => {
            const options = buildPaths(res.list);
            const enableStateData = options.filter(
                (item: TimeZoneItem) => item.enableState,
            );
            const dataMap = new Map(
                enableStateData.map((node, index) => [
                    node.areaCode,
                    { ...node, index },
                ]),
            );
            const treeData = removeEmptyChildren(
                arrayToTree(enableStateData, {
                    idFieldName: 'areaCode',
                    parentIdFieldName: 'parentCode',
                    childrenKey: 'children',
                    order: 'areaTimeZone',
                    sort: 'asc',
                }),
            );
            setCountryMap(dataMap);
            setCountryOption(enableStateData);
            setCountryTree(treeData);
        });
    }, []);

    const buildPaths = (nodes: TimeZoneItem[]) => {
        // 存储所有节点及其索引
        const nodesMap = new Map(
            nodes.map((node, index) => [node.areaCode, { ...node, index }]),
        );
        // 递归函数，为每个节点构建路径
        function buildPath(node: TimeZoneItem, currentPath: string[] = []) {
            // 如果当前节点有父节点，获取父节点的路径
            if (node.parentCode && nodesMap.has(node.parentCode)) {
                const parent = nodesMap.get(node.parentCode);
                buildPath(parent, currentPath);
            }
            // 将当前节点的 code 添加到路径数组中
            currentPath.push(node.areaCode);
            node.path = [...currentPath];
        }

        // 遍历所有节点，为每个节点构建路径
        nodes.forEach((node) => {
            const translateName = `${i18n.t(
                `@i18n:@areaTimeZone__${node.areaCode}`,
                node.areaName,
            )}`;
            node.value = node.areaCode;
            node.label = translateName;
            node.utc = `${translateName} (${onlyCountryUtc(node)})`;
            buildPath(node, []);
        });
        return nodes;
    };

    const filter = (inputValue: string, path: []) => {
        return path.some(
            (option) =>
                (option?.label as string)
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) > -1,
        );
    };
    const displayRender = (labels: string[], selectedOptions: any[]) => {
        if (selectedOptions.filter((item) => item).length) {
            const labelString = labels?.map((label, i) => {
                const option = selectedOptions[i];
                if (i === labels.length - 1) {
                    return <span key={option.value}>{option.utc}</span>;
                }
                return <span key={option.value}>{label} / </span>;
            });
            return (
                <OverflowEllipsisContainer>
                    {labelString}
                </OverflowEllipsisContainer>
            );
        }
        return null;
    };
    const render = (inputValue, path) => {
        // 高亮显示匹配的文本
        const highlightText = (text: string) => {
            const parts = [];
            let lastIndex = 0;
            const lowerInputValue = inputValue.toLowerCase();
            const lowerText = text.toLowerCase();
            let regex = new RegExp('', 'gi');
            if (lowerInputValue?.indexOf('+') === 0) {
                regex = new RegExp(`(\\${lowerInputValue})`, 'gi');
            } else {
                regex = new RegExp(`(${lowerInputValue})`, 'gi');
            }
            let match;
            while ((match = regex.exec(lowerText)) !== null) {
                // 添加匹配项之前的文本
                parts.push(text.slice(lastIndex, match.index));
                // 添加匹配项
                parts.push(
                    <span
                        style={{ color: 'red' }}
                        key={`highlight-${parts.length}`}
                    >
                        {text.slice(
                            match.index,
                            match.index + inputValue.length,
                        )}
                    </span>,
                );
                lastIndex = regex.lastIndex;
            }

            if (lastIndex < text.length) {
                parts.push(text.slice(lastIndex));
            }
            return <>{parts}</>;
        };

        const itemLabels = path.map((node, index) => {
            const highlightedLabel = highlightText(node.label);
            const labeHasSlash =
                index < path.length - 1 ? (
                    <>
                        {highlightedLabel}
                        <span key={`slash-${index}`}> / </span>
                    </>
                ) : (
                    <>
                        {highlightedLabel}
                        {/* 因下拉选择框最下级地区渲染需要，label已经包含时区，故此过滤最下级地区 */}
                        <span key={`slash-${index}-utc`}>
                            {node.label !== node.utc &&
                                `(${onlyCountryUtc(node)})`}
                        </span>
                    </>
                );
            return labeHasSlash;
        });
        return (
            <OverflowEllipsisContainer>{itemLabels}</OverflowEllipsisContainer>
        );
    };

    // 去除空的children
    const removeEmptyChildren = (options: any[]) => {
        const newOptions: any = options.map((option) => {
            const { children = [], utc = '', ...rest } = { ...option };
            if (Array.isArray(children)) {
                if (children.length === 0) {
                    return {
                        ...rest,
                        label: utc, // 最后一级的label展示加上时区
                        utc,
                    };
                }
                return {
                    ...rest,
                    children: removeEmptyChildren(children),
                    utc,
                };
            }
            return option;
        });
        return newOptions;
    };

    return {
        countryOption,
        countryUtc,
        removeEmptyChildren,
        countryTree,
        filter,
        render,
        displayRender,
    };
};
export default useCountryOption;
