import moment, { Moment } from 'moment';
import { getAppGlobalData } from '../../global-data';
import { MILEAGE_UNIT_OPTIONS, TEMPERATURE_UNIT_OPTIONS } from '../../utils/constant';
import { getSummerTimeOffsetByTime } from '../../utils/formator';

export const getUserGlobalConfig = () => {
    // 获取用户数据
    const {
        mapType = 'GMapEN',
        mileageUnit = '1',
        temperatureUnit = '1',
        areaCode,
    } = getAppGlobalData('APP_USER_CONFIG') || {};
    const { timeZone, summerTimeOffset, timeFormat } = getAppGlobalData('APP_USER_CONFIG');

    const lang = getAppGlobalData('APP_LANG') || 'zh_CN';
    // 设置bar的里程单位
    const unit = MILEAGE_UNIT_OPTIONS.find((i) => i.value == mileageUnit);
    // 设置bar的温度单位
    const tempUnit = TEMPERATURE_UNIT_OPTIONS.find((i) => i.value == temperatureUnit);

    // 处理时区
    // 时区偏移
    const now = moment();

    const isDst = getSummerTimeOffsetByTime(now);
    // 时区偏移量，传递给地图组件的是秒级的偏移量
    // 需处理夏令时
    const offset = isDst ? timeZone + summerTimeOffset * 60 : timeZone;
    return {
        mapType,
        unit,
        tempUnit,
        lang,
        timeZone,
        summerTimeOffset,
        timeFormat,
        offset,
        country: areaCode.slice(0, 2),
    };
};
