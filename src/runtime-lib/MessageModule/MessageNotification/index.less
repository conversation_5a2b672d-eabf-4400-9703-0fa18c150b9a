@import '../../styles/themeStyle.less';
.message-notification-modal {
    .poppy-notification-notice-message {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: keep-all;
        overflow-wrap: break-word;
    }
    .poppy-notification-notice-description {
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;

        display: -moz-box;
        -moz-line-clamp: 2;
        -moz-box-orient: vertical;

        overflow-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        overflow: hidden;
    }
}
.notification-notice-icon {
    color: @text-color-theme-style-8;
}
.poppy-notification-notice-close:hover {
    color: var(--poppy-icon-color-hover) !important;
}