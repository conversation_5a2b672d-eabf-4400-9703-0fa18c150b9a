import { Labels } from "../../type";
import {
    getSpeedFormat,
    zeroTimeStampToFormatTime,
} from '../../../../utils/formator';
import { getUnitName } from '../../utils';


// 报警类型的模板标签
export const labels: Labels = [
    {
        name: 'alarmTypeName',
        value: '{{alarmTypeName}}',
    },
    {
        name: 'vehicleNo',
        value: '{{vehicleNo}}',
    },
    {
        name: 'driverName',
        value: '{{driverName}}',
    },
    {
        name: 'deviceNo',
        value: '{{deviceNo}}',
    },
    {
        name: 'alarmTime',
        value: '{{alarmTime}}',
        format: (text: any) => {
            return zeroTimeStampToFormatTime(text) || '-';
        },
    },
    {
        name: 'alarmSpeed',
        value: '{{alarmSpeed}}',
        format: (text: any) => {
            return getSpeedFormat(text / 10) + getUnitName();
        },
    },
];


// 是否展示详情按钮
export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};