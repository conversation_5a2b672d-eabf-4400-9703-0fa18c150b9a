import DefaultMessageItem from "../../DefaultMessageItem";
import BtnDetail from "../../components/BtnDetail";
import { MessageInfo, MessageItemProps } from "../../type";
import { getBtn, getMessage, getMessageContent } from './utils';
import { getFormatMessage, getFormatDescription } from '../../utils';
import { useHistory } from '@base-app/runtime-lib/core';

export default (props: MessageItemProps) => {   
    const {
        messageInfo, 
    } = props;
    const {
        modelData,
    }  = messageInfo;
    const history = useHistory();
    
    // 获取类型对应的标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);

    // 查看详情按钮
    const getMessageItemBtnDetail = (messageInfo: MessageInfo, pageConfig: any) => {
        const btn = getBtn(modelData, pageConfig, history);
        return <BtnDetail btn={btn} messageInfo={messageInfo} />;
    };

    return (
        <DefaultMessageItem 
            showBtn={true}
            getBtnDetail={getMessageItemBtnDetail}
            getMessage={() => getFormatMessage(message, undefined, modelData)}
            getDescription={() => getFormatDescription(content, undefined, modelData)}
            messageInfo={messageInfo}
        />
    )
}