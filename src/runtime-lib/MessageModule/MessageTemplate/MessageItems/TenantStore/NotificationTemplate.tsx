import DefaultNotificationRender from "../../DefaultNotificationRender";
import { NotificationTemplateProps } from "../../type";
import { getFormatMessage, getFormatDescription } from '../../utils';
import { getMessage, getMessageContent } from './utils';
export default (props: NotificationTemplateProps) => {
    const {
        messageInfo, 
        prefixCls
    } = props;
    const {
        modelData
    }  = messageInfo;
    // 获取类型对应的标题和内容
    const message = getMessage();
    const content = getMessageContent();

    // 获取格式化后的类型标题和内容
    const formatMessage = getFormatMessage(message, undefined, modelData);
    const formatDescription = getFormatDescription(content, undefined, modelData);

    return (
        <DefaultNotificationRender
            type="warning"
            message={formatMessage}
            description={formatDescription}
            prefixCls={prefixCls}
        />
    );
}