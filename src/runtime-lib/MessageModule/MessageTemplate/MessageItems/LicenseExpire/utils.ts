import i18n from '../../../../i18n';
import { MessageInfo, ModalData } from '../../type';
import {
    timestampToZeroTimeStamp,
} from '../../../../utils/formator';
import moment from 'moment';
import { JumpOptions, jumpPage } from '../../utils';
import { getAppGlobalData } from '../../../../global-data';


export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId } = messageInfo;
    return i18n.t(titleId, i18n.t('message', '车辆License到期通知'));
};

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId } = messageInfo;
    return i18n.t(contentId, '');
};


export const getBtn = (modelData: ModalData, pageConfig?: any, history?: any) => {
    return {
        text: i18n.t('action', '查看详情'),
        onclick: () => {
            const noticeRemainDays = modelData.noticeRemainDays || 30;
            const expireStartTime = timestampToZeroTimeStamp(moment().startOf('day'));
            const expireEndTime = timestampToZeroTimeStamp(
                moment().subtract(-noticeRemainDays, 'days').endOf('day'),
            );
            const jumpOptions: JumpOptions = {
                pageType: 'licenseBind',
                appId: modelData?.appId  ?? getAppGlobalData('APP_ID'),
                suffixPath: `?expireStartTime=${expireStartTime}&expireEndTime=${expireEndTime}`,
            };
            jumpPage(jumpOptions, pageConfig, history);
        },
    };
}