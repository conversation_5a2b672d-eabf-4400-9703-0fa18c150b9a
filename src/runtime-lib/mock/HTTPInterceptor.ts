import {
    AxiosAdapter,
    AxiosInstance,
    AxiosRequestConfig,
    AxiosResponse,
} from 'axios';
import { mockConfig } from './config';
import { HttpInterceptors } from './types';

export class HTTPInterceptor {
    private static instance: HTTPInterceptor;
    private originalAdapter: AxiosAdapter | undefined;
    private axiosInstance: AxiosInstance | null = null;

    private constructor() {}

    static getInstance(): HTTPInterceptor {
        if (!HTTPInterceptor.instance) {
            HTTPInterceptor.instance = new HTTPInterceptor();
        }
        return HTTPInterceptor.instance;
    }

    /**
     * 查找匹配的拦截器配置
     */
    private findMatchingInterceptor(
        axiosConfig: AxiosRequestConfig,
    ): { interceptors: HttpInterceptors; value: string } | null {
        const currentConfig = mockConfig.getConfig();
        const configDefs = mockConfig.getConfigDefs();
        if (!currentConfig.enabled) return null;

        // 遍历所有开启的页面配置
        for (const page of currentConfig.pages) {
            if (!page.enabled) continue;

            // 获取页面对应的配置定义
            const pageDef = configDefs.find(
                (def) => def.pageName === page.pageName,
            );
            if (!pageDef) continue;

            // 遍历页面下所有开启的HTTP类型配置
            for (const runtimeConfig of page.configs) {
                if (!runtimeConfig.enabled || runtimeConfig.type !== 'HTTP')
                    continue;

                // 从配置定义中获取对应的配置项定义
                const configDef = pageDef.configs.find(
                    (def) => def.name === runtimeConfig.name,
                );
                if (!configDef?.interceptors?.match) continue;

                // 使用定义中的match方法进行匹配
                if (
                    configDef.interceptors.match(
                        axiosConfig,
                        runtimeConfig.value,
                    )
                ) {
                    return {
                        interceptors: configDef.interceptors,
                        value: runtimeConfig.value,
                    };
                }
            }
        }

        return null;
    }

    /**
     * 处理请求拦截
     */
    private async handleRequest(config: AxiosRequestConfig) {
        const match = this.findMatchingInterceptor(config);
        if (match?.interceptors.reqInterceptor) {
            config = await match.interceptors.reqInterceptor(
                config,
                match.value,
            );
        }
        return config;
    }

    /**
     * 处理响应拦截
     */
    private async handleResponse(response: AxiosResponse) {
        const match = this.findMatchingInterceptor(response.config);
        if (match?.interceptors.resInterceptor) {
            const result = await match.interceptors.resInterceptor(
                {
                    data: response.data,
                    res: response,
                },
                match.value,
            );
            return result.res || { ...response, data: result.data };
        }
        return response;
    }

    /**
     * 创建Mock Adapter
     */
    private createMockAdapter(originalAdapter: AxiosAdapter): AxiosAdapter {
        return async (config: AxiosRequestConfig) => {
            // 1. 执行请求拦截
            config = await this.handleRequest(config);

            // 2. 调用原始adapter
            const response = await originalAdapter(config);

            // 3. 执行响应拦截
            return this.handleResponse(response);
        };
    }

    /**
     * 添加Mock拦截器
     */
    addMockInterceptors(axiosInstance: AxiosInstance) {
        // 避免重复添加
        if (this.axiosInstance === axiosInstance && this.originalAdapter) {
            return;
        }

        // 保存原始adapter和实例引用
        this.originalAdapter = axiosInstance.defaults.adapter;
        this.axiosInstance = axiosInstance;

        // 设置新的adapter
        if (this.originalAdapter) {
            axiosInstance.defaults.adapter = this.createMockAdapter(
                this.originalAdapter,
            );
        }
    }

    /**
     * 移除Mock拦截器
     */
    removeMockInterceptors() {
        if (!this.axiosInstance || !this.originalAdapter) return;

        // 恢复原始adapter
        this.axiosInstance.defaults.adapter = this.originalAdapter;

        // 清理引用
        this.originalAdapter = undefined;
        this.axiosInstance = null;
    }
}

export const httpInterceptor = HTTPInterceptor.getInstance();
