/**
 * 基础配置类型
 * 允许任意字符串键的配置对象
 */
interface BaseConfigType {
  [key: string]: any;
}

export class BaseConfig<T extends BaseConfigType> {
  /** 单例实例 */
  private static instance: BaseConfig<any> | null = null;
  /** 按 appId 分类存储多个配置 */
  private appConfigs: Map<number, Map<keyof T, any>>;

  /**
   * 私有构造函数，防止外部通过 `new` 创建实例
   */
  private constructor() {
    this.appConfigs = new Map<number, Map<keyof T, any>>();
  }

  /**
   * 获取配置管理器的单例实例
   * @returns BaseConfig实例
   */
  public static getInstance<U extends BaseConfigType>(): BaseConfig<U> {
    if (BaseConfig.instance === null) {
      BaseConfig.instance = new BaseConfig<U>();
    }
    return BaseConfig.instance;
  }

  /**
   * 注册多个配置，按 appId 分类
   * @param appId 应用ID
   * @param configs 配置对象
   */
  config(appId: number, configs: Record<keyof T, any>): void {
    if (!this.appConfigs.has(appId)) {
      this.appConfigs.set(appId, new Map<keyof T, any>());
    }

    const appConfigMap = this.appConfigs.get(appId)!;
    appConfigMap.clear();
    Object.entries(configs).forEach(([key, config]) => {
      if (appConfigMap.has(key)) {
        console.warn(
          `Config with key ${key} already exists for appId ${appId}. It will be overwritten.`
        );
      }
      appConfigMap.set(key, config);
    });
  }

  /**
   * 获取单个配置
   * @param appId 应用ID
   * @param key 配置键
   * @returns 配置值
   */
  getConfig<K extends keyof T>(appId: number, key: K): T[K] | undefined {
    const appConfigMap = this.appConfigs.get(appId);
    return appConfigMap?.get(key);
  }

  /**
   * 获取某个应用的所有配置
   * @param appId 应用ID
   * @returns 应用的所有配置
   */
  public getAppConfigs(appId: number): Map<keyof T, any> | undefined {
    return this.appConfigs.get(appId);
  }

  /**
   * 更新某个配置
   * @param appId 应用ID
   * @param key 配置键
   * @param newConfig 新的配置值
   */
  public updateConfig<K extends keyof T>(
    appId: number,
    key: K,
    newConfig: Partial<T[K]>
  ): void {
    const appConfigMap = this.appConfigs.get(appId);
    if (!appConfigMap) {
      console.warn(`No configs found for appId ${appId}.`);
      return;
    }

    const existingConfig = appConfigMap.get(key as keyof T);
    if (existingConfig) {
      const updatedConfig = { ...existingConfig, ...newConfig };
      appConfigMap.set(key as keyof T, updatedConfig);
    } else {
      console.warn(
        `Config with key ${String(key)} does not exist for appId ${appId}.`
      );
    }
  }

  /**
   * 删除某个配置
   * @param appId 应用ID
   * @param key 配置键
   */
  public removeConfig<K extends keyof T>(appId: number, key: K): void {
    const appConfigMap = this.appConfigs.get(appId);
    if (appConfigMap?.has(key as keyof T)) {
      appConfigMap.delete(key as keyof T);
    } else {
      console.warn(`No config found for key ${String(key)} in appId ${appId}.`);
    }
  }

  /**
   * 删除整个应用的配置
   * @param appId 应用ID
   */
  public removeAppConfigs(appId: number): void {
    if (this.appConfigs.has(appId)) {
      this.appConfigs.delete(appId);
    } else {
      console.warn(`No configs found for appId ${appId}.`);
    }
  }
}
