//@ts-nocheck
import {request, i18n, getAppGlobalData} from '@base-app/runtime-lib';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;
const limit = 2147483647;

// 报警等级分页查询
export const fetchAlarmLevelPage = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/alarm/level/page',
        params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警等级设置详情
export const fetchAlarmLevelDetail = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/alarm/level/detail',
        params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 报警等级-排序
export const sortAlarmLevel = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/level/sort',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 报警分类-排序
export const sortAlarmType = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/category/sort',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警等级设置-编辑保存
export const updateLevelAlarm = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/level/type/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警分类设置-编辑保存
export const updateCategoryAlarm = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/category/type/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 新增报警等级
export const addAlarmLevel = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/level/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 编辑报警等级
export const editAlarmLevel = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/level/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除报警等级
export const deleteAlarmLevel = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/level/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警等级数量统计
export const alarmLevelCount = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v1/alarm/level/count',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警分类分页查询
export const fetchAlarmCategoryPage = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/alarm/category/page',
        params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 新增报警分类
export const addAlarmCategory = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/category/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 新增常用文本
export const addComText = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/text/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 编辑报警分类
export const editAlarmCategory = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/category/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 编辑常用文本
export const editComText = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/text/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警分类设置详情
export const fetchAlarmCategoryDetail = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/alarm/category/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除报警分类
export const deleteCategoryLevel = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/alarm/category/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取邮件发送配置
export const fetchEmailConfigurePage = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-alarm-service/api/v1/configure/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 修改邮件发送配置
export const editEmailConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'put',
        url: '/base-alarm-service/api/v1/configure',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警的响应设置
export const configureEfficient = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'GET',
        url: '/base-config-service/api/v1/configure/efficient',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export default {
    getCategory: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-config-service/api/v1/alarm/category/list',
            params,
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }

        return data.map((i: any) => {
            return {
                value: i['categoryId'],
                label: i18n.t(`alarmType_${i['categoryId']}`, i['categoryName']),
            };
        }) as { value: string; label: string }[];
    },
    getAlarmTypes: async (params?: any, headers?: Record<string, any>) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-config-service/api/v1/alarm/type/page',
            params,
            headers
        });
        if (!success || code !== successCode) {
            throw message;
        }
        if (data && data.list) {
            const obj = {};
            data.list.forEach((item: any) => {
                if (!obj[item.alarmType]) {
                    obj[item.alarmType] = item;
                }
            });
            return Object.values(obj).map((i: any) => {
                return {
                    value: i.alarmType,
                    label: i18n.t(`@i18n:@alarmType__${i.alarmType}`, i['typeName']),
                    // label: i18n.t(`name`, i['typeName']),
                };
            });
        }
        return undefined;
    },
    getAuthAlarmTypes: async (params?: any) => {
        const data: any = await getAlarmTypeAuthorityPage(params);
        if (data && data.list) {
            const obj = {};
            data.list.forEach((item: any) => {
                if (!obj[item.alarmType]) {
                    obj[item.alarmType] = item;
                }
            });
            return Object.values(obj).map((i: any) => {
                return {
                    value: i.alarmType,
                    label: i18n.t(`@i18n:@alarmType__${i.alarmType}`, i['alarmTypeName']),
                    // label: i18n.t(`name`, i['typeName']),
                };
            });
        }
        return undefined;
    },
    getList: async () => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/api/v1/company/list',
            params: {
                limit,
                domainType: 1,
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data.map((i: any) => {
            return {
                id: i['companyId'],
                title: i['companyName'],
                pId: i['parentId'],
            };
        }) as { id: string; title: string; pId: string }[];
    },
    getAuthorityCategory: async (params?: any) => {
        return new Promise(async (resolve, reject) => {
            const { success, code, message, data } = await request({
                baseURL,
                method: 'get',
                url: '/base-alarm-service/api/v1/alarm/type/authority/list',
                params: {
                    limit: 1e8,
                    ...params,
                },
            });
            if (!success || code !== successCode) {
                reject(message);
            }
            const list = data.map((i: any) => {
                return {
                    value: i['alarmType'],
                    label: i18n.t(`alarmType_${i['alarmType']}`, i['typeName']),
                };
            });
            resolve(list);
        });
    },
};
// 查询报警历史
export const getAlarmHistory = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/alarm/history',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警基础信息列表
export const getBaseAlarmList = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-search-service/api/v2/alarm/list',
        params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警类型
export const getAlarmType = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'GET',
        url: '/base-config-service/api/v1/alarm/type/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警列表V2
interface AlarmByPageParams {
    startTime: number;
    endTime: number;
    [key: string]: any;
}
export interface AlarmByPageItem {
    alarmId: string;
    alarmType: string;
    alarmLevelId: string;
    // 报警来源类型：1-设备，2-平台
    alarmSourceType: 1 | 2;
    startTime: number;
    lng: number;
    lat: number;
    angle: number;
    mileage: number;
    altitude: number;
    speed: number;
    status: string;
    handleStatus: string;
    vehicleInfo?: {
        vehicleId: string;
        vehicleNumber: string;
        vehicleColor: string;
        deviceNo: string;
    };
    driverList?: {
        driverId: string;
        driverName: string;
        jobNumber: string;
        fleetList: Record<string, any>[];
    }[];
    labelIdList: {
        alarmId: string;
        labelType: number;
        labelIds: string[];
    }[];
}
export const getAlarmByPage = async (params?: AlarmByPageParams, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<AlarmByPageItem>
    >({
        baseURL,
        method: 'POST',
        url: '/base-search-service/api/v2/alarm/page',
        data: params,
        timeout: 60000,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export const getAlarmCountByPage = async (params?: AlarmByPageParams) => {
    const { success, code, message, data } = await request<Request.Response<number>>({
        baseURL,
        method: 'POST',
        url: '/base-search-service/api/v2/alarm/count',
        data: params,
        timeout: 60000,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]获取指定用户下的实时报警列表
export const getRealAlarmList = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-search-service/api/v1/alarm/real/list',
        data: params,
        timeout: 60000,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// [API]获取用户权限下的增量实时报警列表
export const getRealAlarmListAppend = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'GET',
        url: '/base-search-service/api/v1/alarm/real/append',
        params,
        timeout: 60000,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 处理报警
export const handleAlarm = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-search-service/api/v1/alarm/handle',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警操作记录
export const handleAlarmRecord = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-search-service/api/v1/alarm/handle/record',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警详情
export const getAlarmDetail = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'GET',
        url: '/base-search-service/api/v2/alarm/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查报警处理历史查询
export const getAlarmHandleHistory = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'GET',
        url: '/base-search-service/api/v1/alarm/handle/record/page',
        params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询已授权报警类型分页
export const getAlarmTypeAuthorityPage = async (params?: any, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-alarm-service/api/v1/alarm/configure/type/authority/page',
        data: {
            appId: getAppGlobalData('APP_ID'),
            // state: 1,
            ...params
        },
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    data.list.forEach(i=>{
        i.alarmTypeName = i.typeName;
    });
    return data;
};

// 删除报警分类
export const deleteAlarmCategory = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-config-service/api/v1/alarm/type/authority/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 授权报警分类
export const postAlarmCategoryAuthority = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-config-service/api/v1/alarm/type/authority',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警类型详情
export const fetchAlarmTypeDetail = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/alarm/type/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]获取指定用户下的实时报警总数
export const fetchAlarmRealLast = async () => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-search-service/api/v1/alarm/real/last',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]报警标签分页查询接口
export const fetchAlarmLabelPage = async (params?: any) => {
    const { success, code, message, data } = await request<
        Request.Response<{
            list: {
                alarmId: string;
                labelIdList: string[]; // 标签id
            }[];
            page?: number;
            pageSize?: number;
            total?: number;
        }>
    >({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v1/alarm/label/page',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]通过报警id列表查询报警标签信息
export const fetchAlarmLabelByAlarmId = async (params: { alarmIdList: string[] }) => {
    const { success, code, message, data } = await request<
        Request.Response<
            {
                alarmId: string;
                labelIdList: string[];
            }[]
        >
    >({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v1/alarm/label/list/by/alarmid',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]根据alarmIds 批量查询报警信息
export const fetchAlarmListByAlarmId = async (params: {
    alarmIdList: string[];
    fields: string;
    complexSort: string;
}) => {
    // fields 和原来一样
    const { success, code, message, data } = await request<Request.Response<any[]>>({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v2/alarm/list/by/alarmid',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
export const getPageListByAlarm = async (params: { alarmIds: string; fields: string; mosaicFlag: number }) => {
    const { success, code, message, data } = await request<Request.Response<any[]>>({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v2/evidence/by/alarm/list',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警打标，支持单个报警处理和批量报警处理
export const handleAlarmPlayLabel = async (params: {
    alarmIdList: string[];
    labelIdList?: string[];
    content?: string;
}) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v1/alarm/label/handle',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 单个报警的打标处理结果查询 - 回显报警打标继续处理
export const fetchAlarmHandleResult = async (params: { alarmId: string }, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request<
        Request.Response<{
            alarmId: string;
            labelIdList: string[];
            content: string;
        }>
    >({
        baseURL,
        method: 'post',
        url: '/base-search-service/api/v1/alarm/label/by/alarmid',
        data: params,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询报警是存在
export const queryAlarmExist = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-search-service/api/v1/alarm/exist',
        params,
    });
    if (!success || code !== 200) {
        throw message;
    }
    return data;
};
