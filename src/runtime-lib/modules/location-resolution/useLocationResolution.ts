import { i18n } from '@base-app/runtime-lib';
import { useEffect, useRef, useState } from 'react';
import { locationResolution } from './regeo';
import type { Point } from './regeo';

/**
 * A custom hook that resolves the location of given points and provides the resolved address,
 * loading state, and a function to manually trigger the resolution.
 *
 * @param {Object} options - The options object.
 * @param {Point[]} options.points - The array of points to be resolved.
 * @param {boolean} [options.autoParse=true] - Whether to automatically trigger the resolution on mount.
 * @param {() => void} [options.onSuccess] - A callback function to be called when the resolution is successful.
 * @return {Object} - An object containing the resolved address, loading state, and a function to manually trigger the resolution.
 * @property {string[]} address - The resolved address for each point.
 * @property {boolean} parsing - The loading state indicating whether the resolution is in progress.
 * @property {(points: Point[]) => Promise<void>} parse - A function to manually trigger the resolution.
 */
export enum ParseStatus  {
    'start',
    'parsing',
    'success',
    'failed',
    'end'
}
const useLocationResolution = ({
    points,
    autoParse = true,
    onSuccess,
}: {
    points: Point[];
    autoParse?: boolean;
    failed?: boolean;
    onSuccess?: (address: string, hasRealAddress: boolean) => void;
}) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<string[]>([]);
    const [status, setStatus] = useState<ParseStatus>(ParseStatus.start);
    const pointsRef = useRef<string>('');
    const parse = async (points: Point[]) => {
        let address;
        let hasRealAddress = false;
        setStatus(ParseStatus.parsing);
        const addressTitle = i18n.t('name', '解析中...');
        const errorAddress = i18n.t('name', '解析失败');
        setLoading(true);
        setData(points.map(() => addressTitle));
        pointsRef.current = JSON.stringify(points);
        try {
            const result = await locationResolution(points);
            setStatus(ParseStatus.success);
            //是否解析出真正的地址，排除为空地址
            hasRealAddress = !!result[0];
            if(i18n.t('message', '暂无地址') === result[0]){
                hasRealAddress = false;
            }
            if (i18n.t('message', '解析失败') === result[0]){
                hasRealAddress = false;
                setStatus(ParseStatus.failed);
            }
            address = result;
            onSuccess?.(address?.[0] || i18n.t('message', '暂无地址'), hasRealAddress);
        } catch (error) {
            setStatus(ParseStatus.failed);
            address = points.map(() => errorAddress);
        }
        setData(address);
        setStatus(ParseStatus.end);
        setLoading(false);
    };
    useEffect(() => {
        if (autoParse) {
            parse(points);
        } else if(!autoParse && pointsRef.current !== JSON.stringify(points)){
            // 不自动解析，并且经纬度发生变更，重置内容为空
            setData([]);
            setStatus(ParseStatus.start);
        }
    }, [autoParse, JSON.stringify(points)]);

    return { address: data, parsing: loading, parse, status };
};
export default useLocationResolution;
