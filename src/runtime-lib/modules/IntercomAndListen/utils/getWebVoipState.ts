import { fetchParameterDetail } from '@/service/parameter';
import { getAppGlobalData } from '@/runtime-lib/global-data';

const VoiceFileMap = {
    //zh_CN
    zh_CN: {
        startFile: require('../assets/pcm/zh-cn-start-speak.js').default,
        stopFile: require('../assets/pcm/zh-cn-end-speak.js').default,
    },
    //繁体zh_CN
    zh_HK: {
        startFile: require('../assets/pcm/zh-cn-start-speak.js').default,
        stopFile: require('../assets/pcm/zh-cn-end-speak.js').default,
    },
    //英文
    en_US: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //泰语
    th_TH: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //阿拉伯语
    ar_EG: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //越南语
    vi_VN: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //土耳其语
    tr_TR: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //波兰语
    pl_PL: {
        startFile: require('../assets/pcm/en-us-start-speak.js').default,
        stopFile: require('../assets/pcm/en-us-end-speak.js').default,
    },
    //西班牙语
    es_ES: {
        startFile: require('../assets/pcm/es-es-start-speak.js').default,
        stopFile: require('../assets/pcm/es-es-end-speak.js').default,
    },
    //葡萄牙语
    pt_BR: {
        startFile: require('../assets/pcm/pt-br-start-speak.js').default,
        stopFile: require('../assets/pcm/pt-br-end-speak.js').default,
    },
    //法语
    fr_FR: {
        startFile: require('../assets/pcm/fr-fr-start-speak.js').default,
        stopFile: require('../assets/pcm/fr-fr-end-speak.js').default,
    },
    //俄罗斯语
    ru_RU: {
        startFile: require('../assets/pcm/ru-ru-start-speak.js').default,
        stopFile: require('../assets/pcm/ru-ru-end-speak.js').default,
    },
    //德语
    de_DE: {
        startFile: require('../assets/pcm/de-de-start-speak.js').default,
        stopFile: require('../assets/pcm/de-de-end-speak.js').default,
    },
    //日语
    ja_JP: {
        startFile: require('../assets/pcm/ja-jp-start-speak.js').default,
        stopFile: require('../assets/pcm/ja-jp-end-speak.js').default,
    },
    //塞尔维亚语
    sr_RS: {
        startFile: require('../assets/pcm/sr-rs-start-speak.js').default,
        stopFile: require('../assets/pcm/sr-rs-end-speak.js').default,
    },
    //意大利语
    it_IT: {
        startFile: require('../assets/pcm/it-it-start-speak.js').default,
        stopFile: require('../assets/pcm/it-it-end-speak.js').default,
    },
    // 塔吉克语
    tg_TJ: {
        startFile: require('../assets/pcm/tg-tj-start-speak.js').default,
        stopFile: require('../assets/pcm/tg-tj-end-speak.js').default,
    }
};

// 查询提示语音参数
export const getIntercomVoiceOpenParameter = async () => {
    try {
        const parameterData = await fetchParameterDetail(
            {
                parameterKey: 'INTERCOM.VOICE.CONFIG',
            },
            false,
        );
        return parameterData?.parameterValue === '1';
    } catch (error) {
        return false;
    }
};

//获取结束对讲提示音播放后关闭对讲延时
export const getIntercomCloseTimeoutParameter = async () => {
    try {
        const parameterData = await fetchParameterDetail(
            {
                parameterKey: 'INTERCOM.CLOSE.TIMEOUT.CONFIG',
            },
            false,
        );
        let parameterValue = Number(parameterData?.parameterValue);
        if (Number.isNaN(parameterValue)) return 2;
        if (parameterValue > 20 || parameterValue < 0) parameterValue = 2;
        return parameterValue;
    } catch (error) {
        return 2;
    }
};
/**
 *
 * @returns object,
 * intercomVoiceOpen: 是否开启对讲提示音
 * startPrompt: 开启对讲提示语音文件buffer
 * stopPrompt: 关闭对讲提示语音文件buffer
 */
export const getWebVoipState = async () => {
    let startPrompt = undefined;
    let stopPrompt = undefined;
    const intercomVoiceOpen = await getIntercomVoiceOpenParameter();
    //未配置下发语音
    if (!intercomVoiceOpen) return { intercomVoiceOpen, startPrompt, stopPrompt };

    try {
        const appLang = getAppGlobalData('APP_LANG');
        const { startFile, stopFile } = VoiceFileMap[appLang];
        startPrompt = startFile;
        stopPrompt = stopFile;
        return { intercomVoiceOpen, startPrompt, stopPrompt };
    } catch (error) {
        return { intercomVoiceOpen: false, startPrompt: undefined, stopPrompt: undefined };
    }
};
