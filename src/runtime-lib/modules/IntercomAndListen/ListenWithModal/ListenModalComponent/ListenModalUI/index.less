//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.listen-modal-UI {
    position: static !important;
    display: flex;
    justify-content: center;
    .listen-modal-UI-modal {
        overflow: unset;
        position: fixed;
        top: 67px!important;
        z-index: 2000;
        width: auto !important;
        min-width: 520px;
        transition: all 0s;
        .poppy-modal-content {
            background: rgba(255, 255, 255, 0);
            border-radius: 6px;
        }
        .poppy-modal-body {
            display: flex;
            justify-content: space-between;
            gap: 16px;
            min-width: 520px;
            height: 57px;
            padding: 16px;
            color: rgba(255, 255, 255, 0.75);
            font-size: 16px;
            text-align: center;
            background: rgba(0, 0, 0, 0.75);
            border-radius: 50px;
            box-shadow: 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
            user-select: none;
            .listen-modal-close {
                width: 60px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                background: #fc5750;
                border-radius: 50px;
                cursor: pointer;
            }
            .disabled-hang-up {
                background: rgba(255, 255, 255, 0.75);
                cursor: not-allowed;
                pointer-events: none;
            }
            img {
                font-size: 0;
                vertical-align: baseline;
            }
        }
        .poppy-modal-content {
            box-shadow: none;
        }
    }
    .listen-vehicle-name {
        display: flex;
        align-items: center;
        max-width: 146px;
        overflow: hidden;
        color: #fff;
        white-space: nowrap;
        text-align: left;
        text-overflow: ellipsis;
        flex-basis: 100%;
        > span {
            display: inline-block;
            margin-right: 8px;
        }
        .icon-intercom {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 0;
            cursor: pointer;
            img {
                width: 18px !important;
                height: 18px;
            }
            &-disable {
                pointer-events: none;
            }
        }
        .listen-vehicle-number {
            display: inline-block;
            width: 120px;
            overflow: hidden;
            color: #fff;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-right: 0;
        }
    }
    .listen-drop-down-wrapper{
        background-color: @dropdown-menu-bg;
        &::abroad{
            border-radius: @border-radius-12;
        }
        top: 60px!important;
        .poppy-dropdown-menu{
            max-height: 184px;
            overflow-y: auto;
            width: 280px;
            .poppy-dropdown-menu-item{
                height: 32px;
            }

            .poppy-dropdown-menu-title-content{
                display: inline-block;
                width: 100%;
            }
            .channel-list-item{
                display: inline-block;
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }

    .listen-drop-down-container{
        max-width: 120px;
        display: flex;
        align-items: center;
        gap: 4px;
        .listen-channel-name {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            color: #fff;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .listen-drop-down-icon{
            height: 14px;
            width: 14px;
        }
    }

    .listen-div-container {
        display: flex;
        align-items: center;
        .listen-modal-close {
            margin-left: 16px;
        }
    }
    .voip-intercom-body {
        width: 175px;
        height: 25px;
        padding: 0 8px;
    }
    .voip-intercom-box {
        min-width: 175px;
    }
    .voip-intercom-body-text {
        height: 25px;
        margin: 0 30px;
        color: #ffffffd9;
        font-weight: 400;
        font-size: 14px;
        line-height: 25px;
    }
}
