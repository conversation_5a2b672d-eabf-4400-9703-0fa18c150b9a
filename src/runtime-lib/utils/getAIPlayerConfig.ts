import { getAppGlobalData } from "../global-data";
import i18n from "../i18n";

export enum SPEED_VALUE_MAP {
    'km/h' = 1,
    'mph' = 2
};
export enum DISTANCE_VALUE_MAP {
    'm' = 1,
    'ft' = 2
};
export const speedMap = [
    {label: 'km/h', value: SPEED_VALUE_MAP['km/h']},
    {label: 'mph', value: SPEED_VALUE_MAP.mph},
];

/* 此处按需求需要把km--->m展示
   此处按需求需要把mi--->ft展示*/
export const distanceMap = [
    {label: 'm', value: DISTANCE_VALUE_MAP.m},
    {label: 'ft', value: DISTANCE_VALUE_MAP.ft}
]
export const DEFAULT_SPEED_UNIT = SPEED_VALUE_MAP['km/h'];
export const DEFAULT_DISTANCE_UNIT = DISTANCE_VALUE_MAP.m;

export const getConfig = () => {
    const userConfig = getAppGlobalData('APP_USER_CONFIG');
    const speedUnitData: SPEED_VALUE_MAP = Number(userConfig.speedUnit) || DEFAULT_SPEED_UNIT;
    // const speedUnit = speedMap.find(i => i.value == speedUnitData)?.label || ''
    const distanceUnitData: DISTANCE_VALUE_MAP = Number(userConfig.mileageUnit) || DEFAULT_DISTANCE_UNIT;
    // const distanceUnit = distanceMap.find(i => i.value == distanceUnitData)?.label || ''
    return (
        {
            // 必须设置baseURL，不然langs、showCurves、aiUnit不会生效
            baseURL: (window as any).APP_CONFIG['s17.server.h5.url'],
            langs: [
                { key: "FATIGUE", value: i18n.t('name', '闭眼疲劳') },
                { key: "YAWNING", value: i18n.t('name', '打哈欠疲劳') },
                { key: "DISTRACTION", value: i18n.t('name', '分心') },
                { key: "SEATBELT", value: i18n.t('name', '安全带') },
                { key: "SMOKING", value: i18n.t('name', '抽烟') },
                { key: "PHONE", value: i18n.t('name', '打电话') },
                { key: "phone", value: i18n.t('name', '手机') },
                { key: "MASK", value: i18n.t('name', '口罩') },
                { key: "SUNGLASSES", value: i18n.t('name', '超规眼镜') },
                { key: "sunglasses", value: i18n.t('name', '超规眼镜') },
                { key: "LANE DEPARTURE", value: i18n.t('name', '车道偏离') },
                { key: "VEHICLE TOO CLOSE", value: i18n.t('name', '车距过近') },
                { key: "IMPENDING COLLISION", value: i18n.t('name', '碰撞') },
                { key: "people", value: i18n.t('name', '人') },
                { key: "car", value: i18n.t('name', '车') },
                { key: "truck", value: i18n.t('name', '卡车') },
                { key: "lamp", value: i18n.t('name', '红绿灯') },
                { key: "Large", value: i18n.t('name', '大') },
                { key: "Small", value: i18n.t('name', '小') },
            ],
            // 2. 需要展示的曲线配置 ['distance', 'ttc', 'speed']
            showCurves: [],
            // 3. 曲线单位配置
            aiUnit: {
                speed: speedUnitData,
                distance: distanceUnitData
            }
        }
    )
};