# 租户参数模块

租户参数模块提供了一个统一、类型安全、易于扩展的方式来管理和访问租户参数。它使用了现代的 React 状态管理库 zustand，支持在 React 组件内监听参数变化，并提供了便捷的脚本来快速添加新的参数。

## 模块结构

```
src/utils/tenant-params/
├── base/                       # 基础类和类型定义
│   ├── TenantParamBase.ts      # 租户参数基类
│   ├── TenantParamManager.ts   # 租户参数管理器
│   └── types.ts                # 类型定义
├── constants/                  # 常量定义
│   └── keys.ts                 # 租户参数键名枚举
├── params/                     # 具体的租户参数实现
│   ├── MonitoringVersion.ts    # 监控版本参数
│   ├── VideoDisplayAlgorithm.ts# 视频显示算法参数
│   ├── PlayerMediaSource.ts    # 播放媒体源参数
│   └── PlayerMp4Version.ts     # MP4版本参数
├── scripts/                    # 工具脚本
│   └── generate-param.js       # 生成租户参数文件的脚本
├── README.md                   # 本文档
└── index.ts                    # 导出文件
```

## 主要功能

1. **统一的参数访问方式**：通过 `tenantParams` 命名空间统一导出所有参数，提供更好的编辑器提示
2. **类型安全**：使用 TypeScript 泛型确保类型安全
3. **参数解析**：每个参数负责自己的解析逻辑，更符合面向对象设计
4. **状态管理**：使用 zustand 管理状态，支持 React 组件内监听变化
5. **批量加载**：通过 TenantParamManager 批量加载参数，减少请求次数
6. **默认值处理**：当参数未返回时使用默认值
7. **自动生成**：通过脚本快速生成新参数，减少重复工作

## 使用方法

### 初始化

在应用启动时，需要初始化租户参数模块，以便加载所有参数：

```typescript
import { initTenantParams } from '@/utils/tenant-params';

// 在应用启动时初始化所有租户参数
await initTenantParams();
```

推荐在应用的入口文件（如 `src/app.tsx`）中进行初始化。

### 在非 React 组件中使用

```typescript
import { tenantParams } from '@/utils/tenant-params';

// 获取监控版本
const version = tenantParams.monitoringVersionParam.get();
console.log(`当前监控版本: ${version === 1 ? '旧版' : '新版'}`);

// 判断是否是新版
if (tenantParams.monitoringVersionParam.isNewVersion()) {
  // 使用新版逻辑
}

// 获取算法配置
const algorithmConfig = tenantParams.videoDisplayAlgorithmParam.getAlgorithmConfig(true);
console.log('ADAS 是否开启:', algorithmConfig.adas);

// 获取媒体源类型
if (tenantParams.playerMediaSourceParam.isS17()) {
  // 使用 S17 服务器逻辑
}

// 获取 MP4 版本
if (tenantParams.playerMp4VersionParam.isVideoTag()) {
  // 使用 video 标签播放
}
```

### 在 React 组件中使用

在 React 组件中，可以使用 `useParam` 方法来获取参数值，并在参数值变化时自动重新渲染组件：

```tsx
import { tenantParams } from '@/utils/tenant-params';
import React from 'react';

const MonitoringComponent = () => {
  // 使用 hook 方式获取参数，组件会在参数值变化时自动重新渲染
  const { value, isLoading, error } = tenantParams.monitoringVersionParam.useParam();
  
  if (isLoading) {
    return <div>加载中...</div>;
  }
  
  if (error) {
    return <div>加载失败: {error.message}</div>;
  }
  
  return (
    <div>
      当前监控版本: {value === 1 ? '旧版' : '新版'}
    </div>
  );
};
```

### 添加新的租户参数

使用生成脚本快速创建新的租户参数：

```bash
node src/base/util/tenantParams/scripts/generate-param.js
```

脚本会引导你输入以下信息：

1. 参数名称（驼峰命名，如 monitoringVersion）
2. 参数键名（大写下划线，如 MONITORING_VERSION）
3. 参数实际键值（如 USE.MONITORING.VERSION）
4. 参数描述
5. 参数类型（string、number、boolean、json）
6. 参数默认值
7. 是否有枚举值及枚举值定义

然后脚本会自动：

1. 生成参数文件
2. 更新常量文件
3. 更新索引文件

## 租户参数文件示例

以下是一个租户参数文件的示例：

```typescript
/**
 * 监控版本参数
 * 用于控制使用哪个版本的监控页面
 */
import { TenantParamBase } from '../base/TenantParamBase';
import { TenantParamManager } from '../base/TenantParamManager';
import { TenantParamKeys } from '../constants/keys';

// 监控版本枚举
export enum MONITORING_VERSION {
  OLD_MONITORING_VERSION = 1,
  NEW_MONITORING_VERSION = 2
}

/**
 * 监控版本参数类
 */
export class MonitoringVersionParam extends TenantParamBase<number> {
  readonly key = TenantParamKeys.MONITORING_VERSION;
  readonly defaultValue = MONITORING_VERSION.NEW_MONITORING_VERSION;
  readonly type = 'number';
  readonly enumValues = MONITORING_VERSION;
  
  /**
   * 解析方法
   * @param value 字符串值
   * @returns 解析后的值
   */
  parse(value: string): number {
    const parsedValue = Number(value);
    // 检查值是否合法（1或2），不合法则使用默认值
    return parsedValue === MONITORING_VERSION.OLD_MONITORING_VERSION || 
           parsedValue === MONITORING_VERSION.NEW_MONITORING_VERSION 
           ? parsedValue 
           : this.defaultValue;
  }
  
  /**
   * 判断是否是新版
   * @returns 是否是新版
   */
  isNewVersion(): boolean {
    return this.get() === MONITORING_VERSION.NEW_MONITORING_VERSION;
  }
  
  /**
   * 判断是否是旧版
   * @returns 是否是旧版
   */
  isOldVersion(): boolean {
    return this.get() === MONITORING_VERSION.OLD_MONITORING_VERSION;
  }
}

// 创建实例并注册
export const monitoringVersionParam = new MonitoringVersionParam();
TenantParamManager.getInstance().register(monitoringVersionParam);
```

## 最佳实践

1. **统一管理**：所有租户参数都应该通过本模块管理，避免直接调用接口获取参数
2. **类型安全**：充分利用 TypeScript 的类型系统，确保参数类型安全
3. **默认值**：为每个参数提供合理的默认值，避免在参数未加载时出现错误
4. **辅助方法**：为常用的判断逻辑提供辅助方法，如 `isNewVersion()`，使代码更加清晰
5. **批量初始化**：在应用启动时一次性初始化所有参数，避免多次请求
6. **使用脚本**：使用提供的脚本来创建新的参数，保持代码风格一致

## 常见问题

### 参数值未加载

如果在参数加载完成前尝试获取参数值，将会得到默认值。可以通过 `isLoading()` 方法来判断参数是否已加载：

```typescript
if (!tenantParams.monitoringVersionParam.isLoading()) {
  // 参数已加载，可以安全使用
  const version = tenantParams.monitoringVersionParam.get();
}
```

在 React 组件中，可以使用 `useParam()` 方法来获取加载状态：

```tsx
const { value, isLoading } = tenantParams.monitoringVersionParam.useParam();

if (isLoading) {
  return <div>加载中...</div>;
}
```

### 参数加载失败

如果参数加载失败，可以通过 `getError()` 方法来获取错误信息：

```typescript
const error = tenantParams.monitoringVersionParam.getError();
if (error) {
  console.error('参数加载失败:', error);
}
```

在 React 组件中，可以使用 `useParam()` 方法来获取错误信息：

```tsx
const { value, isLoading, error } = tenantParams.monitoringVersionParam.useParam();

if (error) {
  return <div>加载失败: {error.message}</div>;
}
```

### 参数值变化监听

在非 React 环境中，如果需要监听参数值变化，可以使用 zustand 的 subscribe 方法：

```typescript
const unsubscribe = tenantParams.monitoringVersionParam.store.subscribe(
  (state) => state.value,
  (value) => {
    console.log('参数值变化:', value);
  }
);

// 不再需要监听时取消订阅
unsubscribe();
