import { registerConfig } from '../configRegistry';
import { getConfig } from '@/runtime-lib/utils/getAIPlayerConfig';

// 存储已配置过的版本号
const playerVersionCache: string[] = [];

/**
 * 注册播放器基础配置
 * 这个函数会在应用初始化时被调用，注册一个配置函数
 * 该配置函数会在 loadH5SDK 获取到 SPlayer 后被调用
 */
export const registerAILangueAndUnitConfig = () => {
  registerConfig((data) => {
    if (data && data.SPlayer && playerVersionCache.indexOf(data.SPlayer.version) === -1) {
      // 对没有配置过的版本进行设置播放器配置
      const config = getConfig();
      
      // 设置配置，player 实例内部会自动合并
      data.SPlayer.config = config;
      
      // 增加配置过的版本的版本号的缓存
      playerVersionCache.push(data.SPlayer.version);
    }
  });
};
