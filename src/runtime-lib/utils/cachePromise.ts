type CachedKey = string | number; // 定义了缓存键的类型，可以是字符串或数字

const cachePromise = new Map<CachedKey, Promise<any>>(); // 创建一个 Map，用于存储缓存的 Promise


 // 定义一个用于删除缓存的函数
  const removeCache = (cacheKey: CachedKey) => cachePromise.delete(cacheKey);

const getCachePromise = (cacheKey: CachedKey): Promise<any> | undefined => {
    return cachePromise.get(cacheKey); // 根据键获取缓存的 Promise
};

const setCachePromise = (cacheKey: CachedKey, promise: Promise<any>): void => {
    // 将 Promise 缓存起来
    cachePromise.set(cacheKey, promise);
    // 确保 Promise 解决或拒绝后将其从缓存中删除
    promise
        .then((res) => {
			removeCache(cacheKey);
            return res; 
        })
        .catch((err) => {
         	removeCache(cacheKey);
            throw err; // 重新抛出错误，确保 Promise 链不被打断
        });
};

export { getCachePromise, setCachePromise };
