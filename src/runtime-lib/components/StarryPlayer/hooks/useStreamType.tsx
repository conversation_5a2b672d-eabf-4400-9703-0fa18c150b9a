/*
 * @LastEditTime: 2024-02-28 10:50:16
 */
/**
 * @description 选择码流【hooks】
 */
import { flatten, groupBy } from 'lodash';
import { useContext, useMemo } from 'react';
import { PLAYBACK_TYPE, PLAYER_STATUS, PROTOCOL_N9M, STREAM_TYPE, UPDATE_STREAM_EXEC } from '../constant';
import { Context, usePlayerState } from '../store';
import type { ActionInfo, PlaybackVideoData, StreamType } from '../types';
import usePlaybackPlayData from './usePlaybackPlayData';
import useShouldRender from './useShouldRender';

export interface StoreItem {
    title: string;
    value: StreamType;
}

const useStreamType = () => {
    const { streamType, storeType, isLive, disabledWidgets, dispatch, playbackType, currentPlayTime, options, playerStatus } =
        usePlayerState(state => ({
            streamType: state.streamType,
            storeType: state.storeType,
            isLive: state.isLive,
            disabledWidgets: state.disabledWidgets,
            dispatch: state.dispatch,
            playbackType: state.playbackType,
            currentPlayTime: state.currentPlayTime,
            options: state.options,
            playerStatus: state.playerStatus
        }), true);
    const shouldRender = useShouldRender('streamType');
    const { playChannelData } = usePlaybackPlayData();

    const setStreamType = (type: StreamType) => {
        if ([PLAYER_STATUS.stop, PLAYER_STATUS.idle].includes(playerStatus)) return;
        // 判断当前时间是否存在此码流视频
        // 扁平化videoList
        const videoList: PlaybackVideoData[] = flatten(
            playChannelData.map((item) => item.videoList),
        ).filter((item) => {
            if (playbackType === PLAYBACK_TYPE.mix) {
                return item.streamType === type;
            }
            return item.storeType === storeType && item.streamType === type;
        });
        // 最小的开始时间
        const minStartUnix = Math.min(...videoList.map((item) => item.startUnix));
        const betweenInCurrentType = videoList.filter(p => (
            p.startUnix <= currentPlayTime &&
            p.endUnix >= currentPlayTime
        )).length > 0;

        dispatch({ type: 'setStreamType', payload: type });
        const actionInfo: ActionInfo = {
            actionName: 'streamType',
            actionPayload: { 
                streamType: type, 
                exec: UPDATE_STREAM_EXEC.reloadStart
            },
        };
        if (betweenInCurrentType) {
            // if (playbackType === PLAYBACK_TYPE.server) {
            //     // 当前时刻存在此码流，且是服务器回放，直接动态切换即可
            //     actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.dynamic;
            // } else if (playbackType === PLAYBACK_TYPE.mix) {
            //     // 融合回放也是直接销毁重新创建
            //     actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.reloadResume;
            //     actionInfo.actionPayload.time = currentPlayTime;
            // } else {
            //     // 如果是融合回放或设备回放，则需要是n9m设备才能动态切换
            //     if (options?.playbackoptions?.protocolType === PROTOCOL_N9M) {
            //         actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.dynamic;
            //     } else {
            //         actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.reloadResume;
            //         actionInfo.actionPayload.time = currentPlayTime;
            //     }
            // }
            actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.reloadResume;
            actionInfo.actionPayload.time = currentPlayTime;
        } else if (currentPlayTime <= minStartUnix) {
            // 当前时间之后还有此码流的视频，销毁后重新初始化并从此码流开始时间开始播放
            actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.reloadResume;
            actionInfo.actionPayload.time = minStartUnix;
        } else {
            // 当前时间之前存在此码流视频，销毁后重新初始化且从头开始播放
            actionInfo.actionPayload.exec = UPDATE_STREAM_EXEC.reloadStart;
            actionInfo.actionPayload.time = minStartUnix;
        }
        // 放到宏任务中发布action，确保streamType已变化
        setTimeout(() => {
            dispatch({
                type: 'setActionInfo',
                payload: actionInfo,
            });
        }, 0);
        return true;       
    };

    const streamTypeList: StreamType[] = useMemo(() => {
        if (isLive) {
            return [STREAM_TYPE.MAJOR, STREAM_TYPE.MINOR];
        }
        // 码流的可选类型是需要依赖于当前选择的存储器类型的，所以需要根据存储器类型进行过滤
        let videoList = flatten(playChannelData.map((item) => item.videoList)).filter(
            (item) => item.storeType === storeType,
        );
        if (playbackType === 'mixPlay') {
            // 融合回放不通过存储器类型进行过滤
            videoList = flatten(playChannelData.map((item) => item.videoList));
        }
        const streamTypeMap = groupBy(videoList, 'streamType');
        const list = Object.keys(streamTypeMap) as StreamType[];
        if (!list.includes(streamType) && list.length) {
            // 切换存储器后，当前存储器并不存在之前的码流类型数据，故需要自动切换码流
            setStreamType(list[0]);
        }
        return list;
    }, [playChannelData, storeType]);

    function _disabled() {
        return disabledWidgets.includes('storeType');
    }

    return {
        shouldRender,
        disabled: _disabled(),
        streamTypeList,
        currentStreamType: streamType,
        setStreamType,
    };
};

export default useStreamType;
