import { useEffect, useState } from 'react';
// @ts-nocheck
import { message } from '@streamax/poppy';
import { i18n, g_emmiter, getAppGlobalData, utils } from '@base-app/runtime-lib';
import { usePlayerConfigState } from '../store';
import { VEHICLE_STATE } from '../../../utils/constant';

export interface IntercomAndListenConfig {
    baseURL: string;
    loadH5SDK: () => Promise<any>;
    defaultHeaders?:Record<string, any>;
    getVehicleDetail: (params: any) => Promise<any>;
    getWebVoipState: () => Promise<any>;
    getIntercomCloseTimeoutParameter: () => Promise<any>
}

export enum StartVoicePlayStatus {
    play, //开始播放
    stop, // 结束播放
}

// 由于sdk异步，监听对讲成功失败相关信息采用callback方式
export type WebVoipStartTalkInfo = (Info: { type: 'success' | 'fail'; message?: any }) => void;
// const mediaServer = window.APP_CONFIG['s17.server.url'];
// const mediaPort = window.APP_CONFIG['s17.server.h5.port'];
// const loadH5SDK = utils.loadH5SDK.default;

let intercom: any = null;
let intercormInterval = 0;
let lastVehicleId: any = null;
export interface WebVoipCallback {
    onIntercomStart?: (params?: object) => void;
    onIntercomClose?: (params?: object) => void;
    onIntercomError?: (params: object) => void;
}

const useWebVoip = (
    intercomAndListenConfig: IntercomAndListenConfig,
    callbacks: WebVoipCallback, 
    onStartTalkInfo?: WebVoipStartTalkInfo, 
) => {
    const {
        // mediaServer,
        baseURL,
        loadH5SDK,
        defaultHeaders,
        getVehicleDetail,
        getWebVoipState,
        getIntercomCloseTimeoutParameter,
    } = intercomAndListenConfig;
    const { onIntercomStart, onIntercomClose, onIntercomError } = callbacks || {};
    const [intercomReady, setIntercomReady] = useState<boolean>(false);
    const [intercomConnect, setIntercomConnect] = useState<boolean>(false);
    const [intercomTimes, setIntercomTimes] = useState<string>('00:00:00');
    const [intercomMinuteTimes, setIntercomMinuteTimes] = useState<string>('00:00');
    const [intercomVolume, setIntercomVolume] = useState<number>(1);
    const [isEnableMic, setIsEnableMic] = useState<boolean>(true);
    const [intercomVoiceOpen, setIntercomVoiceOpen] = useState<boolean>(false); //是否开启对讲提示音
    const [startVoicePlayStatus, setStartVoicePlayStatus] = useState<StartVoicePlayStatus>(
        StartVoicePlayStatus.stop,
    ); //开始对讲语音播放完成状态
    const [disableVoiceButton, setDisableVoiceButton] = useState<boolean>(false); //禁用开麦闭麦
    // 初始化对讲
    const talk = async (vehicleId: string, optId?: string, deviceAuthId?: string): Promise<boolean|undefined> => {
        // 先获取设备信息
        try {
            if (lastVehicleId) return;
            lastVehicleId = vehicleId;
            if (intercom) {
                closeVoip();
            }
            if (!optId) {
                // @ts-nocheck
                console.log('【对讲】业务层没有返回optId');
            }

            const promiseList = [
                getVehicleDetail({
                    vehicleId,
                    fields: 'device,driver',
                }),
                loadH5SDK(),
                getWebVoipState(),
                getIntercomCloseTimeoutParameter()
            ];

            const [
                vehiclePromiseResult, 
                sdkPromiseResult, 
                voipStatePromiseResult, 
                timeoutParamterPromiseResult
            ] = await Promise.all(promiseList);

            const { deviceList, vehicleStateList = [] } = vehiclePromiseResult;
            const vehicleOnline =
                vehicleStateList.findIndex(
                    (p: any) => Number(p.stateCode) === VEHICLE_STATE.online,
                ) !== -1;
            // 中台现在只有3种状态，在线、离线、报警（也算在线）
            if (!vehicleOnline) {
                // eslint-disable-next-line no-console
                message.error(i18n.t('message', '设备离线，对讲失败'));
                // eslint-disable-next-line no-console
                onStartTalkInfo?.({
                    type: 'fail',
                    message: '设备离线，对讲失败',
                });
                return false;
            }
            const { authId } = deviceList[0];
            const { SPlayer } = sdkPromiseResult;
            SPlayer.config = {
                devId: deviceAuthId || authId,
                // ip: mediaServer,
                // port: mediaPort,
                // baseURL: (window as any).APP_CONFIG['s17.server.h5.url'],
                baseURL: baseURL,
                channel: '1',
                // httpProtocol: location.hostname === 'localhost' ? 'https' : location.protocol.split(':')[0],
                // mse解码 1mse 0 wasm
                decoderType: 1,
                // 打码类型  0 模糊 1方块
                mosaicType: 1,
            };
            SPlayer.defaultHeaders = {
                // _tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
                // _appId: getAppGlobalData('APP_ID'),
                // _token: window.localStorage.getItem('AUTH_TOKEN'),
                ...(defaultHeaders || {})
            };

            const { intercomVoiceOpen, startPrompt, stopPrompt } = voipStatePromiseResult;

            const intercomCloseTimeout = timeoutParamterPromiseResult;
            setIntercomVoiceOpen(intercomVoiceOpen);
            intercom = SPlayer.createIntercom({
                devId: deviceAuthId || authId,
                channel: "1",
                optId,
                startPrompt,
                stopPrompt,
                intercomCloseTimeout,
            });

            //开始对讲提示语音播放完成
            intercom.hooks.afterReady.tap('afterReady', () => {
                if (intercomVoiceOpen) {
                    setDisableVoiceButton(false);
                    message.destroy();
                    message.success(i18n.t('message', '提示音播放完毕，请开始对讲'));
                }
                setIsEnableMic(true);
                setStartVoicePlayStatus(StartVoicePlayStatus.stop);
            });
            //结束对讲提示语音播放完成
            intercom.hooks.afterStop.tap('afterStop', () => {
                setIntercomReady(false);
                setDisableVoiceButton(false);
                // message.destroy();
            });

            intercom.hooks.afterLoaded.tap('play', () => {
                if (intercomVoiceOpen) {
                    setStartVoicePlayStatus(StartVoicePlayStatus.play);
                    setDisableVoiceButton(true);
                    message.warning(i18n.t('message', '设备提示音播放中，请稍等'), 0);
                }
                setIntercomConnect(true);
                intercomStart(optId);
                onStartTalkInfo?.({
                    type: 'success',
                });
            });

            // intercom.hooks.onError.tap('error', (code, msg) => {
            intercom.hooks.onError.tap('error', (errorData: any) => {
                const {
                    errorType,
                    subErrorType,
                    errorInfo
                } = errorData;
                const { code, message: msg } = errorInfo;
                message.destroy();
                lastVehicleId = null;
                setIntercomConnect(true);
                onIntercomError?.({ code, msg });
                if (subErrorType.indexOf('DEVICE_IS_OCCUPIED') > -1) {
                    setIntercomVoiceOpen(false);
                    message.error(i18n.t('message', '设备占用'));
                    setIntercomReady(false);
                    onStartTalkInfo?.({
                        type: 'fail',
                        message: i18n.t('message', '设备占用'),
                    });
                } else if (subErrorType.indexOf('H5_SDK_INVALID_DEVICE') > -1) {
                    setIntercomVoiceOpen(false);
                    message.error(i18n.t('message', '设备不存在'));
                    intercom?.stop();
                } else if (subErrorType.indexOf('H5_SDK_NO_MICROPHONE_PERMISSION') > -1) {
                    setIntercomVoiceOpen(false);
                    message.error(i18n.t('message', '获取麦克风权限失败'));
                    intercom?.stop();
                } else if (subErrorType.indexOf("H5_SDK_NETWORK_ERROR") > -1) {
                    setIntercomVoiceOpen(false);
                    // 设备断流
                    lastVehicleId = null;
                    intercom?.stop();
                    message.error(i18n.t('message', '设备已主动断开对讲'));
                } else if (intercomVoiceOpen) {
                    intercom?.stop();
                }
                intercomStop();
            });

            // intercom.hooks.afterListenTalkAutoClose.tap('close', () => {
            //     setIntercomReady(false);
            //     lastVehicleId = null;
            //     intercom?.stop();
                // message.error(i18n.t('message', '设备已主动断开对讲'));
            // });
            if (intercormInterval) {
                window.clearInterval(intercormInterval);
                intercormInterval = 0;
                setIntercomTimes('00:00:00');
                setIntercomMinuteTimes('00:00');
            }
            return true;
        } catch (e) {
            message.error(e);
            return false;
        }
    };
    const fillZero = (num: number, len: number): string => {
        return num.toString().padStart(len, '0');
    };
    const getTime = (period: number): string => {
        if (period > 0) {
            const hours = Math.floor((period % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((period % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((period % (1000 * 60)) / 1000);
            return `${fillZero(hours, 2)}:${fillZero(minutes, 2)}:${fillZero(seconds, 2)}`;
        }
        return '';
    };
    const getMinuteTime = (period: number): string => {
        if (period > 0) {
            let minutes: string | number = Math.floor(period / (1000 * 60));
            //计算秒
            const seconds = (period / 1000) % 60;
            //如果只有一位数，前面增加一个0
            minutes = minutes < 10 ? '0' + minutes : minutes;
            return `${minutes}:${fillZero(seconds, 2)}`;
        }
        return '';
    };
    // 开始对讲
    const intercomStart = (optId: string) => {
        if (intercom == null) return;
        // 去掉手动对讲逻辑
        // intercom.start();
        onIntercomStart?.({ optId });
        intercom.volume = 1;
        setIntercomVolume(1);
        g_emmiter.emit('change-video-play-status', 'stop');
        setIntercomReady(true);
        intercom.onError = (error) => {
            if (error === '获取麦克风权限失败') {
                message.error(i18n.t('message', '获取麦克风权限失败'));
            } else {
                message.error(i18n.t('message', '系统异常，请稍后再试'));
            }
            intercomStop();
        };
        intercom.onClose = () => {
            g_emmiter.emit('change-video-play-status', 'play');
        };
    };

    // 关闭对讲
    const intercomStop = () => {
        if (intercomVoiceOpen) {
            message.destroy();
            setIntercomConnect(false);
            setDisableVoiceButton(true);
            startVoicePlayStatus === StartVoicePlayStatus.stop &&
                message.warning(i18n.t('message', '设备结束提示音播放中，请稍等'));
        }
        if (intercom) {
            intercom.stop();
            onIntercomClose?.();
            intercom = null;
            lastVehicleId = null;
            setIntercomConnect(false);
        }
        if (intercormInterval) {
            clearInterval(intercormInterval);
        }
    };
    // 静音
    const intercomMute = () => {
        if (intercom) {
            intercom.volume = 0;
            setIntercomVolume(0);
        }
    };
    // /放开音量
    const intercomTurnon = () => {
        if (intercom) {
            intercom.volume = 1;
            setIntercomVolume(1);
        }
    };
    // 闭麦
    const disableMic = () => {
        if (intercom) {
            try {
                intercom.disableMic();
                setIsEnableMic(false);
            } catch (error) {
                // @ts-nocheck
                console.warn('【===闭麦失败===】', error);
            }
        }
    };
    // 开麦
    const enableMic = () => {
        if (intercom) {
            try {
                intercom.enableMic();
                setIsEnableMic(true);
            } catch (error) {
                // @ts-nocheck
                console.warn('【===开麦失败===】', error);
            }
        }
    };

    const closeVoip = () => {
        intercomStop();
    };
    useEffect(() => {
        //未开启下发语音且播放器准备好、开启下发语音播放器准备好并下发语音播放完毕 开始计时
        if (
            (!intercomVoiceOpen && intercomReady) ||
            (intercomVoiceOpen &&
                intercomReady &&
                startVoicePlayStatus === StartVoicePlayStatus.stop)
        ) {
            let count = 0;
            if (intercormInterval) {
                clearInterval(intercormInterval);
            }
            intercormInterval = window.setInterval(() => {
                count += 1000;
                setIntercomTimes(getTime(count));
                setIntercomMinuteTimes(getMinuteTime(count));
            }, 1000);
        }
    }, [intercomReady, startVoicePlayStatus]);

    useEffect(() => {
        window.addEventListener('unload', () => {
            closeVoip();
        });

        return () => {
            closeVoip();
            if (intercormInterval) {
                window.clearInterval(intercormInterval);
            }
            window.removeEventListener('beforeunload', () => {
                closeVoip();
            });
        };
    }, []);

    const stopIntercomStop = () => {
        message.destroy();
        intercomStop();
    };

    return {
        talk,
        stopIntercomStop,
        intercomMute,
        intercomTurnon,
        disableMic,
        enableMic,
        isEnableMic,
        intercomReady,
        intercomTimes,
        intercomMinuteTimes,
        intercomVolume,
        intercomConnect,
        intercomVoiceOpen,
        startVoicePlayStatus,
        disableVoiceButton,
    };
};
export default useWebVoip;