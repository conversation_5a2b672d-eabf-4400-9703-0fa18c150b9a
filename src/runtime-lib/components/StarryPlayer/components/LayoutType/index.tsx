/**
 * @description 布局模式【逻辑组件】
 */
import React from "react";
import LayoutTypeWidget, { LayoutTypeWidgetProps } from '../../widgets/LayoutType';
import useLayoutType from '../../hooks/useLayoutType';
import { InjectProps, LayoutType } from '../../types';

export type LayoutTypeComponentProps = LayoutTypeWidgetProps & InjectProps;

const componentName = 'LayoutType';

const LayoutTypeComponent: React.FC<LayoutTypeComponentProps> & {
    componentName: string;
} = (props) => {

    const {
        onSelect
    } = props;

    const { shouldRender, disabled, layoutType, setLayoutType } = useLayoutType();

    function handleOnSelect(value: LayoutType) {
        if (!disabled) {
            setLayoutType(value);
            onSelect?.(value);
        }
    }

    if (shouldRender) {
        return (
            <LayoutTypeWidget
                {...props}
                disabled={disabled}
                onSelect={handleOnSelect}
                value={layoutType}
            />
        );
    }
    return null;
};

LayoutTypeComponent.componentName = componentName;

export default LayoutTypeComponent;