.modal-outline-intercom-wrapper {
    position: static !important;
    display: flex;
    justify-content: center;
    .voip-modal {
        position: fixed;
        top: 67px;
        z-index: 2000;
        min-width: 520px;
        width: auto !important;
        transition: all 0s;
        .poppy-modal-content {
            background: rgba(255, 255, 255, 0);
            border-radius: 6px;
        }
        .poppy-modal-body {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-width: 520px;
            height: 57px;
            padding: 0 16px;
            color: rgba(255, 255, 255, 0.75);
            font-size: 16px;
            text-align: center;
            background: rgba(0, 0, 0, 0.75);
            border-radius: 50px;
            box-shadow: 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
            user-select: none;
            .voip-button-close {
                width: 60px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                background: #fc5750;
                border-radius: 50px;
                cursor: pointer;
            }
            .disabled-hang-up {
                background: rgba(255, 255, 255, 0.75);
                cursor: not-allowed;
                pointer-events: none;
            }
            img {
                font-size: 0;
                vertical-align: middle;
            }
        }
        .poppy-modal-content {
            box-shadow: none;
        }
        .voip_div_container{
            display: flex;
            align-items: center;
            .voip-button-close {
                margin-left: 16px;
            }
        }
    }
    .listen-vehicle-name {
        display: flex;
        align-items: center;
        width: 140px;
        overflow: hidden;
        color: #fff;
        white-space: nowrap;
        text-align: left;
        text-overflow: ellipsis;
        > span {
            display: inline-block;
            margin-right: 8px;
        }
        .icon-intercom {
            display: inline-block;
            width: 18px;
            height: 18px;
            font-size: 0;
            cursor: pointer;
            img {
                width: 18px !important;
                height: 18px;
            }
            &-disable {
                pointer-events: none;
            }
        }
        .listen-vehicle-vehicle-number {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            color: #fff;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
    .listen_div_container {
        display: flex;
        align-items: center;
        .voip-button-close {
            margin-left: 16px;
        }
    }
    .voip-intercom-body {
        width: 175px;
        height: 25px;
        padding: 0 8px;
    }
    .voip-intercom-box{
        min-width: 175px;
        max-width: 45%;
    }
    .voip-intercom-body-text {
        margin: 0 10px !important;
        line-height: 20px;
        color: #ffffffd9;
        font-weight: 400;
        font-size: 14px;
    }
}
