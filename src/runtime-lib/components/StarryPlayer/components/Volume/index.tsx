/**
 * @description 调节音量【逻辑组件】
 */
import React from 'react';
import VolumeWidget from '../../widgets/Volume';
import type { VolumeWidgetProps } from '../../widgets/Volume';
import useVolume from '../../hooks/useVolume';

export type VolumeProps = VolumeWidgetProps;

const Volume: React.FC<VolumeProps> & {
    componentName: string;
} = (props) => {
    const {} = props;

    const { shouldRender, disabled, volume, setVolume } = useVolume();

    function handleOnSelect(value: number) {
        if (!disabled) {
            setVolume(value);
        }
    }

    if (shouldRender) {
        return (
            <VolumeWidget {...props} disabled={disabled} value={volume} onChange={handleOnSelect} />
        );
    }
    return null;
};

Volume.componentName = 'Volume';

export default Volume;
