/**
 * @description 当前播放速度组件
 */
import SpeedSettingWidget from '../../widgets/SpeedSetting';
import type { SpeedSettingWidgetProps } from '../../widgets/SpeedSetting';
import useSpeedSetting from '../../hooks/useSpeedSetting';

export type SpeedSettingProps = SpeedSettingWidgetProps;

const componentName = 'SpeedSetting';

const SpeedSetting: React.FC<SpeedSettingProps> & {
    componentName: string;
    // 被MoreSetting收藏后组件渲染的位置
    getRenderPosition: () => 'left' | 'right';
} = (props) => {
    const { shouldRender, playSpeed, setSpeed } = useSpeedSetting();

    if (!shouldRender) return null;

    return <SpeedSettingWidget {...props} value={playSpeed} onSelect={setSpeed} />;
};

SpeedSetting.componentName = componentName;
SpeedSetting.getRenderPosition = () => 'left';

export default SpeedSetting;
