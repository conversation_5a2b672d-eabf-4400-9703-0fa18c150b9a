@import "~@streamax/poppy-themes/starry/index.less";
@import "../../vars.less";

.@{com-cls-prefix-channel-layout} {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    height: 100%;
    background: #212B36;
    // 满窗口通道
    .full {
        position: absolute;
        top: 0 !important;
        left: 0 !important;
        height: 100% !important;
        width: 100% !important;
        z-index: 10;
    }
    .placeholder-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &.deep-player-bg {
            background: #0D0D0D;
        }
        &-inner {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            & > img {
                height: 50%;
                max-height: 80px;
            }
        }
        .placeholder-text{
            margin-top: 8px;
            color: rgba(255, 255, 255, 0.45);
            margin-bottom: 0;
        }
    }
    .main-placeholder-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #fff;
        background:#0D0D0D;
        .placeholder-text{
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.45);
        }
    }
    .cover-wrapper{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        display: flex;
        align-content: center;
        justify-content: center;
        z-index: 10;
    }
}