@import "../../../vars.less";

@layout-gap: 4px;

.@{com-cls-prefix-channel-layout}-height-light {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
    align-items: center;
    .@{com-cls-prefix-channel-layout}-height-light-item {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #252A33;
    }
    &.height-light-layout-1 {
        .@{com-cls-prefix-channel-layout}-height-light-item {
            width: 100%;
            height: 100%;
        }
    }
    &.height-light-layout-2 {
        .@{com-cls-prefix-channel-layout}-height-light-item {
            width: calc(50% - 2px);
            height: 100%;
            &:first-child {
                margin-right: @layout-gap;
            }
        }
    }
    &.height-light-layout-4, &.height-light-layout-9 {
        position: static;
        padding-left: calc(100% - 314px);
        display: block;
        overflow-y: auto;
        .@{com-cls-prefix-channel-layout}-height-light-item {
            width: 100%;
            height: calc((100% - 8px) / 3);
            margin-bottom: @layout-gap;
            &.highlight {
                position: absolute;
                top: 0;
                left: 0;
                width: ~"calc(100% - 318px)";
                height: 100%;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    &.height-light-layout-4 {
        overflow: hidden;
    }
}