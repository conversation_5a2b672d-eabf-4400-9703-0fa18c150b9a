/**
 * @description 倒计时关闭小部件
 */

import { i18n } from '@base-app/runtime-lib';
import cn from 'classnames';
import { COM_CLS_PREFIX_WIDGET } from '../../constant';
import { BaseWidgetProps } from '../BaseWidget';
import { BaseWidgetExtendProps } from '../types';
import './index.less';

export interface CloseTimeWidgetProps
    extends BaseWidgetProps,
        BaseWidgetExtendProps {
    // 倒计时时长（单位秒）
    time: number;
}

const clsPrefix = COM_CLS_PREFIX_WIDGET;
const widgetName = 'CloseTime';

const CloseTime: React.FC<CloseTimeWidgetProps> & {
    componentName: string;
} = (props) => {
    const { time, className, style } = props;

    return (
        <div className={cn(`${clsPrefix}-closetime`, className)} style={style}>
            {i18n.t('message', '{closeTime}S后自动关闭', {
                closeTime: <span className="closetime">{time < 0 ? 0 : time}</span>,
            })}
        </div>
    );
};

CloseTime.componentName = widgetName;

export default CloseTime;
