/**
 * @description 边看边录小组件
 */
import React, { useState, useEffect, useRef } from 'react';
import BaseWidget from '../BaseWidget';
import type { BaseWidgetProps } from '../BaseWidget';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { Space } from '@streamax/poppy';
// @ts-ignore
import { ReactComponent as RecordingIcon } from '@/assets/icons/icon_recording.svg';
import { IconStoprecordingLine } from '@streamax/poppy-icons';
import { i18n, RouterPrompt } from '@base-app/runtime-lib';
import type { RecordStatus } from '../../types';
import { RECODING_STATUS, COM_CLS_PREFIX_WIDGET } from '../../constant';
import type { BaseWidgetExtendProps } from '../types';
import cn from 'classnames';
import './index.less';
export interface RealtimeRecordWidgetProps extends BaseWidgetProps, BaseWidgetExtendProps {
    // 开始图标
    startIcon?: React.ReactNode;
    // 结束图标
    stopIcon?: React.ReactNode;
    // 当前状态
    state: RecordStatus;
    // 点击开始
    onStart?: () => void;
    // 点击结束
    onEnd?: () => void;
}

const prefix = COM_CLS_PREFIX_WIDGET + '-realtime-record';

const { start, close, end } = RECODING_STATUS;

const RealtimeRecord: React.FC<RealtimeRecordWidgetProps> = (props) => {
    const {
        startIcon,
        stopIcon,
        disabled,
        state = close,
        onStart,
        onEnd,
        className,
        style,
    } = props;
    const [when, setWhen] = useState(false);
    const [recordTime, seRecordTime] = useState<number>(0);

    const recordTimer = useRef<any>();

    useEffect(() => {
        if (state === 'start') {
            seRecordTime(0);
            recordTimer.current = setInterval(() => {
                seRecordTime((t) => t + 1);
            }, 1000);
        } else {
            clearInterval(recordTimer.current);
        }
        if (state === 'end') {
            setWhen(false);
        }
    }, [state]);

    // 点击开始
    function handleOnStart() {
        if (!disabled) {
            setWhen(true);
        }
        onStart?.();
    }

    // 点击结束
    function handleOnEnd() {
        setWhen(false);
        onEnd?.();
    }

    function fixZero(value: any) {
        return value >= 10 ? value : `0${value}`;
    }

    const formatTime = (time: number) => {
        const minutes = Math.floor(time / 1000 / 60);
        const seconds = Math.floor((time % 60000) / 1000);
        return `${fixZero(minutes)}:${fixZero(seconds)}`;
    };
    const smallSize = window.outerWidth <= 768 ? 'small-size' : '';
    return (
        <div className={cn(prefix, className, smallSize)} style={style}>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '中断录制并离开？系统可能无法保存录制文件。')}
            />
            <Space size={16}>
                {state === start && (
                    <Space className="record-time">{formatTime(recordTime * 1000)}</Space>
                )}
                {[close, end].includes(state) ? (
                    <span onClick={handleOnStart}>
                        <BaseWidget title={i18n.t('action', '边看边录')} disabled={disabled} toolTipPlacement={'top'}>
                            {startIcon || <Icon component={RecordingIcon} />}
                        </BaseWidget>
                    </span>
                ) : (
                    <BaseWidget title={i18n.t('action', '边看边录')} disabled={disabled} toolTipPlacement={'top'}>
                        <span onClick={handleOnEnd}>
                            <span className="end-icon">
                                {stopIcon || <IconStoprecordingLine />}
                            </span>
                        </span>
                    </BaseWidget>
                )}
            </Space>
        </div>
    );
};

export default RealtimeRecord;
