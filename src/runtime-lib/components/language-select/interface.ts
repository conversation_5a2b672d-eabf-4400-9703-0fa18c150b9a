/**
 * Language Select 组件接口定义
 */

import { DropDownProps } from "@streamax/poppy/lib/dropdown";

/** 语言选项接口 */
export interface ILanguageOption {
    /** 语言代码 */
    value: string;
    /** 语言显示名称 */
    label: string;
}
/** 组件属性接口 */
export interface ILanguageSelectProps {
    /** 数据源获取函数，支持自定义 */
    getDataSource?: () => Promise<ILanguageOption[]>;

    /** 选择回调，支持自定义行为 */
    onSelect?: (value: string, option: ILanguageOption) => void;

    /** 自定义触发元素 */
    child?: React.ReactNode;

    /** 触发方式 */
    trigger?: DropDownProps['trigger'];
    
    /** 下拉菜单位置 */
    placement?: DropDownProps['placement'];
}
