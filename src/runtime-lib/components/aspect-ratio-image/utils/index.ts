import { IAspectRatio, IDisplayRatio } from '../interfaces';
import { ASPECT_RATIO_MAP, SUPPORTED_ASPECT_RATIOS } from '../constants';
import { StrategyConfigManager } from '../controller/StrategyConfigManager';

/**
 * 验证展示比例是否有效
 * @param ratio 展示比例
 * @returns 是否为有效的展示比例
 */
export const isValidAspectRatio = (ratio: string) => {
    return SUPPORTED_ASPECT_RATIOS.includes(ratio as IAspectRatio);
};

/**
 * 验证aspectRatio参数是否有效
 * @param aspectRatio 宽高比参数
 * @returns 是否有效
 */
export const isValidDisplayRatio = (aspectRatio: IDisplayRatio): boolean => {
    if (typeof aspectRatio === 'string') {
        return isValidAspectRatio(aspectRatio);
    } else if (typeof aspectRatio === 'number') {
        return aspectRatio > 0 && Number.isFinite(aspectRatio);
    }
    return false;
};

/**
 * 获取当前显示区域尺寸
 * @param containerRef 容器引用
 * @returns 显示区域尺寸
 */
export const getDisplayArea = (containerRef?: HTMLElement | null) => {
    return {
        width: containerRef?.clientWidth,
        height: containerRef?.clientHeight,
    };
};
/**
 * 获取宽高比映射值
 * @param aspectRatio 显示比例，可以是数字或字符串类型
 * @param width 图片原始宽度
 * @param height 图片原始高度
 * @returns 转换后的数字比例值
 */
export const getAspectRatioMapValue = (
    aspectRatio: IDisplayRatio, // 显示比例
    width: number, // 图片原始宽度
    height: number, // 图片原始高度
) => {
    //转换比例值
    if (typeof aspectRatio === 'number') {
        return aspectRatio;
    } else if (typeof aspectRatio === 'string') {
        return aspectRatio === 'origin'
            ? width / height
            : ASPECT_RATIO_MAP[aspectRatio];
    }
};
/**
 * 获取修正后的显示比例
 */
export const getFixedAspectRatio = (
    originAspectRatio: IAspectRatio, // 显示比例
    width: number, // 图片原始宽度
    height: number, // 图片原始高度
    wrapperWidth: number, // 容器宽度
    wrapperHeight: number, // 容器高度
    threshold: number = 0.65, // 比例阈值
) => {
    if (originAspectRatio !== 'full') return originAspectRatio;
    // 计算图片宽高和wrapper宽高的比例
    const widthRate = width / wrapperWidth;
    const heightRate = height / wrapperHeight;
    let rate = 0;
    if (widthRate > heightRate) {
        const realVideoHeight = (height * wrapperWidth) / width;
        rate = realVideoHeight / wrapperHeight;
    } else {
        const realVideoWidth = (width * wrapperHeight) / height;
        rate = realVideoWidth / wrapperWidth;
    }
    if (rate < threshold) {
        return 'origin';
    }
    return originAspectRatio;
};
/**
 * 根据数字宽高比生成样式
 * @param aspectRatio 宽高比
 * @param containerWidth 容器宽度
 * @param containerHeight 容器高度
 * @returns CSS样式对象
 */
export const generateStyleByNumberRatio = (
    aspectRatio: number,
    containerWidth: number,
    containerHeight: number,
): React.CSSProperties => {
    if (aspectRatio > containerWidth / containerHeight) {
        return {
            width: '100%',
            height: containerWidth / aspectRatio + 'px',
        };
    } else {
        return {
            width: aspectRatio * containerHeight + 'px',
            height: '100%',
        };
    }
};

/**
 * 应用策略后的最终样式
 * @param aspectRatio 展示比例
 * @param containerWidth 容器宽度
 * @param containerHeight 容器高度
 * @param imageWidth 图片宽度
 * @param imageHeight 图片高度
 * @returns 最终的CSS样式对象
 */
export const applyStrategyStyle = (
    aspectRatio: IDisplayRatio,
    containerWidth: number,
    containerHeight: number,
    imageWidth: number,
    imageHeight: number,
): React.CSSProperties => {
    let newAspectRatio = aspectRatio;
    if (aspectRatio === 'full') {
        //传入为铺满时需要根据容器与图片拉伸判断是否拉伸铺满还是展示图片原始比例
        const configManager = StrategyConfigManager.getInstance();
        const config = configManager.getConfig();
        const {
            adaptiveStrategy: { minCoverageRatio },
        } = config['full'];
        newAspectRatio = getFixedAspectRatio(
            aspectRatio,
            imageWidth,
            imageHeight,
            containerWidth,
            containerHeight,
            minCoverageRatio,
        );
    }
    //计算后full铺满容器，返回容器宽高
    if (newAspectRatio === 'full') {
        return {
            width: `100%`,
            height: `100%`,
        };
    }

    //转换比例值
    const transformAspectRatio = getAspectRatioMapValue(
        newAspectRatio,
        imageWidth,
        imageHeight,
    );
    //计算宽高
    return generateStyleByNumberRatio(
        transformAspectRatio,
        containerWidth,
        containerHeight,
    );
};
