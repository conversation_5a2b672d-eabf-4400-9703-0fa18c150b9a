import {
    useEffect,
    useState,
    useRef,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { ProTableProps } from '@streamax/poppy/lib/pro-table';
import {
    ProTable,
    ProForm,
    Form,
    Pagination,
    Affix,
    Container,
} from '@streamax/poppy';
import { QueryFormProps as DefaultQueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import { i18n } from '@base-app/runtime-lib';
import {cloneDeep, isNil, pick} from 'lodash';
import './index.less';

interface QueryFormProps<Values = any> extends DefaultQueryFormProps<Values> {
    onSearch?: (values: Values) => boolean | void;
    onReset?: () => boolean | void;
}

export type FetchDataFuncRes = {
    list: any[];
    total: number | string;
};

export interface StarryTableProps<Values = any> extends ProTableProps<Values> {
    /** @name 查询表单属性，参见QueryForm的属性 */
    queryProps?: QueryFormProps;
    /** @name 请求数据方法，方法需返回一个Promise */
    fetchDataFunc?: (values: any) => Promise<FetchDataFuncRes>;
    /** @name 组件加载完成后是否立即请求数据 */
    fetchDataAfterMount?: boolean;
    /** @name 是否启动轮训请求数据 */
    poll?: boolean;
    /** @name 轮训时间间隔，以秒为单位，默认60s */
    pollTime?: number;
    renderContainerStyle?: {
        queryForm?: boolean;
        toolbar?: boolean;
        listRender?: boolean;
        toolbarAndListRender?: boolean;
    };
    dataSource?: any[];
}

export interface Params {
    page?: number;
    pageSize?: number;
    complexSort?: string | null;
    [paramName: string]: any;
}

interface ReloadOptions {
    reset?: boolean;
}

export interface RefStarryTableProps {
    /** 对外抛出reload方法，reset用于控制是否需要重置分页查询参数 */
    reload?: (options?: ReloadOptions) => void;
    /** 对外抛出reset方法，外部可手动控制重置 */
    reset?: () => void;
    loadDataSource?: (values?: Record<string, any>) => void;
}

const DEFAULT_PAGE: number = 1;
const DEFAULT_PAGE_SIZE: number = 20;

const StarryTable: React.ForwardRefRenderFunction<
    RefStarryTableProps,
    StarryTableProps
> = (props, ref) => {
    const {
        queryProps,
        fetchDataFunc,
        dataSource,
        fetchDataAfterMount = false,
        poll = false,
        pollTime = 60, // 默认轮训时间间隔是60秒
        renderContainerStyle,
        ...rest
    } = props;

    const { pagination, ...tableProps } = rest;

    const [loading, setLoading] = useState<boolean>(false);
    const [innerDataSource, setInnerDataSource] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [params, setParams] = useState<Params>();
    const [affix, setAffix] = useState(false);
    const [containerStyle, setContainerStyle] = useState(renderContainerStyle);

    const [formRef] = Form.useForm<any>(queryProps?.form);

    const pollControlTimer = useRef<any>();
    const isMount = useRef<boolean>(false);
    useEffect(() => {
        isMount.current = true;
        return () => {
            isMount.current = false;
        };
    }, []);

    useEffect(() => {
        if (dataSource) {
            setInnerDataSource(dataSource);
        }
    }, [dataSource]);

    useEffect(() => {
        const {
            queryForm = true,
            toolbarAndListRender = true,
            toolbar = false,
            listRender = false,
        } = renderContainerStyle || {};
        setContainerStyle({
            queryForm,
            toolbar,
            listRender,
            toolbarAndListRender,
        });
    }, [renderContainerStyle]);

    const getTableData = () => {
        if (typeof fetchDataFunc === 'function') {
            setLoading(true);
            fetchDataFunc(cloneDeep(params))
                .then(({ list, total }) => {
                    setInnerDataSource(list);
                    setTotal(Number(total));
                })
                .catch()
                .finally(() => {
                    setLoading(false);
                    clearTimeout(pollControlTimer.current);
                    if (isMount.current && poll) {
                        pollControlTimer.current = setTimeout(
                            getTableData,
                            pollTime * 1000,
                        );
                    }
                    setTimeout(() => {
                        // 创建一个新的事件
                        const event = new Event('resize');
                        // 触发resize事件，触发Affix重新计算，【100744】当筛选数据较少时，分页的固定位置需要重新计算,暂行方案，查询后触发一次更新
                        window.dispatchEvent(event);
                    }, 100);
                });
        }
    };

    // 请求参数中的page超过了实际的页数时，用正确的页码重新请求
    // 这个逻辑可以处理以下场景：
    // 1. 删除最后一页数据，需要调整页码到倒数第二页
    // 2. 设置过滤参数进行请求，导致页码缩小
    // 3. 请求数据返回的total为空
    useEffect(()=>{
        if (!total) return;
        const currentPage = getCurrentPage();

        if (currentPage != params?.page){
            setParams({
                ...params,
                page: currentPage
            });
        }
    },[total]);

    const setFieldsParams = (reset = false) => {
        const queryParams = formRef.getFieldsValue();
        setParams({
            ...params,
            ...queryParams,
            page: reset
                ? params?.page || pagination?.defaultCurrent || DEFAULT_PAGE
                : DEFAULT_PAGE,
            pageSize: reset
                ? params?.pageSize ||
                  pagination?.defaultPageSize ||
                  DEFAULT_PAGE_SIZE
                : params?.pageSize || DEFAULT_PAGE_SIZE,
        });
    };

    useImperativeHandle(ref, () => {
        return {
            reload: (options?: ReloadOptions) => {
                const { reset } = options || { reset: false };
                if (reset) {
                    setParams({
                        ...params,
                        page: DEFAULT_PAGE,
                        pageSize: DEFAULT_PAGE_SIZE,
                    });
                } else {
                    getTableData();
                }
            },
            reset: () => {
                if (formRef) {
                    formRef.resetFields();
                    setFieldsParams();
                }
            },
            loadDataSource: (values?: Record<string, any>) => {
                if (values) {
                    if (queryProps) {
                        const keys = Object.keys(formRef.getFieldsValue());
                        const formValues = pick(values, keys);
                        Object.keys(formValues).length &&
                            formRef.setFieldsValue(formValues);
                    }
                    setParams({
                        page: DEFAULT_PAGE,
                        pageSize: DEFAULT_PAGE_SIZE,
                        ...params,
                        ...(queryProps ? formRef.getFieldsValue() : {}),
                        ...(values || {}),
                    });
                } else {
                    getTableData();
                }
            },
        };
    });

    useEffect(() => {
        if (fetchDataAfterMount) {
            // 获取查询表单的初始值
            setTimeout(() => {
                setFieldsParams(true);
            }, 500);
        }
        return () => {
            clearTimeout(pollControlTimer.current);
        };
    }, []);

    useEffect(() => {
        if (params) {
            getTableData();
        }
    }, [params]);

    /**
     * 代理onSeach回调，如果外部有传入该回调，则先调用外部逻辑
     * 如果外部逻辑返回true则继续执行内部逻辑，如果是false则不执行内部逻辑
     * @param values 表单数据
     */
    const onSearch = (values: any) => {
        let ret = true;
        if (
            queryProps?.onSearch &&
            typeof queryProps?.onSearch === 'function'
        ) {
            // @ts-ignore
            ret = queryProps?.onSearch(values);
        }
        if (ret) {
            // 执行内部onSearch逻辑
            setFieldsParams();
        }
        return ret;
    };

    const onReset = () => {
        let ret = true;
        if (queryProps?.onReset && typeof queryProps?.onReset === 'function') {
            // @ts-ignore
            ret = queryProps?.onReset();
        }
        if (ret) {
            // 执行内部onReset逻辑
            setFieldsParams();
        }
        return ret;
    };

    const onReload = () => {
        getTableData();
    };

    const handleTableChange = (pagination: any, filter: any, sorter: any) => {
        let complexSort = '';
        const TypeOfSorter = Object.prototype.toString.call(sorter);
        if (TypeOfSorter === '[object Object]') {
            const { field, order } = sorter;
            if (order === 'ascend') {
                complexSort = `orderBy ${field} asc`;
            } else if (order === 'descend') {
                complexSort = `orderBy ${field} desc`;
            }
        } else if (TypeOfSorter === '[object Array]') {
            const sorterList = sorter
                .map((item: any) => {
                    const { field, order } = item;
                    if (order === 'ascend') {
                        complexSort = `${field} asc`;
                        return complexSort;
                    }
                    if (order === 'descend') {
                        complexSort = `${field} desc`;
                        return complexSort;
                    }
                    return null;
                })
                .filter((item: string | null) => item !== null);
            if (sorterList.length > 0) {
                complexSort = `orderBy ${sorterList.reverse().join(',')}`;
            }
        }
        setParams({
            ...params,
            complexSort,
        });
    };

    const handleAffixChange = (affixed: any) => {
        setAffix(affixed);
    };

    const handlePaginationChange = (page: number, pageSize?: number) => {
        setParams({
            ...params,
            page,
            pageSize,
        });
    };
    const newQueryProps: QueryFormProps = {
        ...(queryProps || { items: [] }),
        onSearch,
        onReset,
    };
    let toolbar: any = {};
    if (tableProps.toolbar === false) {
        toolbar = false;
    } else if (tableProps.toolbar === true) {
        toolbar.onReload = onReload;
    } else {
        toolbar = {
            onReload,
            ...(tableProps.toolbar || {}),
        };
    }

    const renderContainer = (
        children: React.ReactNode,
        render: boolean,
        otherProps?: Record<string, any>,
    ) => {
        if (!render) return children;
        return <Container {...otherProps}>{children}</Container>;
    };

    // 通过后端返回的total和 pageSize 计算当前的页码，防止请求参数中的page超过了实际的页数
    const getCurrentPage = ()=>{
        const maxPage =  Math.ceil(total / (params?.pageSize || DEFAULT_PAGE_SIZE) );
        return Math.min(maxPage, params?.page || DEFAULT_PAGE);
    };

    return (
        <div className="starry-table-wrapper">
            {queryProps &&
                renderContainer(
                    <>
                        <div className="starry-table-query-wrapper">
                            <ProForm.QueryForm
                                layout="vertical"
                                {...newQueryProps}
                                form={formRef}
                                onKeyPress={(e) => {
                                    if (
                                        e.code === 'Enter' ||
                                        e.code === 'NumpadEnter'
                                    ) {
                                        onSearch(formRef.getFieldsValue());
                                    }
                                }}
                            />
                        </div>
                    </>,
                    containerStyle?.queryForm as boolean,
                    { className: 'query-form-container' },
                )}
            {renderContainer(
                <>
                    <div className="starry-table-content-wrapper">
                        <ProTable
                            {...tableProps}
                            dataSource={innerDataSource}
                            loading={loading}
                            toolbar={toolbar}
                            onChange={handleTableChange}
                            renderContainerStyle={{
                                toolbar: containerStyle?.toolbar,
                                table: containerStyle?.listRender,
                            }}
                        />
                    </div>
                    {total > 0 && (
                        <div
                            className={`starry-table-pagnition-wrapper ${
                                affix ? 'affixed' : ''
                            }`}
                        >
                            <Affix
                                offsetBottom={0}
                                onChange={handleAffixChange}
                            >
                                <div className="affix-pagination-wrapper">
                                    <Pagination
                                        showQuickJumper
                                        showSizeChanger
                                        {...pagination}
                                        current={params?.page || DEFAULT_PAGE}
                                        pageSize={
                                            params?.pageSize ||
                                            DEFAULT_PAGE_SIZE
                                        }
                                        total={total}
                                        showTotal={(totalNum: number) => {
                                            return i18n.t(
                                                'message',
                                                '共{nums}条',
                                                {
                                                    nums: (
                                                        <span className="pagination-total-nums">
                                                            {totalNum}
                                                        </span>
                                                    ),
                                                },
                                            );
                                        }}
                                        onChange={handlePaginationChange}
                                    />
                                    {affix ? (
                                        <div
                                            style={{
                                                height: 16,
                                            }}
                                        />
                                    ) : null}
                                </div>
                            </Affix>
                        </div>
                    )}
                </>,
                containerStyle?.toolbarAndListRender as boolean,
            )}
        </div>
    );
};

const StarryTableRef = forwardRef<RefStarryTableProps, StarryTableProps>(
    StarryTable,
) as (
    props: React.PropsWithChildren<StarryTableProps> & {
        ref?: React.Ref<RefStarryTableProps>;
    },
) => React.ReactElement;

export default StarryTableRef;
