@import (reference) '~@streamax/poppy-themes/starry/index.less';
@import (reference) '~@streamax/poppy/dist/poppy.dynamic-variable.less';
@import './abroad.less';

@prefix: starry-page-breadcrumb-layout;
@theme-style-prefix: starry-theme-style;
.@{prefix} {
    width: 100%;
    min-width: unset !important;
    height: 100%;
    background-color: @body-background;
    // overflow-x: auto;
    &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 24px;
        background-color: @body-background;
        &-left {
            display: flex;
            font-size: 16px;
            .poppy-breadcrumb {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                .poppy-breadcrumb-link {
                    word-break: break-all;
                }
            }
            .back-icon {
                width: 30px;
                height: 24px;
                margin-right: 24px;
                color: @primary-color;
                border: none;
                box-shadow: unset;
                &:disabled {
                    color: @starry-text-color-disabled;
                    background-color: @starry-bg-color-component-disabled;
                }
                &:not(:disabled) {
                    &:hover {
                        color: @primary-color-hover;
                    }
                    &:active {
                        color: @primary-color-active;
                    }
                }
            }
        }
    }
    &-body {
        display: flex;
        flex-direction: column;
        min-height: ~'calc(100% - 48px)';
        padding: 0 24px 24px;
        background-color: @body-background;
        & > * {
            margin-top: 16px;
            &:first-of-type {
                margin-top: 0;
            }
            &:last-of-type {
                flex: 1;
            }
        }
    }
    &-sticky {
        .@{prefix}-header {
            position: sticky;
            top: 64px;
            z-index: 50;
        }
    }
}

.@{theme-style-prefix}-dark .@{theme-style-prefix}-light {
    .@{prefix} {
        &-header {
            &-left {
                .back-icon {
                    background-color: transparent !important;
                }
            }
        }
    }
}

html[data-theme-mode='light'] {
    body[data-component-style='default'] {
        .@{prefix} {
            &-header {
                background: rgb(240, 242, 245);
            }
        }
    }
}
