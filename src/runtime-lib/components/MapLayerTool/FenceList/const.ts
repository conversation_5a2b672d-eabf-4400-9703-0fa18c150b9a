import { i18n } from '@base-app/runtime-lib';

export interface AreaPoint {
    order: number;
    lat: number;
    lng: number;
}

export interface FenceAreaItem {
    id: string; // 区域或线路ID
    areaName: string; // 区域或线路名称
    areaType: 1 | 2 | 3; // 形状，1： 圆形，2： 多边形，3：线路
    areaPointList: AreaPoint[]; // 点位列表
    style: {
        color: string; // 颜色
        linePx: number; // 线宽
        linePattern: 1 | 2 | 3; // 线段样式
    }[];
    extParam?: {
        radius?: number; // 圆半径
        routeLength?: number; // 线路长度
        routeWidth?: number; // 线路宽度
        routeSpeedLimit?: number; // 线路限速
    };
}

/** 围栏事件类型，1：出围栏事件，2：进围栏事件，3：进出围栏事件  */
export type FenceEventType = 1 | 2 | 3;

/** 限速参数类型，1：最高限速，2：最低限速，3：平均限速 */
export type SpeedLimitType = 1 | 2 | 3;

export const DEFAULT_MONITORING_CENTER_FENCE = 'MONITORING_CENTER_FENCE';

export enum FENCE_STATE {
    notStarted, // 未开始
    effective, // 生效中
    expired, // 已过期
}
export const FENCE_STATE_ARR = () => [
    // 这里需求未定暂时取消围栏状态
    {
        label: i18n.t('state', '未开始'),
        value: FENCE_STATE.notStarted,
        color: '#faad14',
    },
    {
        label: i18n.t('state', '生效中'),
        value: FENCE_STATE.effective,
        color: '#53c21c',
    },
    {
        label: i18n.t('state', '已过期'),
        value: FENCE_STATE.expired,
        color: '#bfbfbf',
    },
];
/** @type areaIds的最大长度，发请求用
 * 
*/
export const MAX_AREAIDS_LENGTH = 100;