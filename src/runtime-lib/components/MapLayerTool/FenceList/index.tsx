import { useAsyncEffect, useLatest } from '@streamax/hooks';
// @ts-ignore
import { StarryStorage } from '@base-app/runtime-lib';
import React, {
    forwardRef,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
    useContext
} from 'react';
import { DEFAULT_MONITORING_CENTER_FENCE } from './const';
import Detail from './Detail';
import List from './list';
import type { ListItem } from './list';
import usePaintShape, { sortAreaAndLine } from '../../../hooks/usePaintShape';
import { getShapePointsByIds } from '../../../service/fence';
import {
    getPlatformFenceBindArea,
    getMonitorPlatformFencePage,
} from '../../../service/fence-monitor';
import {
    getUserPageQueryParameters,
    saveUserPageQueryParameters,
} from '../../../service/user';
import { Context } from '../store';
import { DEFAULT_MAX_FENCE_SELECT_NUM } from '../const';
import { MAX_AREAIDS_LENGTH } from './const';
import { chunk } from 'lodash';
import './index.less';
export interface FenceListRef {
    clearFence: (selectedItem: ListItem) => Promise<void>;
    onOpenStateChange: (open: boolean) => void;
}
interface FenceListProps {
    map: any;
    L: any;
    /** 围栏开关 开：地图上展示围栏 关：地图上不展示 */
    panelShow: boolean;
    /** 存储code */
    storageKey?: string;
    /** 围栏详情code */
    fenceDetailCode?: string;
    /** 当前弹窗是否显示 */
    show?: boolean;
}

const LIST_SHOW = 1;
const DETAIL_SHOW = 2;

const FenceList = forwardRef(
    (props: FenceListProps, ref: React.Ref<FenceListRef>) => {
        const {
            map,
            L,
            show,
            panelShow,
            storageKey = DEFAULT_MONITORING_CENTER_FENCE,
            fenceDetailCode,
        } = props;

        const MONITORING_CENTER_FENCE_SWITCH = `${storageKey}_SWITCH`;
        const MONITORING_CENTER_FENCE_SELECTED_ROW = `${storageKey}_SELECTED_ROW`;
        const MONITORING_CENTER_FENCE_CLICKED_FENCE = `${storageKey}_CLICKED_FENCE`;
        const MONITORING_CENTER_FENCE_RELATED_AREA = `${storageKey}_RELATED_AREA`;
        const MONITORING_CENTER_FENCE_SELECTED_AREA = `${storageKey}_SELECTED_AREA`;

        const storage = StarryStorage();

        // 围栏列表或详情显示控制 1：显示列表 2:显示详情
        const [listOrDetailShowState, setListOrDetailShowState] =
            useState<number>(LIST_SHOW);
        const [detailTile, setDetailTile] = useState<string>('');
        const [detailId, setDetailId] = useState<string>('');
        // --fix--state 的any需要优化
        const [relatedArea, setRelatedArea] = useState<any>([]);
        const [selectedArea, setSelectedArea] = useState<any>([]);
        const [loading, setLoading] = useState<boolean>(false);

        const listRef = useRef<any>();
        const cacheSelectedRowKeysRef = useRef<any>([]); // 缓存勾选的围栏--用于打开popover时List组件回填之前勾选的
        const cacheClickedFenceRef = useRef<ListItem>({} as ListItem); //缓存选中行的围栏----用于打开popover时List组件回填之前选中的
        const cacheRelatedAreaRef = useRef<any>([]); // 缓存勾选的围栏关联的区域、线路--用于switch切换显示隐藏使用;详情goBack使用;
        const cacheSelectedAreaRef = useRef<any>([]); // 缓存选中行的围栏关联的区域、线路--用于switch切换显示隐藏使用;详情goBack使用;
        const cacheSwitchRef = useRef<boolean>(false); // 缓存电子围栏开关的状态
        const isInit = useRef(true);
        const latestPanelShow = useLatest(panelShow);
        const { maxFenceSelectNum = DEFAULT_MAX_FENCE_SELECT_NUM, onInitialFenceSelect } = useContext(Context);

        const { allShapeInstanceMap } = usePaintShape({
            map,
            L,
            shapeInfoList: relatedArea,
            selectedShapeInfo: selectedArea,
        });
        
        /** 每次弹窗打开时，重新加载围栏列表 */
        useAsyncEffect(async () => {
            if (!isInit.current && latestPanelShow.current) {
                await getAndSetData();
                listRef.current?.loadData();
                updateData(true);
            }
            isInit.current = false;
        }, [panelShow, show]);

        /** 关闭时恢复列表视图 */
        useEffect(() => {
            if (!panelShow) {
                setListOrDetailShowState(LIST_SHOW);
            }
        }, [panelShow]);

        const updateOrSaveData = async (id, type, value) => {
            await saveUserPageQueryParameters({
                paramType: type,
                paramValue: JSON.stringify(value),
            });
        }

        const getAreaIds = (someIds: ('related' | 'selected' | 'all') = 'all') => {
            const relatedIds =
                cacheRelatedAreaRef.current?.map((p: any) => p.id) || [];
            const selectedIds =
                cacheSelectedAreaRef.current?.map((p: any) => p.id) || [];
            if (someIds === 'related') {
                return relatedIds;
            }
            if (someIds === 'selected') {
                return selectedIds;
            }
            return Array.from(
                new Set([...relatedIds, ...selectedIds]),
            );
        }

        const updateCacheAreaData = async () => {
            const areaIds = getAreaIds('all');
            if (areaIds.length) {
                // 切割成长度为100的数组
                const chunkedAreaIds = chunk(areaIds, MAX_AREAIDS_LENGTH);
                const requestArr = chunkedAreaIds.map(ids => getShapePointsByIds({ areaIds: ids.join(',') }));
               
                Promise.all(requestArr).then(resArr => {
                    const rs = resArr.flat();
                    const areaMap = {};
                    rs.forEach((area) => {
                        areaMap[area.id] = area;
                    });
                    cacheRelatedAreaRef.current?.forEach(
                        (item: any, idx: number) => {
                            const newInfo = areaMap[item.id];
                            if (newInfo) {
                                cacheRelatedAreaRef.current[idx] = newInfo;
                            }
                        },
                    );
                    cacheSelectedAreaRef.current?.forEach(
                        (item: any, idx: number) => {
                            const newInfo = areaMap[item.id];
                            if (newInfo) {
                                cacheSelectedAreaRef.current[idx] = newInfo;
                            }
                        },
                    );
                    storage.setItem(
                        MONITORING_CENTER_FENCE_RELATED_AREA,
                        cacheRelatedAreaRef.current,
                    );
                    storage.setItem(
                        MONITORING_CENTER_FENCE_SELECTED_AREA,
                        cacheSelectedAreaRef.current,
                    );
                }).then(() => {
                    const relatedIds = getAreaIds('related');
                    const selectedIds = getAreaIds('selected');
                    // 暂定，目前只能通过查询接口判断是否已经保存过来决定调用更新还是保存接口
                    Promise.all([
                        getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_RELATED_AREA }),
                        getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_SELECTED_AREA }),
                        getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_CLICKED_FENCE })
                    ]).then(([relatedRes, selectedRes, clickedRes]) => {
                        try {
                            const related = relatedRes?.[0]?.id || null;
                            const selected = selectedRes?.[0]?.id || null;
                            const clicked = clickedRes?.[0]?.id || null;
                            updateOrSaveData(related, MONITORING_CENTER_FENCE_RELATED_AREA, relatedIds)
                            updateOrSaveData(selected, MONITORING_CENTER_FENCE_SELECTED_AREA, selectedIds)
                            updateOrSaveData(clicked, MONITORING_CENTER_FENCE_CLICKED_FENCE, cacheClickedFenceRef.current)

                        } catch (error) { }
                    });
                });
            }
        };

        const updateData = (isInit?: boolean) => {
            updateCacheAreaData().then(() => {
                // 初始化时展示围栏bug58485
                onSelectedChange(cacheSelectedRowKeysRef.current || [], true);
                onClick(cacheClickedFenceRef.current || []);
                listRef.current?.setSelection({
                    selectedRowKeys: cacheSelectedRowKeysRef.current,
                    clickedFence: cacheClickedFenceRef.current,
                });
                if (isInit) {
                    const param = {
                        selectedRowKeys: cacheSelectedRowKeysRef.current,
                        clickedFence: cacheClickedFenceRef.current,
                        switch: cacheSwitchRef.current,
                        relatedArea: cacheRelatedAreaRef.current,
                        selectedArea: cacheSelectedAreaRef.current
                    }
                    // 调用围栏初始化数据，有围栏选中时调用
                    onInitialFenceSelect?.(param);
                }
            });
        };

        const getAndSetData = async () => {
            return Promise.all([
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_SELECTED_ROW }),
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_CLICKED_FENCE }),
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_SWITCH }),
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_RELATED_AREA }),
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_SELECTED_AREA })
            ]).then(async ([selectedRowRes, clickedRes, switchRes, relatedRes, selectedRes]) => {
                try {
                    const hasData = getDataFromBackend(selectedRowRes?.[0]?.paramValue,
                        clickedRes?.[0]?.paramValue,
                        switchRes?.[0]?.paramValue,
                        relatedRes?.[0]?.paramValue,
                        selectedRes?.[0]?.paramValue);
                    if (!hasData) {
                        const keys = [
                            MONITORING_CENTER_FENCE_SWITCH,
                            MONITORING_CENTER_FENCE_SELECTED_ROW,
                            MONITORING_CENTER_FENCE_CLICKED_FENCE,
                            MONITORING_CENTER_FENCE_RELATED_AREA,
                            MONITORING_CENTER_FENCE_SELECTED_AREA,
                        ];
                        const allStore = await storage.getItems(keys);

                        const checkAllValues = keys.every(key => allStore[key] !== null && allStore[key] !== undefined);
                        // 有缓存取不到值，则默认值
                        if (checkAllValues) {
                            cacheSelectedRowKeysRef.current =
                                allStore[MONITORING_CENTER_FENCE_SELECTED_ROW];
                            cacheClickedFenceRef.current =
                                allStore[MONITORING_CENTER_FENCE_CLICKED_FENCE];
                            cacheSwitchRef.current = allStore[MONITORING_CENTER_FENCE_SWITCH];
                            cacheRelatedAreaRef.current =
                                allStore[MONITORING_CENTER_FENCE_RELATED_AREA];
                            cacheSelectedAreaRef.current =
                                allStore[MONITORING_CENTER_FENCE_SELECTED_AREA];
                            // 从缓存取，更新一次存到后端的数据
                            const relatedIds = getAreaIds('related');
                            const selectedIds = getAreaIds('selected');
                            updateOrSaveData(selectedRowRes?.[0]?.id, MONITORING_CENTER_FENCE_SELECTED_ROW, cacheSelectedRowKeysRef.current)
                            updateOrSaveData(clickedRes?.[0]?.id, MONITORING_CENTER_FENCE_CLICKED_FENCE, cacheClickedFenceRef.current)
                            updateOrSaveData(switchRes?.[0]?.id, MONITORING_CENTER_FENCE_SWITCH, cacheSwitchRef.current)
                            updateOrSaveData(relatedRes?.[0]?.id, MONITORING_CENTER_FENCE_RELATED_AREA, relatedIds)
                            updateOrSaveData(selectedRes?.[0]?.id, MONITORING_CENTER_FENCE_SELECTED_AREA, selectedIds)
                        }
                    }
                } catch (error) { }
            });
        }

        const getDataFromBackend = (selectedRowResValue, clickedResValue, switchResValue, relatedResValue, selectedResValue) => {
            // 从接口取
            cacheSelectedRowKeysRef.current = JSON.parse(selectedRowResValue) || [];
            cacheClickedFenceRef.current = JSON.parse(clickedResValue) || {};
            cacheSwitchRef.current = JSON.parse(switchResValue) || false;
            // 存的是只有id的数组，需要根据id去请求数据
            const cacheRelatedAreaIds = JSON.parse(relatedResValue) || [];
            const cacheSelectedAreaIds = JSON.parse(selectedResValue) || [];
            const allIds = Array.from(new Set([...cacheRelatedAreaIds, ...cacheSelectedAreaIds]))
            if (allIds.length) {
                const requestArr = chunk(allIds, MAX_AREAIDS_LENGTH)
                    .map(ids => getShapePointsByIds({ areaIds: ids.join(',') }));
                Promise.all(requestArr).then(resArr => {
                    const rs = resArr.flat();
                    const areaMap = {};
                    rs.forEach((area) => {
                        areaMap[area.id] = area;
                    });
                    cacheRelatedAreaRef.current?.forEach(
                        (item: any, idx: number) => {
                            const newInfo = areaMap[item.id];
                            if (newInfo) {
                                cacheRelatedAreaRef.current[idx] = newInfo;
                            }
                        },
                    );
                    cacheSelectedAreaRef.current?.forEach(
                        (item: any, idx: number) => {
                            const newInfo = areaMap[item.id];
                            if (newInfo) {
                                cacheSelectedAreaRef.current[idx] = newInfo;
                            }
                        },
                    );
                })
            }
            if (selectedRowResValue && clickedResValue && (switchResValue !== undefined) && relatedResValue && selectedResValue) {
                return true;
            } else {
                return false;
            }
        }
        useAsyncEffect(async () => {
            
            // fix: 69548
            // updateData();
        }, []);

        // const setPanelShowState = (state: boolean) => {
        //     setPanelShow(state);
        // };
        const fetchRelatedArea = async (id: string) => {
            getPlatformFenceBindArea({ fenceIds: id }).then((rs) => {
                setRelatedArea(rs);
                setSelectedArea([]);
            });
        };

        const getArrDifference = (arr1: string[], arr2: string[]) => {
            return arr1.concat(arr2).filter(function (v, i, arr) {
                return arr.indexOf(v) === arr.lastIndexOf(v);
            });
        };
        /**
         * @description:
         * @param {string} selectedItems
         * @param {false} isInit 是否是第一次初始化
         * @return {*}
         */
        const onSelectedChange = async (
            selectedItems: string[],
            isInit?: boolean,
        ) => {
            let isChecked = true; // 当前是选中还是取消选中
            const allStore = await storage.getItems([
                MONITORING_CENTER_FENCE_SELECTED_ROW,
                MONITORING_CENTER_FENCE_RELATED_AREA,
                MONITORING_CENTER_FENCE_CLICKED_FENCE,
            ]);
            const realTimeAllFence = await getMonitorPlatformFencePage({
                page: 1,
                pageSize: 1e8,
                fields: 'vehicleCount',
                state: 1,
            });
            let existDeleteFence = false; // 是否存在删除的围栏
            const oldCacheSelectedFenceRow =
                allStore[MONITORING_CENTER_FENCE_SELECTED_ROW] || [];
            const realTimeAllFenceId = realTimeAllFence.list.map(
                (i: { id: any }) => i.id,
            ); // 所有围栏的id
            // 判断之前缓存的围栏是否有被删除的
            const deleteFence = oldCacheSelectedFenceRow.filter(
                (i: any) => !realTimeAllFenceId.includes(i),
            );
            
            // 在选中的围栏中去掉被删除的
            // eslint-disable-next-line no-param-reassign
            selectedItems = selectedItems.filter((i) =>
                realTimeAllFenceId.includes(i),
            );
            if (deleteFence.length) {
                existDeleteFence = true;
            }
            try {
                // 已click的的围栏
                const oldCacheClickedFenceRow =
                    allStore[MONITORING_CENTER_FENCE_CLICKED_FENCE] || [];
                // 判断之前缓存的围栏是否有被删除的
                if (
                    oldCacheClickedFenceRow.id &&
                    !realTimeAllFenceId.includes(oldCacheClickedFenceRow.id)
                ) {
                    // @ts-ignore
                    cacheClickedFenceRef.current = {};
                    storage.setItem(
                        MONITORING_CENTER_FENCE_CLICKED_FENCE,
                        cacheClickedFenceRef.current,
                    );
                    updateData();
                }
            } catch (error) {}

            const oldCacheRelatedArea =
                allStore[MONITORING_CENTER_FENCE_RELATED_AREA] || [];
            let diff = getArrDifference(
                selectedItems,
                oldCacheSelectedFenceRow,
            );
            if (existDeleteFence) {
                diff = selectedItems;
            }
            if (selectedItems.length < oldCacheSelectedFenceRow.length)
                isChecked = false;
            // 第一次的时候请求所有的
            if (isInit) {
                diff = selectedItems;
            }
            // 最多勾选maxFenceSelectNum个围栏
            if (!!diff.length || selectedItems.length !== maxFenceSelectNum) {
                  setLoading(true);
                  cacheSelectedRowKeysRef.current = selectedItems;
                  // 获取选中围栏的关联区域
                  if (!selectedItems || !selectedItems.length) {
                      setRelatedArea([]);
                      cacheRelatedAreaRef.current = [];
                  } else {
                    const promiseAllArr = chunk(diff, 100).map((item) =>
                        getPlatformFenceBindArea({ fenceIds: item.join(',') }),
                    );
                      const promiseData = await Promise.all(promiseAllArr);
                      const rs = promiseData?.reduce((pre, cur) => pre.concat(cur), []);
                    let newRelatedArea = [];
                    // 选中时增加缓存中的数据
                    if (isChecked) {
                        const filterData = (
                            oldCacheRelatedArea || []
                        ).filter(
                            (p: any) =>
                                rs.findIndex(
                                    (r) => r.id === p.id,
                                ) === -1,
                        );
                        newRelatedArea = filterData.concat(rs);
                        if (existDeleteFence || isInit) {
                            newRelatedArea = rs;
                        }
                    } else {
                        //取消选中时,删除缓存中的数据
                        const areaIds = (rs || []).map(
                            (item) => item.id,
                        );
                        newRelatedArea = (
                            oldCacheRelatedArea || []
                        ).filter(
                            (item: any) =>
                                !areaIds.includes(item.id),
                        );
                    }
                    cacheRelatedAreaRef.current =
                        sortAreaAndLine(newRelatedArea);
                    setRelatedArea(newRelatedArea);
                  }
            }
            storage.setItem(
                MONITORING_CENTER_FENCE_RELATED_AREA,
                cacheRelatedAreaRef.current,
            );
            storage.setItem(
                MONITORING_CENTER_FENCE_SELECTED_ROW,
                cacheSelectedRowKeysRef.current,
            );
            // 暂定，目前只能通过查询接口判断是否已经保存过来决定调用更新还是保存接口
            Promise.all([
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_RELATED_AREA }),
                getUserPageQueryParameters({ paramType: MONITORING_CENTER_FENCE_SELECTED_ROW })
            ]).then(([relatedRes, selectedRowRes]) => {
                try {
                    const related = relatedRes?.[0]?.id || null;
                    const selected = selectedRowRes?.[0]?.id || null;
                    const relatedIds = getAreaIds('related');
                    updateOrSaveData(related, MONITORING_CENTER_FENCE_RELATED_AREA, relatedIds)
                    updateOrSaveData(selected, MONITORING_CENTER_FENCE_SELECTED_ROW, cacheSelectedRowKeysRef.current)

                } catch (error) { }
            });
            setLoading(false);
        };

        const onDetailView = (selectedItem: ListItem) => {
            setDetailTile(selectedItem.name);
            setDetailId(selectedItem.id);
            setListOrDetailShowState(DETAIL_SHOW);
            fetchRelatedArea(selectedItem.id);
        };

        const onClick = async (selectedItem: ListItem) => {
            cacheClickedFenceRef.current = selectedItem;
            storage.setItem(
                MONITORING_CENTER_FENCE_CLICKED_FENCE,
                cacheClickedFenceRef.current,
            );
            const dataItem = (await getUserPageQueryParameters({
                paramType: MONITORING_CENTER_FENCE_CLICKED_FENCE,
            }))?.[0]
            const clicked = dataItem?.id;
            updateOrSaveData(clicked, MONITORING_CENTER_FENCE_CLICKED_FENCE, cacheClickedFenceRef.current)
            if (!Object.values(selectedItem).length || !selectedItem.id) {
                setSelectedArea([]);
                cacheSelectedAreaRef.current = [];
            } else {
                await getPlatformFenceBindArea({
                    fenceIds: selectedItem.id,
                }).then((rs) => {
                    cacheSelectedAreaRef.current = sortAreaAndLine(rs);
                    setSelectedArea(rs);
                });
            }
            storage.setItem(
                MONITORING_CENTER_FENCE_SELECTED_AREA,
                cacheSelectedAreaRef.current,
            );
            const areaItem = (await getUserPageQueryParameters({
                paramType: MONITORING_CENTER_FENCE_SELECTED_AREA,
            }))?.[0]
            const selected = areaItem?.id;
            const selectedIds = getAreaIds('selected');
            updateOrSaveData(selected, MONITORING_CENTER_FENCE_SELECTED_AREA, selectedIds)
        };

        const onOpenStateChange = async (state: boolean) => {
            cacheSwitchRef.current = state;
            storage.setItem(
                MONITORING_CENTER_FENCE_SWITCH,
                cacheSwitchRef.current,
            );
            const switchItem = (await getUserPageQueryParameters({
                paramType: MONITORING_CENTER_FENCE_SWITCH,
            }))?.[0]
            const fence = switchItem?.id;
            updateOrSaveData(fence, MONITORING_CENTER_FENCE_SWITCH, cacheSwitchRef.current)
            if (!state) {
                setRelatedArea([]);
                setSelectedArea([]);
            } else {
                setRelatedArea(cacheRelatedAreaRef.current);
                setSelectedArea(cacheSelectedAreaRef.current);
            }
        };

        const goBack = () => {
            setListOrDetailShowState(LIST_SHOW);
            // 显示表格时，地图展示之前勾选的围栏的区域、线路
            setRelatedArea(cacheRelatedAreaRef.current);
            setSelectedArea(cacheSelectedAreaRef.current);
        };

        const fitBoundsShape = (idArr: string[]) => {
            // allShapeInstanceMap[id]
            if (!map || !idArr || !idArr.length) return;
            try {
                const latlngs = idArr.map((id: any) => {
                    return allShapeInstanceMap?.[
                        id
                    ]?.[0]?.instance?.getBounds();
                });
                map.fitBounds(latlngs, {
                    maxZoom: 18,
                    padding: [100, 100],
                });
            } catch (error) {
                console.error(error);
            }
        };

        useImperativeHandle(ref, () => ({
            clearFence: onClick,
            onOpenStateChange,
        }));

        const content =
            listOrDetailShowState === LIST_SHOW ? (
                <List
                    onSelectedChange={onSelectedChange}
                    onDetailView={onDetailView}
                    onClick={onClick}
                    panelShow={panelShow}
                    loading={loading}
                    initialSelectedRowKeys={cacheSelectedRowKeysRef.current}
                    initialClickedFence={cacheClickedFenceRef.current}
                    fenceDetailCode={fenceDetailCode}
                    ref={listRef}
                />
            ) : (
                <Detail
                    id={detailId}
                    // @ts-ignore
                    name={detailTile}
                    goBack={goBack}
                    relatedArea={relatedArea}
                    fitBoundsShape={fitBoundsShape}
                    show={show}
                />
            );

        return content;
    },
);

export default FenceList;
