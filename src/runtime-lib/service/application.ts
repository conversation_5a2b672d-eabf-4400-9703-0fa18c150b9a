import { request } from '../request';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

// 应用分页查询
export const fetchApplicationPage = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/application/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
