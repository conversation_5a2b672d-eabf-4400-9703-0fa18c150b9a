// @ts-ignore
import { encryptPassword } from '../utils/general';
import type { CacheType, RequestConfig } from '../request';
import { request } from '../request';
export * from './message';

export const exchangeTenantExtraCode = 3110126;
// 未绑定应用
export const noBindApp = 120020143;

const baseURL = window.APP_CONFIG['gateway.public.url'];
const successCode = 200;
/**
 * 获取用户信息
 * @param setToLoginTime 是否设置异常时跳转登录的等待时间，只有框架主流程加载时才需要设置为 true
 * @param CacheControl 缓存控制，默认仅网络
 * @returns
 */
export const getUserInfo = async (setToLoginTime = false, CacheControl?: CacheType) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/user/info',
        baseURL,
        showFailedMessage: false,
        ...(setToLoginTime ? { toLoginTime: 0 } : {}),
        CacheControl,
    });

    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw {
            code,
            message,
        };
    }

    return data || {};
};

export const getUserConfig = async (userId: number | string, CacheControl?: CacheType) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-business-service/api/v1/configure/efficient',
        params: {
            configureType: 6,
            authId: userId,
            authType: 5,
            // appId: 0, // 租户级别 appId为0
        },
        baseURL,
        showFailedMessage: false,
        CacheControl,
    });

    if (!success || code !== successCode) throw message;

    const ret = {};
    (data?.paramList || []).forEach((item: any) => {
        ret[item.paramName] = item.paramValue;
    });

    return ret;
};
export const transformI18nValue = (value: string | null)=>{
    // i18n翻译时不为 undefined 就不会取默认值
    return (['', null].includes(value) ? undefined : value) as string | undefined;
}
export const getRuntimeLocales = async (type: string, CacheControl?: CacheType, toLoginTime?: number) => {
    const [transition, allByType] = await Promise.all([
        getTransition(type, CacheControl, toLoginTime),
        getAllByType(type, toLoginTime),
    ]);
    if (
        !transition.success ||
        transition.code !== successCode ||
        !allByType.success ||
        allByType.code !== successCode
    )
        throw 'error';
    const ret = {};
    const list = [...(transition.data || []), ...(allByType.data || [])];
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        ret[item.lk] = transformI18nValue(item.tv);
    }

    return ret;
};
const getAllByType = async (type: string, toLoginTime?: number) => {
    return await request({
        method: 'POST',
        url: '/base-server-service/api/v1/language/getAllByType',
        // @ts-ignore
        data: { types: [105, 117], langType: type },
        baseURL,
        showFailedMessage: false,
        toLoginTime
    });
};
export const getTransition = async (type: string, CacheControl?: CacheType, toLoginTime?: number) => {
    return await request({
        method: 'get',
        url: '/base-server-service/api/v1/language/front/list/new',
        params: { langType: type.toUpperCase() },
        baseURL,
        showFailedMessage: false,
        CacheControl,
        toLoginTime
    });
};

export const getAppDetailById = async (appId: string) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/application/detail',
        params: { appId },
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== successCode) throw message;

    return data;
};

export const doLogout = async () => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/user/logout',
        baseURL,
    });

    if (!success || code !== successCode) throw message;

    return data;
};

export const doEditPwd = async (params: any) => {
    const { success, data, message, code } = await request({
        method: 'post',
        url: '/base-server-service/api/v1/user/pwd',
        data: params,
        baseURL,
    });

    if (!success || code !== successCode) throw message;

    return data;
};

// 获取用户配置信息
export interface UserTenantConfig {
    'tenant.user.info.config'?: string;
    'tenant.user.state.config'?: string;
    'tenant.storage.config'?: string;
}
export const getUserTenantConfig = async (params: any, config: RequestConfig = {}) => {
    const { success, message, code, data } = await request<Request.Response<UserTenantConfig>>({
        baseURL,
        url: '/base-server-service/api/v1/tenant/config',
        method: 'GET',
        params,
        ...config
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw {
            code,
            message,
        };
    }
    return data;
};

// 获取租户密码策略
export const fetchTenantPwdConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/config/pwd/query',
        params,
    });
    if (!success || code !== successCode) throw message;
    return data;
};

export const getEnums = async (appId: string, enumName: string) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-config-service/api/v1/enum/type',
        params: { appId, enumName },
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== successCode) throw message;

    return data || {};
};

/**
 * 获取用户服务入口
 * @param setToLoginTime 是否设置异常时跳转登录的等待时间，只有框架主流程加载时才需要设置为 true
 * @param CacheControl 缓存策略，默认仅网络
 * @returns
 */
export const getUserAppEntrys = async (setToLoginTime = false, CacheControl?: CacheType) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/resource/entrance/query',
        baseURL,
        showFailedMessage: false,
        headers: {
            _langType: await window.localStorage.getItem('LANG'),
        },
        ...(setToLoginTime ? { toLoginTime: 0 } : {}),
        CacheControl,
    });

    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw {
            code,
            message,
        };
    }

    return (data.mappers || []).filter((item: any) => !!item.serviceEntrance);
};

export const getTenantWatermarkSetting = async () => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-business-service/api/v1/tenant/watermark/query',
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== successCode) throw message;

    // 1 开启 0 关闭
    return !!data;
};
export const getClientIp = async () => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-config-service/api/v1/ip',
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== successCode) throw message;

    const { ip } = data || {};

    return ip;
};

export const doUploadFile = async (params: any) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/user/info/picture/upload',
        data: params,
        headers: {
            'Content-Type': 'multipart/form-data',
            // @ts-ignore
            _abs: encryptPassword(await window.localStorage.getItem('AUTH_TOKEN')),
        },
    });
    if (code != successCode) {
        throw message;
    }
    return data;
};

export const doEditUser = async (params: any) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/user/info/update',
        data: params,
    });
    if (code != successCode) {
        throw message;
    }
    return data;
};

export const getAreaCode = async () => {
    const { code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/areacode/query',
    });
    if (code != successCode) {
        throw message;
    }
    return data;
};

export const getVerifyCode = async (params: any) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/phone/verify',
        params,
    });
    if (code != successCode) {
        throw message;
    }
    return data;
};

export const doVerifyCode = async (params: any) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/user/verify/phone/auth',
        data: params,
    });
    if (code != successCode) {
        throw message;
    }
    return data;
};

// 将token转换为信令
export const tokenToVoucher = async (params: any) => {
    const { data, code, message } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/user/newSecurityVoucher',
        headers: {
            _token: params._token,
        },
    });
    if (code !== successCode) {
        throw message;
    }
    return data;
};

// 获取登陆配置
export const queryLoginConfig = async (config: RequestConfig = {}) => {
    const domainName =
        location.hostname === 'localhost'
            ? window.APP_CONFIG['gateway.public.url']
                  .replace('/gateway', '')
                  .split(':')[1]
                  .replace('//', '')
            : window.location.hostname;
    const { data, code, message } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/domain/detail',
        params: { domainName: window.encodeURIComponent(`${domainName}`) },
        ...config,
    });
    if (code !== successCode) {
        throw message;
    }
    return data;
};

// 获取用户已收藏的服务入口
export const getCollectedEntrance = async (params: { entrances: any[] }) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/resource/entrance/user/query',
        data: params,
    });
    if (code !== successCode) {
        throw message;
    }
    return data || [];
};

// 收藏服务入口
export const doCollectEntrance = async (data: {
    resourceCode: number;
    appId: number;
    delFlag: 0 | 1; // 0-收藏，1-取消收藏
}) => {
    const {
        data: resultData,
        message,
        code,
    } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/resource/entrance/user/save',
        data,
    });
    if (code !== successCode) {
        throw message;
    }
    return resultData;
};

export const getMessageTaskByIds = async (taskIds: string[]) => {
    const { code, message, success, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/imexport/task/page',
        data: {
            mainTaskIds: taskIds.join(','),
            page: 1,
            pageSize: 100000,
        },
    });

    if (!success || code !== successCode) throw message;

    return data?.list || [];
};
// 获取 s17 文件下载地址
export const getDownloadUrl = async (params?: any) => {
    const headers: any = {};
    if (params.appId !== undefined) {
        headers._appId = params.appId;
    }
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/gss/v1/file/download/url',
        params: { ...params, validTime: 7 },
        headers,
    });
    if (!success || code != successCode) {
        throw message;
    }
    return data;
};

// 证据的文件导出 之前获取文件地址是gss，因为证据mp4合并流程改变之后，下载变成了fms
export const fetchFileUrls = async (params: any, headers?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/fms/v2/file/getUrls',
        params,
        headers,
    });
    if (!success || code != successCode) {
        throw message;
    }
    return data;
};
// 获取email、电话号码脱敏信息
export const getDesensitizationInformation = async (params: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/user/string/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 *
 * * 获取用户信息
 * @param setToLoginTime 是否设置异常时跳转登录的等待时间，只有框架主流程加载时才需要设置为 true
 * @param CacheControl 缓存策略 默认仅网络
 * @returns
 */
// 获取可切换的租户列表
export const getTenantList = async (setToLoginTime = false, CacheControl?: CacheType) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/access/list',
        ...(setToLoginTime ? { toLoginTime: 0 } : {}),
        CacheControl,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取可切换的租户列表
export const getTenantChangeToken = async (params: any) => {
    const { code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/access/change/token',
        showFailedMessage: false,
        params,
    });

    // code 3110126 表示没有访问权限，需要特殊处理
    if (code !== successCode && code !== exchangeTenantExtraCode) {
        throw message;
    }
    return { data, code, message };
};

// 虚拟账户获取真实账户信息
export const getUserRealDetail = async (
    params: any,
    setToLoginTime = false,
    CacheControl?: CacheType,
) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/user/real/detail',
        params,
        ...(setToLoginTime ? { toLoginTime: 0 } : {}),
        CacheControl,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 根据resourceCode查找对应的服务入口
export const getEntryWithResourceCode = async (params: {
    resourceCode: string;
    appId?: number;
}) => {
    const { appId, resourceCode } = params;
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/resource/entrance/find',
        params: {
            resourceCode,
            filterUserPermission: true,
        },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    // [71201] 因为页面可能被多个应用、服务入口关联跳转，接口调整为返回多个可跳转结果的列表
    // 解决资源跳转页面不能跨应用跳转的问题
    let result = {};
    // 若传了appId参数，在接口返回结果中取appId相同的第一个返回
    if (appId || appId === 0) {
        const targets = (data || []).filter((i: any) => i.appId === appId);
        result = targets && targets.length ? targets[0] : {};
    } else {
        // 没有传appId，取返回值中的第一个即可
        result = (data || [])[0] || {};
    }
    return result;
};

// 资源是否存在校验
export const checkRsourceExist = async (params: { resourceList: string[] }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/resource/exist/check',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询参数设置详情
export const fetchParameterDetail = async (params?: any, showFailedMessage = true) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/parameter/setting/detail',
        params,
        showFailedMessage,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 根据国家地区code查询国家地区详情
export const getCountryDetailByCode = async (
    params?: any,
    showFailedMessage = false,
    CacheControl?: CacheType,
) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/area/time/zone/detail',
        params,
        showFailedMessage,
        CacheControl,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 根据国家地区code查询国家地区详情
export const getSummerTimePage = async (
    params?: any,
    showFailedMessage = false,
    CacheControl?: CacheType,
) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/area/time/zone/summer/time/page',
        params,
        showFailedMessage,
        CacheControl,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询租户详情
export const getTenantDetail = async (tenantId?: string) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/detail',
        params: { tenantId },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户参数请求
export const getTenantParamsSetting = async (params?: any, config?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/parameter/setting/value/cache',
        params,
        ...config,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};


export type GeneralLangParams = {
    langType: string;
    includeResource?: boolean,
    appId?: string,
}

// 查询通用多语言
export const getGeneralLang = async (params?: GeneralLangParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/language/front/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
