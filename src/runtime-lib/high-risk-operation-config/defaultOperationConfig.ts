/*
 * @LastEditTime: 2025-07-26 15:55:29
 */
import i18n from '../i18n';
/**
 * 请求数据类型
 */
export type RequestData = Record<string, unknown>;
/**
 * 高风险操作配置项接口
 */
export interface HighRiskOperationConfigItem {
    /** 请求URL，支持精确匹配和通配符匹配 */
    requestUrl: string;
    /** 自定义解析操作描述的函数，接收请求数据返回操作描述 */
    parseOperationDescription: (requestData: RequestData) => string;
    /** 可选的应用ID，用于区分不同应用的配置 */
    appId?: number | string;
}
enum AuthTypeEnum {
    /**车辆授权**/
    VEHICLE = 3,
    /**车组授权**/
    FLEET = 2,
    /**设备授权**/
    DEVICE = 6,
}
export enum FlowModeEnum {
    /**全局**/
    GLOBAL = 1,
    /**精准*/
    ACCURATE = 2,
}
const getAuthTypeMap = (type: AuthTypeEnum, ids: string): string => {
    const number = ids?.split(',')?.length;
    const mapType = {
        [AuthTypeEnum.VEHICLE]: i18n.t('message', '{number}车辆', {
            number: number,
        }),
        [AuthTypeEnum.FLEET]: i18n.t('message', '{number}车组', {
            number: number,
        }),
        [AuthTypeEnum.DEVICE]: i18n.t('message', '{number}设备', {
            number: number,
        }),
    };
    return mapType[type];
};
/**
 * 默认的高风险操作配置
 */
export const defaultHighRiskOperationConfig: HighRiskOperationConfigItem[] = [
    // 切换流量管控模式为情准模式/全局模式 todo
    {
        requestUrl: '/base-server-service/api/v1/application/config/flow',
        parseOperationDescription: (_requestData: RequestData) => {
            const flowConfig = (_requestData.configList || [])?.find(
                (item) => item.key === 'flow.limit.config',
            );
            const flowConfigInfo = JSON.parse(flowConfig?.value || '{}');
            /***接口有两次场景，1切换模式，2编辑报错全局模式***/
            /****没有mode的时候为编辑全局模式**/
            if (!flowConfigInfo.hasOwnProperty('mode')) {
                return i18n.t('message', '修改流量管控开关') as string;
            }
            /***有mode的时候***/
            const modeName =
                flowConfigInfo.mode === FlowModeEnum.GLOBAL
                    ? i18n.t('name', '全局模式')
                    : i18n.t('name', '精准模式');
            return i18n.t('message', '切换流量管控模式为{name}', {
                name: modeName,
            }) as string;
        },
    },
    // 给权限台车批量授权流量设置
    {
        requestUrl:
            '/base-business-service/api/v1/flow/setting/configure/authority',
        parseOperationDescription: (_requestData: RequestData) => {
            if (_requestData.authList) {
                const authList: string[] = [];
                (_requestData.authList || []).forEach((item: any) => {
                    authList.push(getAuthTypeMap(item.authType, item.authIds));
                });
                return i18n.t('message', '给{authData}批量授权流量设置', {
                    authData: authList.join('、'),
                }) as string;
            }
            return '';
        },
    },
    // 修改流量管控开关
    {
        requestUrl: '/base-business-service/api/v1/flow/setting/configure/edit',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '修改流量管控开关') as string;
        },
    },
    // 修改空间限额清理配置
    {
        requestUrl: '/base-flow-service/api/v1/storage/tenant/config',
        parseOperationDescription: (_requestData: RequestData) => {
            const flowConfig = (_requestData.configList || [])?.find(
                (item) => item.key === 'tenant.storage.config',
            );
            return flowConfig ? i18n.t('message', '修改空间覆盖清理配置') : '';
        },
    },
    // 封停子租户
    {
        requestUrl: '/base-server-service/api/v1/tenant/lock',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '封停子租户') as string;
        },
    },
    // 解封子租户
    {
        requestUrl: '/base-server-service/api/v1/tenant/unlock',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '解封子租户') as string;
        },
    },
    // 删除子租户
    {
        requestUrl: '/base-server-service/api/v2/tenant/delete',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '删除子租户') as string;
        },
    },
    // 更换租户管理员 todo
    {
        requestUrl: '/base-server-service/api/v1/tenant/manager/change',
        parseOperationDescription: (_requestData: RequestData) => {
            /***租户管理员和子租户管理员都是同一个接口，只能通过路由判断***/
            const pathname = location.pathname.split('/');
            if (pathname.includes('tenant-detail')) {
                return i18n.t('message', '更换租户管理员') as string;
            }
            return i18n.t('message', '更换子租户管理员') as string;
        },
    },
    // 重置子租户管理员密码
    {
        requestUrl:
            '/base-server-service/api/v1/email/reset/tenant/admin/password',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '重置子租户管理员密码') as string;
        },
    },
    // 删除证据视频 - 清理创建 todo
    {
        requestUrl: '/base-alarm-service/api/v2/evidence/clean/create',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '删除{number}个证据视频', {
                number: 0,
            }) as string;
        },
    },
    // 删除证据视频 - 批量删除 todo
    {
        requestUrl: '/base-alarm-service/api/v1/evidence/batch/delete',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '删除{number}个证据视频', {
                number: _requestData?.evidenceIds?.split(',').length,
            }) as string;
        },
    },
    // 删除车辆
    {
        requestUrl: '/base-server-service/api/v1/vehicle/delete',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '删除{number}台车', {
                number: _requestData?.vehicleIds?.split(',').length,
            }) as string;
        },
    },
    // 解绑License
    {
        requestUrl: '/base-server-service/api/v1/license/device/unbinding',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '解绑License') as string;
        },
    },
    // 删除车组
    {
        requestUrl: '/base-server-service/api/v1/fleet/delete',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '删除{number}车组', {
                number: _requestData?.fleetIds?.split(',').length,
            }) as string;
        },
    },
    // 解绑设备
    {
        requestUrl: '/base-server-service/api/v2/vehicle/device/unbinding',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '解绑设备') as string;
        },
    },
    // 换绑设备
    {
        requestUrl: '/base-server-service/api/v2/vehicle/primary/device/change',
        parseOperationDescription: (_requestData: RequestData) => {
            return i18n.t('message', '换绑设备') as string;
        },
    },
];
