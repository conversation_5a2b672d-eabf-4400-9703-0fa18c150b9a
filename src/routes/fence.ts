import { dynamic } from '@base-app/runtime-lib';

export default [
    // 区域管理
    {
        path: '/fence/area-manage/add',
        component: dynamic(() => import('../runtime-pages/fence/area/action')),
        exact: true,
    },
    {
        path: '/fence/area-manage/edit/:shapeId',
        component: dynamic(() => import('../runtime-pages/fence/area/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/area-manage/copy/:shapeId',
        component: dynamic(() => import('../runtime-pages/fence/area/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/area-manage',
        component: dynamic(() => import('../runtime-pages/fence/area')),
        exact: true,
    },
    {
        path: '/fence/area-manage/detail/:shapeId/:tab?',
        component: dynamic(() => import('../runtime-pages/fence/area/detail')),
        exact: true,
        enablePageLoading: true,
    },

    // 线路管理
    {
        path: '/fence/line-manage/add',
        component: dynamic(() => import('../runtime-pages/fence/line/action')),
        exact: true,
    },
    {
        path: '/fence/line-manage/edit/:shapeId',
        component: dynamic(() => import('../runtime-pages/fence/line/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/line-manage/copy/:shapeId',
        component: dynamic(() => import('../runtime-pages/fence/line/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/line-manage',
        component: dynamic(() => import('../runtime-pages/fence/line')),
        exact: true,
    },
    {
        path: '/fence/line-manage/detail/:shapeId/:tab?',
        component: dynamic(() => import('../runtime-pages/fence/line/detail')),
        exact: true,
        enablePageLoading: true,
    },

    {
        path: '/fence/base',
        component: dynamic(() => import('../runtime-pages/fence/base')),
        exact: true,
    },
    {
        path: '/fence/base/add',
        component: dynamic(() => import('../runtime-pages/fence/base/action')),
        exact: true,
    },
    {
        path: '/fence/base/edit/:eventId',
        component: dynamic(() => import('../runtime-pages/fence/base/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/base/detail/:eventId/:tab?',
        component: dynamic(() => import('../runtime-pages/fence/base/detail')),
        exact: true,
    },

    {
        path: '/fence/speed',
        component: dynamic(() => import('../runtime-pages/fence/speed')),
        exact: true,
    },
    {
        path: '/fence/speed/add',
        component: dynamic(() => import('../runtime-pages/fence/speed/action')),
        exact: true,
    },
    {
        path: '/fence/speed/edit/:eventId',
        component: dynamic(() => import('../runtime-pages/fence/speed/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/speed/detail/:eventId/:tab?',
        component: dynamic(() => import('../runtime-pages/fence/speed/detail')),
        exact: true,
    },

    {
        path: '/fence/time',
        component: dynamic(() => import('../runtime-pages/fence/time')),
        exact: true,
    },
    {
        path: '/fence/time/add',
        component: dynamic(() => import('../runtime-pages/fence/time/action')),
        exact: true,
    },
    {
        path: '/fence/time/edit/:eventId',
        component: dynamic(() => import('../runtime-pages/fence/time/action')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence/time/detail/:eventId/:tab?',
        component: dynamic(() => import('../runtime-pages/fence/time/detail')),
        exact: true,
    },
    {
        path: '/fence-monitor',
        component: dynamic(() => import('../runtime-pages/fence-monitor')),
        exact: true,
    },
    {
        path: '/fence-monitor/add',
        component: dynamic(() => import('../runtime-pages/fence-monitor/add')),
        exact: true,
    },
    {
        path: '/fence-monitor/edit',
        component: dynamic(() => import('../runtime-pages/fence-monitor/edit')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/fence-monitor/detail',
        component: dynamic(() => import('../runtime-pages/fence-monitor/detail')),
        exact: true,
        enablePageLoading: true,
    },
];
