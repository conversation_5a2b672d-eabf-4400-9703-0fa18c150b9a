/*
 * @LastEditTime: 2025-04-12 14:47:59
 */
import { dynamic } from '@base-app/runtime-lib';

export default [
    // 流量中心-流量预览
    {
        path: '/flow/flow-preview',
        component: dynamic(() => import('../runtime-pages/flow-center/flow-preview/index')),
        exact: true,
    },
    // 流量中心-流量管理
    {
        path: '/flow/flow-manage',
        component: dynamic(() => import('../runtime-pages/flow-center/flow-manage/index')),
        exact: true,
    },
    {
        path: '/flow/flow-manage/detail',
        component: dynamic(() => import('../runtime-pages/flow-center/flow-manage/detail')),
        exact: true,
    },
    // 流量设置-主页
    {
        path: '/flow/flow-setting',
        component: dynamic(() => import('../runtime-pages/flow-center/flow-setting')),
        exact: true,
    },
    // 流量设置-精准控制-添加
    {
        path: '/flow/flow-setting/add',
        component: dynamic(() => import('../runtime-pages/strategy-center/precise-control/add')),
        exact: true,
    },
    // 流量设置-精准控制-复制
    {
        path: '/flow/flow-setting/copy',
        component: dynamic(() => import('../runtime-pages/strategy-center/precise-control/add')),
        exact: true,
        enablePageLoading: true,
    },
    // 流量设置-精准控制-编辑
    {
        path: '/flow/flow-setting/edit',
        component: dynamic(() => import('../runtime-pages/strategy-center/precise-control/edit')),
        exact: true,
        enablePageLoading: true,
    },
    // 流量设置-精准控制-排序

    {
        path: '/flow/flow-setting/sort/:appId',
        component: dynamic(() => import('../runtime-pages/strategy-center/base/sort')),
        exact: true,
    },
    // 流量设置-精准控制-详情
    {
        path: '/flow/flow-setting/detail',
        component: dynamic(() => import('../runtime-pages/strategy-center/precise-control/detail')),
        exact: true,
    },
    // 用户流量管理
    {
        path: '/flow/user-flow-manage',
        component: dynamic(() => import('../runtime-pages/flow-center/user-flow-manage/index')),
        exact: true,
    },
    // 用户日流量明细
    {
        path: '/flow/user-flow-manage/user-flow-detail',
        component: dynamic(() => import('../runtime-pages/flow-center/user-flow-manage/detail')),
        exact: true,
    },
];