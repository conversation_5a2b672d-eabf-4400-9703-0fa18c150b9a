/*
 * @LastEditTime: 2024-05-10 11:04:30
 */
import { dynamic } from '@base-app/runtime-lib';

export default [
    // 驾驶员管理
    {
        path: '/driver-manage',
        component: dynamic(() => import('../runtime-pages/driver-manage')),
        exact: true,
    },
    // 驾驶员详情
    {
        path: '/driver-manage/detail',
        component: dynamic(() => import('../runtime-pages/driver-manage/driver-detail')),
        exact: true,
        enablePageLoading: true,
    },
    // 添加驾驶员
    {
        path: '/driver-manage/add-driver',
        component: dynamic(() => import('../runtime-pages/driver-manage/add-driver')),
        exact: true,
    },
    // 编辑驾驶员
    {
        path: '/driver-manage/edit-driver',
        component: dynamic(() => import('../runtime-pages/driver-manage/add-driver')),
        exact: true,
        enablePageLoading: true,
    },
    {
        path: '/evidence-list',
        component: dynamic(() => import('../runtime-pages/evidence-list')),
        exact: true,
    },
    // 驾驶员导入
    {
        path: '/driver-manage/import',
        component: dynamic(() => import('../runtime-pages/import-page')),
        exact: true,
    },
];