import { FormateType } from '@/types/pageReuse/trackPlayback';
import { MosaicTypeEnum } from '@/runtime-lib';

export type PanelType = 'alarm' | 'park';
export type PageOrigin = 'risk' | 'track' | 'device' | 'server' | 'mix';
interface ParkData {
    startTime?: number;
    endTime?: number;
    isStart?: boolean;
    isEnd?: boolean;
}
interface AlarmData {
    alarmName?: string;
    alarmId: string;
    alarmType?: string;
}
export interface RenderMapPanelParams {
    data: PanelData;
    type: PanelType;
    map: any;
    L: any;
    resourceCode: MosaicTypeEnum;
    hasWatermark?: boolean;
    hasHeadVideo?: boolean;
    pageOrigin?: PageOrigin | null;
    videoParam?: VideoParam;
    onClickAlarmFun?: string;
    onloadedmetadataFun?: string;
    customFormateSeconds?: ((time: number, type: FormateType) => React.ReactNode) | undefined;
}
export interface PanelData extends ParkData, Omit<AlarmData, 'alarmId'> {
    lng: number;
    lat: number;
    resourceCode: MosaicTypeEnum;
    deviceId: number;
    authId?: string;
    alarmId: string;
    vehicleId: string;
}
export interface PopupDataParams extends PanelData {
    pageOrigin?: PageOrigin | null | undefined;
    videoParam?: VideoParam;
    onClickAlarmFun?: string;
    onloadedmetadataFun?: string;
    type?: PanelType;
    html: string;
    icon?: any;
    map?: any;
    L: any;
    hasWatermark?: boolean;
    hasHeadVideo?: boolean;
}
export interface MediaSource {
    url: string | string[] | null;
    fileSize: number | null;
    type: 'alarm' | 'park';
    alarmId: string;
    duration: string;
    fileType?: number | string;
    channelNo?: number | string;
}

export interface VideoParam {
    videoDomId?: string;
    videoLengthDomId?: string;
}
