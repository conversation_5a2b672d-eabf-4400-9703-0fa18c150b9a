import { Button, Space, message, Input, Select, Tooltip, Form } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import IconAdd from '@streamax/poppy-icons/lib/icons/IconAdd';
import IconDelete from '@streamax/poppy-icons/lib/icons/IconDelete';
import IconEdit from '@streamax/poppy-icons/lib/icons/IconEdit';
import IconRequest from '@streamax/poppy-icons/lib/icons/IconRequest';
import {
    i18n,
    Auth,
    utils,
    useUrlSearchStore,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import {
    StarryModal,
    StarryBreadcrumb,
    StarryCard,
    StarryTable,
    FleetTreeSelect,
    // @ts-ignore
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import React, { useEffect, useState, useRef } from 'react';
import { postQueryDriverList, deleteDriver } from '../../service/driver';
import AuthFleetShow from '@/components/AuthFleetShow';
import './index.less';

const DriverManage = () => {
    const tableRef = useRef<any>();
    const initData: any[] = [];
    const [selectedDrivers, setSelectedDrivers] = useState(initData);
    const history = useHistory();
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const {
        pageSize,
        page,
        state,
        fleetIds,
        includeSubFleet,
        appId,
        complexSort,
        complexSortName,
        ...formQuery
    } = searchStore.get();

    const driverState = [
        {
            text: i18n.t('state', '在职'),
            value: 1,
            status: 'success',
        },
        {
            text: i18n.t('state', '离职'),
            value: 0,
            status: 'default',
        },
    ];

    useEffect(() => {
        form.setFieldsValue({
            ...formQuery,
            state: state ? Number(state) : null,
            companyIds: { fleetIds, includeSubFleet: includeSubFleet ?? 1 },
            appId: appId ? Number(appId) : null,
        });
    }, []);

    const fetchData = (params: any) => {
        const searchStoreParams = {
            ...params,
            fleetIds: params.companyIds?.fleetIds,
            includeSubFleet: params.companyIds?.includeSubFleet,
            complexSortName:
                params.complexSort?.indexOf('createTime') > -1 ? 'createTime' : 'updateTime',
        };
        searchStore.set(searchStoreParams);
        // 每次重新拉取数据时都取消之前得勾选数据
        setSelectedDrivers([]);
        const reqParams = {
            ...params,
            ...params.companyIds,
            fields: 'fleet',
            needDriverPicture: false,
        };
        delete reqParams.driverName;
        if (params.driverName?.trim()) {
            reqParams.driverNameOrJobNumber = params.driverName?.trim();
        }
        return postQueryDriverList(reqParams);
    };
    const handleRowSelectChange = (selectedKeys: any[], selectedRows: any[]) => {
        setSelectedDrivers(selectedRows);
    };

    const jumpToAddDriver = (row: any = {}) => {
        let url = '';
        if (row.driverId) {
            url = `/driver-manage/edit-driver?driverId=${row.driverId}`;
        } else {
            url = `/driver-manage/add-driver`;
        }
        history.push(url);
    };
    const deleteDriverConfirm = (driverId: string | undefined,driverName?: string) => {
        let content;
        if (!driverId) {
            content = i18n.t('message', '确认要删除已选司机姓名的司机吗？');
            if (!selectedDrivers.length) {
                message.warning(i18n.t('message', '请选择删除项'));
                return;
            }
        }else{
            content = i18n.t('message', '确认要删除“{name}”司机吗？', { name: driverName });
        }
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content,
            okText: i18n.t('name', '确定'),
            cancelText: i18n.t('name', '取消'),
            closable: true,
            icon: <IconRequest />,
            onOk: async () => {
                let driverIds: string[];
                if (!driverId) {
                    driverIds = selectedDrivers.map((driver: any) => driver.driverId as string);
                } else {
                    driverIds = [driverId];
                }
                await deleteDriver({ driverIds: driverIds.join(',') });
                message.success(i18n.t('message', '操作成功'));
                tableRef.current?.reload();
                setSelectedDrivers([]);
            },
        });
    };
    const formatStatus = (state: any) => {
        if (state === 1) {
            return (
                <Space>
                    <div className="work-state on-work" />
                    {i18n.t('state', '在职')}
                </Space>
            );
        }
        return (
            <Space>
                <div className="work-state off-work" />
                {i18n.t('state', '离职')}
            </Space>
        );
    };
    const columns: any[] = [
        {
            title: i18n.t('name', '司机姓名'),
            dataIndex: 'driverName',
            key: 'driverName',
            ellipsis: { showTitle: false },
            render: (item: any, row: any) => {
                return (
                    <Tooltip title={item}>
                        <StarryAbroadOverflowEllipsisContainer>
                            <a
                                onClick={() => {
                                    history.push(`/driver-manage/detail?driverId=${row.driverId}`);
                                }}
                            >
                                {item}
                            </a>
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '司机工号'),
            dataIndex: 'jobNumber',
            key: 'jobNumber',
            ellipsis: { showTitle: false },
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'state',
            key: 'state',
            filtered: true,
            render: (text: any) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>{formatStatus(text)}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'companyNames',
            key: 'companyNames',
            ellipsis: { showTitle: false },
            render: (text: any, row: any) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            <AuthFleetShow fleetList={row.fleetList} />
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'createUserName',
            key: 'createUserName',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: { showTitle: false },
            sorter: true,
            defaultSortOrder:
                complexSort &&
                complexSortName == 'createTime' &&
                (complexSort == 'orderBy createTime asc' ? 'ascend' : 'descend'),
            render: (text: any) => {
                return (
                    <Tooltip title={utils.formator.zeroTimeStampToFormatTime(text) || '-'}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {utils.formator.zeroTimeStampToFormatTime(text) || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'updateUserName',
            key: 'updateUserName',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作时间'),
            dataIndex: 'updateTime',
            key: 'updateTime',
            ellipsis: { showTitle: false },
            sorter: true,
            defaultSortOrder:
                complexSort &&
                complexSortName == 'updateTime' &&
                (complexSort == 'orderBy updateTime asc' ? 'ascend' : 'descend'),
            render: (text: any) => {
                return (
                    <Tooltip title={utils.formator.zeroTimeStampToFormatTime(text) || '-'}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {utils.formator.zeroTimeStampToFormatTime(text) || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            key: 'action',
            width: 120,
            fixed: 'right',
            render: (item: any, row: any) => (
                <div style={{ paddingRight: 16 }}>
                    <Space size={24}>
                        <Auth code="@base:@page:driver.manage@action:edit">
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <a
                                    onClick={() => {
                                        jumpToAddDriver(row);
                                    }}
                                >
                                    <IconEdit />
                                </a>
                            </Tooltip>
                        </Auth>
                        <Auth code="@base:@page:driver.manage@action:batch.delete">
                            <Tooltip title={i18n.t('action', '删除')}>
                                <a
                                    onClick={() => {
                                        deleteDriverConfirm(row.driverId,row.driverName);
                                    }}
                                >
                                    <IconDelete />
                                </a>
                            </Tooltip>
                        </Auth>
                    </Space>
                </div>
            ),
        },
    ];
    const formItems: QueryFormProps['items'] = [
        {
            label: i18n.t('name', '司机'),
            name: 'driverName',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入司机姓名或司机工号'),
                maxLength: 50,
            },
            itemProps: {
                rules: [{ max: 50, type: 'string' }],
            },
        },
        {
            label: i18n.t('name', '状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择司机状态'),
                options: driverState.map(({ text, value }) => ({
                    value,
                    label: text,
                })),
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'companyIds',
            field: FleetTreeSelect,
        },
    ];

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="driver-manage-container">
                    <div className="driver-manage-container-ZT">
                        <StarryTable
                            fetchDataAfterMount
                            aroundBordered
                            fetchDataFunc={fetchData}
                            ref={tableRef as any}
                            // bordered
                            columns={columns}
                            pagination={{
                                defaultCurrent: Number(page) || 1,
                                defaultPageSize: Number(pageSize) || 20,
                            }}
                            queryProps={{
                                items: formItems,
                                form,
                            }}
                            toolbar={{
                                iconBtns: ['reload', 'column-setting'],
                                leftRender: () => (
                                    <>
                                        <Auth code="@base:@page:driver.manage@action:add">
                                            <Button type="primary" onClick={jumpToAddDriver}>
                                                <IconAdd />
                                                {i18n.t('action', '添加')}
                                            </Button>
                                        </Auth>
                                        <Auth code="@base:@page:driver.manage@action:batch.delete">
                                            <Button
                                                onClick={() => {
                                                    deleteDriverConfirm(undefined);
                                                }}
                                                disabled={selectedDrivers.length === 0}
                                            >
                                                <IconDelete />
                                                {i18n.t('action', '批量删除')}
                                            </Button>
                                        </Auth>
                                    </>
                                ),
                                columnSetting: {
                                    storageKey: '@base:@page:driver.manage',
                                    disabledKeys: ['driverName', 'action'],
                                },
                            }}
                            rowSelection={{
                                selectedRowKeys: selectedDrivers.map(
                                    (driver) => driver.driverId as string,
                                ),
                                onChange: handleRowSelectChange,
                            }}
                            rowKey={'driverId'}
                        />
                    </div>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default DriverManage;
