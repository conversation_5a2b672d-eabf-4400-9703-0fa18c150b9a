@import "~@streamax/poppy-themes/starry/index.less";
@import '~@streamax/poppy-themes/starry/abroad.less';
.issue-upload .poppy-upload {
    display: inline-block;
    width: 104px;
    height: 104px;
    margin: 0 16px 16px 0;
    background:@starry-bg-color-secondarycontainer;

    .upload-picker {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
}

.driver-upload-preview {
    position: relative;
    width: 104px;
    height: 104px;
    margin: 0 16px 24px 0;
    padding: 8px;
    border: 1px solid @starry-border-level-2-color;
    border-radius: 2px;
    img {
        width: 100%;
        height: 100%;
    }

    .score-tip-bg {
        position: absolute;
        top: 8px;
        right: 8px;
    }
    .score-tip-title {
        position: absolute;
        top: 7.5px;
        right: 7.5px;
        display: inline-block;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: @starry-text-color-primary;
        font-size: 10px;
        transform: rotate(45deg);
    }

    .driver-upload-preview-btns {
        display: none;
    }
    .driver-upload-testing-btns {
        position: absolute;
        top: 50%;
        left: 50%;
        display: block;
        width: fit-content;
        color: #fff;
        transform: translate3d(-50%, -50%, 0);
        transition: all 0.3s;
        .loading-icon {
            padding-left: 20px;
        }
    }
    .mask-layer {
        position: absolute;
        top: 8px;
        left: 8px;
        display: none;
        width: 86px;
        height: 86px;
        background: rgba(0, 0, 0, 0.8);
    }
    .testing-mask-layer {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 86px;
        height: 86px;
        background: rgba(0, 0, 0, 0.65);
    }

    &:hover {
        .driver-upload-preview-btns {
            position: absolute;
            top: 50%;
            left: 50%;
            display: block;
            color: #fff;
            transform: translate3d(-50%, -50%, 0);
            transition: all 0.3s;
        }

        .mask-layer {
            display: block;
        }
    }
}
.driver-upload-preview::abroad {
    border-radius: @border-radius-8;
}
.issue-upload-list {
    flex-wrap: wrap;
    .fail-describe {
        position: absolute;
        width: -webkit-fill-available;
        // clear: both;
        margin-top: 40px;
        margin-left: -108px;
        color: #ff4d4f;
    }
    .fail-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: -4px;
    }
}

.tip-text {
    margin-top: 16px;
    margin-bottom: -10px;
    color: @starry-text-color-secondary;

    .tip-context {
        position: relative;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.upload-preview-modal {
    .poppy-modal-body {
        padding: 24px;
    }
}
