import React, { useEffect, useState, useRef } from 'react';
// @ts-ignore
import { Form, Input, Button, Space, message, Row, Col, Divider, Select } from '@streamax/poppy';
import AuthSelect from '@/components/AuthSelectShow';
import {
    StarryBreadcrumb,
    StarryCard,
    StarryInfoBlock,
    StarryModal,
    //@ts-ignore
    Action,
} from '@base-app/runtime-lib';
import { useSubmitFn } from '@streamax/hooks';
import { useHistory } from '@base-app/runtime-lib/core';
import {
    utils,
    i18n,
    getAppGlobalData,
    RouterPrompt,
    StarryStorage,
    StarryAbroadFormItem as AFormItem,
    StarryAbroadLRLayout,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import CompanyRequest from '../../../service/company';
import { fetchApplicationConfig } from '../../../service/application';
import { getDriverDetail, addDriver, addAbnormalDriver, editDriver } from '../../../service/driver';
import { getVehiclerListByPage } from '../../../service/vehicle';
import { resultCode } from '../constant';
import AuthTip from '@/components/AuthTip';
import { getAuthFleetList, getAuthVehicleList } from '@/utils/commonFun';
import Uploader from './Uploader';
// @ts-ignore
import FleetTreeSelect from '@/components/AuthFleetSelectShow';
// import {FleetTreeSelect} from '@base-app/runtime-lib';
import { EnumDomainType, SourceFleetType } from '../../../types/company';
import usePhotoNumber from '@/hooks/usePhotoNumber';
import { useUpdate } from '@streamax/hooks';
import { IconRequest } from '@streamax/poppy-icons';
import './index.less';
import { RspFormLayout } from '@streamax/responsive-layout';

interface fleetItem {
    [propName: number | string]: any;
}

interface QualityPictureType {
    pictureId?: string;
    pictureSource?: string;
    resultCode?: string;
    score?: string | number;
}

interface FleetItem {
    key: string;
    title: string;
    value: string;
    parentId: string;
    disabled?: string;
}

const Driver = (props: any) => {
    const storage = StarryStorage();
    const { driverId, pictureList, recordId, recordIdFormList, handleType } = props.location.query;
    const [groupNumber, setGroupNumber] = useState(20);
    const [vehicleNumber, setVehicleNumber] = useState(0);
    const [isRelated, setIsRelated] = useState(false); // 关联配置
    const [faceCompare, setFaceCompare] = useState(false); // 是否开启人脸对比
    const [originVehicles, setOriginVehicles] = useState<any[]>([]);
    const [vehicleOptions, setVehicleOptions] = useState<any[]>([]);
    const [picList, setPicList] = useState<any[]>([]);
    const [when, setWhen] = useState(true);
    const update = useUpdate();
    // const arrayToTree = utils.general.arrayToTree;
    const validatorIllegalCharacter = utils.validator.illegalCharacter;
    const validateBaseData = utils.validator.validateBaseData;

    const fleetTreeEleRef = useRef<fleetItem>({});
    const vehicleEleRef = useRef<any>({});
    const { maxPhotoNumber } = usePhotoNumber();
    const [fleetDisable, setFleetDisable] = useState(false);
    const [vehicleDisable, setVehicleDisable] = useState(false);
    const [form] = Form.useForm();
    const history = useHistory();
    const { isAbroadStyle } = useSystemComponentStyle();

    const fetchGroupDataSource = (key?: number) => {
        return async () => {
            const idItems = form.getFieldValue('fleetIds');
            const ids = idItems
                .map((item: { fleetIds: string }) => item?.fleetIds)
                .filter((item: string) => {
                    if (item) {
                        return item;
                    } else {
                        return false;
                    }
                });
            // 新增时会把ids数组初始化的undefined删掉，变成一个空数组
            // 编辑时根据传进来的key把当前项删掉，不禁用当前项。
            ids.splice(key, 1); // 删除当前项的id,下一步会把所有id项禁用，不禁用当前项(修改时可再次选中当前项，不改变)
            const list = await CompanyRequest.getList({
                domainType: EnumDomainType.touchFleet,
                ...utils.general.getSortParam(),
            });
            const flatData: FleetItem[] = list.map((x: SourceFleetType) => {
                return {
                    key: x['key'],
                    title: x['title'],
                    value: x['value'],
                    parentId: x['parentId'],
                    disabled: ids.includes(x['id']),
                };
            });
            return flatData;
        };
    };

    const setQualityPic = (list: any[]) => {
        setPicList(list);
    };

    const onFormValuesChange = (changedValues: any) => {
        if (changedValues['fleetIds']) {
            for (const key in fleetTreeEleRef.current) {
                // if (fleetTreeEleRef.current[key]) {
                fleetTreeEleRef.current[key]?.loadData();
                // }
            }
        }
        if (changedValues['vehicleIds']) {
            const ids = form.getFieldValue('vehicleIds');
            const data = originVehicles.map((item) => {
                return {
                    ...item,
                    disabled: (ids || []).includes(item.vehicleId),
                };
            });
            setVehicleOptions(data);
        }
    };
    useEffect(() => {
        fetchInitFleet();
        fetchApplicationConfig({
            appId: getAppGlobalData('APP_ID'),
            keys: 'driver.fleet.max,driver.vehicle.max,driver.binding.dynamic,driver.face.compare.plateform,driver.dynamic.compare.face.ibutton,driver.face.compare.device',
        }).then((data: any) => {
            const fleetValue = (data || []).find(
                (p: any) => p.configKey === 'driver.fleet.max',
            )?.value;
            setGroupNumber(1);
            if (!fleetValue || fleetValue === '-1') {
                setGroupNumber(1);
            } else {
                setGroupNumber(Number(fleetValue));
            }
            const vehicleValue = (data || []).find(
                (p: any) => p.configKey === 'driver.vehicle.max',
            )?.value;

            const dynamic = (data || []).find(
                (p: any) => p.configKey === 'driver.binding.dynamic',
            )?.value;
            const plateFace = (data || []).find(
                (p: any) => (p.configKey === 'driver.face.compare.plateform'),
            )?.value;
            const deviceFace = (data || []).find(
                (p: any) => p.configKey === 'driver.face.compare.device',
            )?.value;
            const ibuttonFace = (data || []).find(
                (p: any) => (p.configKey === 'driver.dynamic.compare.face.ibutton'),
            )?.value;

            const formatValue = vehicleValue && JSON.parse(vehicleValue);

            const plateFaceValue = (plateFace && JSON.parse(plateFace)) || {};
            const ibuttonFaceValue = (ibuttonFace && JSON.parse(ibuttonFace)) || {};
            const deviceFaceValue = (deviceFace && JSON.parse(deviceFace)) || {};
            const dynamicValue = (dynamic && JSON.parse(dynamic)) || {};

            setIsRelated(formatValue?.isOpen == 0 ? false : true);

            setFaceCompare(
                dynamicValue.isOpen && (plateFaceValue.isOpen || deviceFaceValue.isOpen || ibuttonFaceValue.isOpen)
                    ? true
                    : false,
            );
            if (!formatValue.value) {
                setVehicleNumber(0); // 关闭关联配置
            } else if (formatValue.value === -1) {
                setVehicleNumber(999); // 无限刷
            } else {
                setVehicleNumber(Number(formatValue.value));
            }
        });
        getVehiclerListByPage(
            {
                page: 1,
                pageSize: 1e8,
                appId: getAppGlobalData('APP_ID'),
                queryVehicleStatus: 0, //不查询车辆状态，接口太慢
            },
            true,
        ).then((rs) => {
            setOriginVehicles(rs.list || []);
        });
    }, []);

    // 编辑时获取初始的车组数据
    const fetchInitFleet = async () => {
        if (driverId) {
            const rs = await getDriverDetail({ driverId, fields: 'fleet' });
            const { fleetList, authDisable } = getAuthFleetList(rs);
            setFleetDisable(authDisable);
            if (fleetList.length) {
                rs.fleetIds = fleetList.map((companyItem: any) => {
                    return {
                        fleetIds: companyItem.fleetId,
                        includeSubFleet: 0,
                    };
                });
            } else {
                rs.fleetIds = [undefined]; // 初始化渲染一个空的选择框，如果是空数组则不会渲染空的下拉框(会根据该字段数组的长度渲染选择框的个数)
            }
            form.setFieldsValue({ ...rs });
            onFormValuesChange({ fleetIds: [...(rs.fleetIds || [])] });
        } else {
            form.setFieldsValue({
                fleetIds: [undefined],
            });
            onFormValuesChange({ fleetIds: [] });
        }
    };

    useEffect(() => {
        if (pictureList) {
            const list = (pictureList || '').split(',').filter((p: any) => p);
            form.setFieldsValue({
                picture: list,
            });
            // 调转过来图片不存在，记录id存在则提示
        } else if (recordIdFormList) {
            const modal = StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '未知司机图片中未找到符合人脸库要求照片，请手动添加'),
                okText: i18n.t('name', '我知道了'),
                wrapClassName: 'add-driver-confirm',
                closable: true,
                onOk: () => {
                    modal?.destroy();
                },
            });
        }
    }, [pictureList]);

    useEffect(() => {
        (async () => {
            // 编辑从接口获取数据
            if (driverId) {
                const rs = await getDriverDetail({ driverId, fields: 'vehicle' });
                const { vehicleList, authDisable } = getAuthVehicleList(rs);
                setVehicleDisable(authDisable);
                if (vehicleList.length) {
                    // 之前关联过 则展示关联车辆
                    setIsRelated(true);
                    rs.vehicleIds = vehicleList.map((vehicleItem: any) => vehicleItem.vehicleId); //[["141778596078315300", '1417785960783153002',"1417785960783153004"]] //
                } else {
                    rs.vehicleIds = [undefined];
                }
                form.setFieldsValue({
                    ...rs,
                    state: `${rs.state}`,
                    picture: (rs.picture || '').split(',').filter((p: any) => p),
                });
                onFormValuesChange({ vehicleIds: [...(rs.vehicleIds || [])] });
            } else {
                form.setFieldsValue({
                    vehicleIds: [undefined],
                });
                onFormValuesChange({ vehicleIds: [] });
            }
        })();
    }, [originVehicles]);
    const [submit, submitLoading] = useSubmitFn(async (values: any) => {
        const QualityPictureList: QualityPictureType[] = [];
        (picList || []).forEach((item: any) => {
            if (item.code == resultCode['successCode'] && item.id) {
                QualityPictureList.push({
                    pictureId: item.id,
                    pictureSource: item.pictureSource,
                    resultCode: item.code,
                    score: item.score,
                });
            }
        });
        QualityPictureList.filter((item) => item);
        const fleetIds = values.fleetIds.map(
            (fleetItem: { fleetIds: string }) => fleetItem.fleetIds,
        );

        const requestValues = {
            ...values,
            driverId,
            pictureList: QualityPictureList,
            fleetIds,
            recordId: recordId || recordIdFormList,
            handleType: handleType,
        };
        delete requestValues.picture;
        setWhen(false);
        if (driverId) {
            await editDriver(requestValues);
            message.success(i18n.t('message', '成功'));
            form.resetFields();
            history.goBack();
        } else {
            const detailId = pictureList
                ? await addAbnormalDriver(requestValues)
                : await addDriver(requestValues);
            // const detailId =await addDriver(values);

            // 人脸异常代办 注册新司机后回退到人脸异常代办列表页
            if (detailId && pictureList) {
                message.success(i18n.t('message', '成功'));
                (recordId || recordIdFormList) &&
                    storage.sessionStorage.setItem('handledRecordId', recordId || recordIdFormList);
                history.goBack();
                return;
            }
            if (detailId) {
                message.success(i18n.t('message', '成功'));
                form.resetFields();
                Action.openActionUrl({
                    code: '@base:@page:driver.manage.add@action:view.detail',
                    // @ts-ignore
                    history,
                    url: `/driver-manage/detail`,
                    params: {
                        driverId: detailId,
                    },
                });
            }
        }
    });
    const getVehicleOptions = () => {
        const data = vehicleOptions.map((item) => {
            return {
                label: item.vehicleNumber,
                value: item.vehicleId,
                disabled: item.disabled,
            };
        });
        return data;
    };

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard title={i18n.t('name', '司机信息')}>
                <Form
                    layout="vertical"
                    form={form}
                    onValuesChange={onFormValuesChange}
                    className="add-driver-form-container"
                    onFinish={submit}
                >
                    <StarryInfoBlock title={i18n.t('name', '基本信息')}>
                        <div className="add-driver-form-block-wrap">
                            <RspFormLayout layoutType='auto'>
                            <AFormItem
                                label={i18n.t('name', '司机名称')}
                                name="driverName"
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: i18n.t('message', '请输入司机名称'),
                                    },
                                    {
                                        validator: validateBaseData,
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    maxLength={50}
                                    placeholder={i18n.t('message', '请输入司机名称')}
                                />
                            </AFormItem>
                            <AFormItem
                                label={i18n.t('name', '司机工号')}
                                name="jobNumber"
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: i18n.t('message', '请输入司机工号'),
                                    },
                                    {
                                        validator(_: any, value: any) {
                                            if (value && !/^[A-Za-z0-9.-]+$/.test(value)) {
                                                return Promise.reject(
                                                    new Error(
                                                        i18n.t(
                                                            'message',
                                                            '只允许输入字母、数字、中划线和点',
                                                        ),
                                                    ),
                                                );
                                            }
                                            return Promise.resolve();
                                        },
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    maxLength={50}
                                    placeholder={i18n.t('message', '请输入司机工号')}
                                />
                            </AFormItem>
                            <AFormItem
                                label={i18n.t('name', '状态')}
                                name="state"
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: i18n.t('message', '请选择状态'),
                                    },
                                    {
                                        validator: validatorIllegalCharacter,
                                    },
                                ]}
                            >
                                <Select placeholder={i18n.t('message', '请选择状态')}>
                                    <Select.Option value="1">
                                        {i18n.t('state', '在职')}
                                    </Select.Option>
                                    <Select.Option value="0">
                                        {i18n.t('state', '离职')}
                                    </Select.Option>
                                </Select>
                            </AFormItem>
                            <div className="item-fleet">
                                <div className="poppy-form-item-label" style={{ display: 'block' }}>
                                    <label className="poppy-form-item-required">
                                        {i18n.t('name', '归属车组')}
                                        <AuthTip show={fleetDisable} />
                                    </label>
                                </div>
                                <Form.List name="fleetIds">
                                    {(fields, { add, remove }) => (
                                        <Space direction="vertical" style={{ width: '100%' }}>
                                            {fields.map(
                                                ({ key, name, ...restField }, index: number) => {
                                                    const fleetItem = (
                                                        <FleetTreeSelect
                                                            size={
                                                                isAbroadStyle ? 'large' : 'middle'
                                                            }
                                                            showSearch
                                                            treeNodeFilterProp="title"
                                                            placeholder={i18n.t(
                                                                'message',
                                                                '请选择归属车组',
                                                            )}
                                                            onInitDone={update}
                                                            allowClear={false}
                                                            ref={(node: any) =>
                                                                (fleetTreeEleRef.current[key] =
                                                                    node)
                                                            }
                                                            showSubFleetContent={false}
                                                            getDataSource={fetchGroupDataSource(
                                                                key,
                                                            )}
                                                        />
                                                    );
                                                    return (
                                                        <div
                                                            style={{
                                                                display:
                                                                    groupNumber == 1 ? '' : 'flex',
                                                            }}
                                                            key={key}
                                                        >
                                                            <Form.Item
                                                                {...restField}
                                                                name={name}
                                                                key={key}
                                                                required={false}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: i18n.t(
                                                                            'name',
                                                                            '请选择归属车组',
                                                                        ),
                                                                    },
                                                                ]}
                                                                className="item-fleet-wrap"
                                                            >
                                                                {fleetItem}
                                                            </Form.Item>
                                                            <div
                                                                style={{
                                                                    display:
                                                                        groupNumber === 1
                                                                            ? 'none'
                                                                            : 'block',
                                                                    marginTop: isAbroadStyle
                                                                        ? 10
                                                                        : 0,
                                                                }}
                                                            >
                                                                {index === 0 &&
                                                                fields.length < groupNumber ? (
                                                                    <Button
                                                                        type="primary"
                                                                        onClick={() => add()}
                                                                    >
                                                                        +
                                                                    </Button>
                                                                ) : (
                                                                    <Button
                                                                        disabled={
                                                                            fleetTreeEleRef.current[
                                                                                key
                                                                            ]?.getAuth
                                                                        }
                                                                        onClick={() => remove(name)}
                                                                    >
                                                                        -
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                },
                                            )}
                                        </Space>
                                    )}
                                </Form.List>
                            </div>
                            {vehicleNumber == 0 && !isRelated ? null : (
                                <div className="item-vehicle">
                                    <div
                                        className="poppy-form-item-label"
                                        style={{ display: 'block' }}
                                    >
                                        <label className="poppy-form-item-required">
                                            {i18n.t('name', '关联车辆')}
                                            <AuthTip show={vehicleDisable} />
                                        </label>
                                    </div>
                                    <Form.List name="vehicleIds">
                                        {(fields, { add, remove }) => (
                                            <Space direction="vertical" style={{ width: '100%' }}>
                                                {fields.map(
                                                    (
                                                        { key, name, ...restField },
                                                        index: number,
                                                    ) => (
                                                        <div
                                                            style={{
                                                                display:
                                                                    vehicleNumber == 1
                                                                        ? ''
                                                                        : 'flex',
                                                            }}
                                                            key={key}
                                                        >
                                                            <Form.Item
                                                                {...restField}
                                                                name={name}
                                                                key={key}
                                                                required={false}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: i18n.t(
                                                                            'name',
                                                                            '请选择关联车辆',
                                                                        ),
                                                                    },
                                                                ]}
                                                                className="item-vehicle-wrap"
                                                            >
                                                                <AuthSelect
                                                                    size={
                                                                        isAbroadStyle
                                                                            ? 'large'
                                                                            : 'middle'
                                                                    }
                                                                    options={getVehicleOptions()}
                                                                    placeholder={i18n.t(
                                                                        'message',
                                                                        '请选择关联车辆',
                                                                    )}
                                                                    ref={(node: any) =>
                                                                        (vehicleEleRef.current[
                                                                            key
                                                                        ] = node)
                                                                    }
                                                                    showSearch
                                                                    filterOption={(
                                                                        inputValue: any,
                                                                        option: any,
                                                                    ) =>
                                                                        (option.label as string)
                                                                            .toLowerCase()
                                                                            .indexOf(
                                                                                (
                                                                                    inputValue || ''
                                                                                ).toLowerCase(),
                                                                            ) !== -1
                                                                    }
                                                                />
                                                            </Form.Item>
                                                            <div
                                                                style={{
                                                                    display:
                                                                        vehicleNumber === 1
                                                                            ? 'none'
                                                                            : 'block',
                                                                    marginTop: isAbroadStyle
                                                                        ? 10
                                                                        : 0,
                                                                }}
                                                            >
                                                                {index === 0 &&
                                                                fields.length < vehicleNumber ? (
                                                                    <Button
                                                                        type="primary"
                                                                        onClick={() => add()}
                                                                    >
                                                                        +
                                                                    </Button>
                                                                ) : (
                                                                    <Button
                                                                        disabled={
                                                                            vehicleEleRef.current[
                                                                                key
                                                                            ]?.getAuth
                                                                        }
                                                                        onClick={() => {
                                                                            remove(name);
                                                                        }}
                                                                    >
                                                                        -
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ),
                                                )}
                                            </Space>
                                        )}
                                    </Form.List>
                                </div>
                                )}
                                <RspFormLayout.SingleRow>      
                            <AFormItem
                                className="item-description"
                                label={i18n.t('name', '描述')}
                                name="description"
                                rules={[
                                    {
                                        whitespace: true,
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    allowClear
                                    maxLength={500}
                                    showCount
                                    placeholder={i18n.t('message', '不多于500字')}
                                />
                            </AFormItem>
                                </RspFormLayout.SingleRow>
                            </RspFormLayout>
                        </div>
                    </StarryInfoBlock>
                    {!driverId && faceCompare && (
                        <>
                            <Divider className="add-driver-divider" />
                            <StarryInfoBlock title={i18n.t('name', '司机照片')}>
                                <Col span={24}>
                                    <Form.Item name="picture">
                                        <Uploader
                                            picNum={maxPhotoNumber}
                                            maxPhotoNumber={maxPhotoNumber}
                                            // eslint-disable-next-line no-nested-ternary
                                            type={pictureList ? 'face' : driverId ? 'edit' : 'add'}
                                            setQualityPic={setQualityPic}
                                            pictureList={pictureList}
                                        />
                                    </Form.Item>
                                </Col>
                            </StarryInfoBlock>
                        </>
                    )}
                    <Form.Item>
                        <StarryAbroadLRLayout>
                            <Button
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                {i18n.t('action', '保存')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </Form.Item>
                </Form>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default Driver;
