/*
 * @LastEditTime: 2024-12-30 16:27:54
 */
import IMAGE_NULL from '@/assets/icon_emptystate_deep.svg';
import { exitFullScreen, fullScreen } from '@/utils/commonFun';
import { IconExitFullscreenLine, IconFullscreen } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';
import React, { useMemo, useRef, useState,useEffect, ReactElement } from 'react';
import './image.less';
import { ImageData } from '../../../../types';
import classNames from 'classnames';
import { Tooltip } from '@streamax/poppy';
import {useEventListener} from "@streamax/hooks";
import { aspectRatioImage } from '@base-app/runtime-lib';

interface ImageProps {
    imgSrcList: ImageData[];
    currentTime: number;
    progressBar?: ReactElement;
    aspectRatio?: 'full' | 'origin' | '16:9' | '4:3' | '1:1'
}


const Image: React.FC<ImageProps> = (props) => {
    const { imgSrcList, currentTime, progressBar, aspectRatio } = props;
    const [fullscreen, setFullscreen] = useState(false);
    const imgWrapperRef = useRef<HTMLDivElement>(null);
    const [rendered, setRendered] = useState(false);
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;

    const handleControlFullScreen = () => {
        if (document.fullscreen && fullscreen) {
            setFullscreen(false);
            exitFullScreen();
        } else {
            setFullscreen(true);
            fullScreen(imgWrapperRef.current as Element);
        }
    };

    const ImgSrc = useMemo(() => {
        // 此处保留 后面做展示图片 聚合
        const nextImg = imgSrcList.find((item) => item.startTime == currentTime )?.url
        nextImg && setRendered( false)
        return nextImg;
    }, [currentTime]);

    useEffect(() => {
        setRendered(true)
    }, [currentTime])

    useEventListener(
        'fullscreenchange',
        () => {
            const isFullscreen = document.fullscreenElement !== null;
            if (!isFullscreen){
                setFullscreen(isFullscreen); // 设置标识为退出全屏
            }
        },
        { target: imgWrapperRef },
    );

    const renderImage = () => {
        if (!!imgSrcList.length) {
            return (
                <>
                    {!ImgSrc && <img className={classNames('aspect-img')} src={IMAGE_NULL} />}
                    {ImgSrc && <AspectRatioImage preview={false} className={classNames('aspect-img', 'valid-img')} src={ImgSrc} />}
                    {!ImgSrc && <span className='no-image'>{ i18n.t('name','暂无图片') }</span>}
                </>
            )
        }
        return i18n.t('name','暂无数据');
    };

    return (
        <div ref={imgWrapperRef} className='image-playback-item-wrapper'>
            <div className={classNames('image-playback-item',{
                'image-playback-item-full-screen': fullscreen
            })}>
                {!!imgSrcList.length && (
                    <Tooltip
                        getTooltipContainer={(trigger) => trigger}
                        title={fullscreen ? i18n.t('name', '退出全屏') : i18n.t('name', '全屏')}
                        placement='left'
                    >
                        <div className="fixed-buts" onClick={handleControlFullScreen}>
                            {fullscreen ? <IconExitFullscreenLine /> : <IconFullscreen />}
                        </div>
                    </Tooltip>
                )}
                <div  className={classNames(
                    'img-box',
                    `aspect-ratio-${(aspectRatio || 'default').replace(':', 'x')}`,
                    {
                        'animation-end': rendered,
                        'image-null': !ImgSrc
                    })
                }
                >
                    {renderImage()}
                </div>
            </div>
            {fullscreen ? progressBar : null}
        </div>

    );
};

export default Image;
