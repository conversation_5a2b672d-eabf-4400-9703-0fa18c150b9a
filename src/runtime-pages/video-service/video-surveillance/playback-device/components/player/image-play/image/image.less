@import '~@streamax/poppy-themes/starry/index.less';
.image-playback-item-wrapper{
    height: 100%;
    width: 100%;
    overflow: hidden;

    .image-playback-item{
        position: relative;
        box-sizing: border-box;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        // color: #9e9e9e;
        .img-box{
            width: 5%;
            width: 5%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            .aspect-img {
                &.valid-img {
                    aspect-ratio: auto;
                }
            }
            &.aspect-ratio-full {
                .valid-img {
                    aspect-ratio: unset;
                }
            }
            &.aspect-ratio-origin {
                .valid-img {
                    aspect-ratio: auto;
                }
            }
            &.aspect-ratio-16x9 {
                .valid-img {
                    aspect-ratio: 16 / 9;
                }
            }
            &.aspect-ratio-4x3 {
                .valid-img {
                    aspect-ratio: 4 / 3;
                }
            }
            &.aspect-ratio-1x1 {
                .valid-img {
                    aspect-ratio: 1 / 1;
                }
            }
        }
        .image-null{
            .aspect-img {
                width: 150px;
                height: 150px;
            }
            .no-image {
                position: relative;
                top: -30px;
            }
        }
        .animation-end{
            width: 100%;
            height: 100%;
        }
        .text-no-img{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50% ,-50%);
        }
        .fixed-buts{
            position: absolute;
            top: 6px;
            right: 8px;
            height: 32px;
            background: @primary-color;
            border-radius: 4px;
            padding: 0 12px ;
            line-height: 32px;
            font-size: 16px;
            transition: .3s;
            color: #fff;
            cursor: pointer;
            visibility: hidden;
            z-index: 10;
        }
        &:hover>.fixed-buts{
            visibility: visible;
        }

    }

    .image-playback-item-full-screen{
        height: calc(100% - 36px);
        .img-box{
            background-size: 100% 100%;
        }
    }
}
