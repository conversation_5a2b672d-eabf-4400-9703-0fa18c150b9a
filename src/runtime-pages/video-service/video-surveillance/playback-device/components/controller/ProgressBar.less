@import '~@streamax/poppy-themes/starry/index.less';
@import '../../vars.less';

.@{page-cls-prefix-ctrl}-progress-container{
    width: 100%;
    position: relative;
    height: 12px;
    background: rgba(0,0,0,0.65);
    border-radius: 6px;
    cursor: pointer;
    &.not-allowed{
        cursor: not-allowed;
    }
    /** 视频区域、已剪辑区域、报警点 **/
    .@{page-cls-prefix-ctrl}-progress-print-item{
        position: absolute;
        height: 12px;
        top: 0;
        min-width: 1px;
    }
    /* 播放进度控制按钮 */
    .@{page-cls-prefix-ctrl}-progress-control-btn{
        position: absolute;
        width: 20px;
        height: 20px;
        background: #FFFFFF;
        box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.5);
        border-radius: 50%;
        top: -4px;
        z-index: 20;
        cursor: pointer;
        border: 2px solid #fff;
        box-sizing: border-box;
        &:hover{
            border-color: @primary-color;
        }
        &.active{
            border-color: @primary-color;
        }
    }
    /* 剪辑 */
    .@{page-cls-prefix-ctrl}-progress-clip-box{
        height: 36px;
        background: rgba(24,144,255,0.45);
        border: 1px solid rgba(24,144,255,1);
        border-radius: 4px;
        position: absolute;
        top: -12px;
        z-index: 22;
        cursor: not-allowed;
        .@{page-cls-prefix-ctrl}-progress-clip-left,
        .@{page-cls-prefix-ctrl}-progress-clip-right{
            width: 11px;
            height: 35px;
            background: #1890FF;
            position: absolute;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .@{page-cls-prefix-ctrl}-progress-clip-left{
            left: 0;
            border-radius: 3px 0px 0px 4px;
            cursor: w-resize;
        }
        .@{page-cls-prefix-ctrl}-progress-clip-right{
            right: 0;
            border-radius: 0px 3px 4px 0px;
            cursor: e-resize;
        }
    }
}