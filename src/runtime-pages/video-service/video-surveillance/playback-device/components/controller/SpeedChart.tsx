import React, { useEffect, useRef } from 'react';
import './SpeedChart.less';
import classNames from 'classnames';
import { PAGE_CLS_PREFIX_CTRL } from '../../constant';
import { useSize } from '@streamax/hooks';
import { useGetSize, createCanvas } from './utils';
import { getUnitName } from '@/utils/unit';

// 按需加载echarts
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { GridComponent, MarkLineComponent } from 'echarts/components';
import { LineChart } from 'echarts/charts';

echarts.use([CanvasRenderer, GridComponent, LineChart, MarkLineComponent]);

export interface SpeedData {
    // 时间
    time: number;
    // 速度
    speed: number;
}

export interface AlarmData {
    // 报警的起始、结束位置
    offset: { start: number; end: number };
    // 报警线绘制的颜色
    color: string;
}

export interface SpeedChartProps {
    // 表格绘制参数
    data: {
        // 速度曲线数据
        speed: SpeedData[];
        // 报警数据
        alarm: AlarmData[];
    };
}
const SpeedChart: React.FC<SpeedChartProps> = (props) => {
    const { speed, alarm } = props.data;
    // echats元素ref
    const speedChartsRef = useRef<HTMLDivElement>(null);
    // echarts实例ref
    const speedInstanceRef = useRef<echarts.ECharts>();
    // 获取echats容器宽度

    const canvasContainerRef = useRef<HTMLDivElement>(null);

    const { width: canvasContainerWidth, height: canvasContainerHeight } =
        useGetSize(canvasContainerRef);

    const speedSize = useSize(speedChartsRef);

    function drawAlarmLines() {
        if (!canvasContainerWidth || !canvasContainerHeight) return;

        const container = canvasContainerRef.current;

        let canvas = container?.firstChild as HTMLCanvasElement;

        if (!canvas) {
            canvas = createCanvas(canvasContainerWidth, canvasContainerHeight);
        } else {
            canvas.width = canvasContainerWidth;
            canvas.height = canvasContainerHeight;
        }

        const ctx = canvas.getContext('2d');

        if (!ctx) return;

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        for (const { color, offset } of alarm) {
            ctx.beginPath();
            ctx.setLineDash([2, 2]);
            const { start, end } = offset;
            const w = end - start;
            ctx.moveTo(start + w / 2, 0);
            ctx.lineTo(start + w / 2, canvas.height);
            ctx.strokeStyle = color;
            ctx.lineWidth = w;
            ctx.stroke();
        }

        !container?.firstChild && container!.appendChild(canvas);
    }
    useEffect(() => {
        drawAlarmLines();
    }, [canvasContainerWidth, canvasContainerHeight, alarm]);

    useEffect(() => {
        if (speedSize?.width && speedSize?.height) {
            // 容器宽度变化时，echarts 调用 resize
            speedInstanceRef?.current?.resize();
        }
    }, [speedSize?.width, speedSize?.height]);

    useEffect(() => {
        if (!speedInstanceRef.current) {
            // 初始化echarts实例
            speedInstanceRef.current = echarts.init(
                speedChartsRef.current as unknown as HTMLDivElement,
            );
        }
        // 数据变化，重新绘制echarts
        // 速度曲线用echarts绘制
        speedInstanceRef.current?.setOption(getEchartsOptions(), true);
    }, [speed[0], speed[speed.length - 1], alarm]);

    function getEchartsOptions() {
        // 所有速度点数组，计算得出最大速度和中间速度（最大速度除以二）
        let max = 0;
        const speedData = speed.map((item) => {
            const itemSpeed = item.speed;
            max = Math.max(max, itemSpeed);
            return [item.time, itemSpeed];
        });
        const midium = (max / 2).toFixed(1);
        // y轴两条横线markline的样式
        const lineStyle = {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.25)',
            width: 1,
        };
        const unitName = getUnitName('speed');
        const data = [
            {
                name: midium,
                yAxis: midium,
                lineStyle,
            },
            {
                name: max,
                yAxis: max,
                lineStyle,
            },
        ];
        // 设置echarts图表参数
        const options = {
            yAxis: {
                type: 'value',
                show: false,
            },
            // 设置图标位置占满整个canvas，左右上下不留间隙
            grid: {
                left: 0,
                top: 20,
                right: 0,
                bottom: 0,
            },
            xAxis: {
                show: false,
                top: 0,
                type: 'category',
                boundaryGap: false,
            },
            series: [
                {
                    type: 'line',
                    data: speedData,
                    animation: false,
                    silent: true,
                    // smooth: true,
                    showSymbol: false,
                    sampling: 'max',
                    markLine: {
                        // 取消显示markline的箭头
                        symbol: ['none', 'none'],
                        // 设置markline的数据
                        data: max > 0 ? data : [],
                        label: {
                            position: 'insideStartTop',
                            color: 'rgba(255, 255, 255, 0.6)',
                            textBorderWidth: 0,
                            formatter: `{b}${unitName}`,
                        },
                    },
                    lineStyle: {
                        width: 1,
                    },
                },
            ],
        };

        return options;
    }

    return (
        <div className={classNames(`${PAGE_CLS_PREFIX_CTRL}-speed-container`)}>
            <div
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-speed-charts-box`,
                )}
                ref={speedChartsRef}
            ></div>
            <div
                ref={canvasContainerRef}
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-speed-alarm-lines`,
                )}
            ></div>
        </div>
    );
};

const MemoizedSpeedChart = React.memo(SpeedChart);
export default MemoizedSpeedChart;
