/*
 * @LastEditTime: 2024-11-09 17:09:57
 */
import React, { useLayoutEffect, useRef, useState } from 'react';
import './ScaleControl.less';
import classNames from 'classnames';
import {
    PAGE_CLS_PREFIX_CTRL,
    DEFAULT_CTRL_SCALE_LEVEL_MAX,
    CTRL_SCALE_LEVEL_STEP,
} from '../../constant';
import { i18n } from '@base-app/runtime-lib';
import {
    IconRefresh,
    IconReduceLine,
    IconAddLine,
} from '@streamax/poppy-icons';
import { Slider, Tooltip } from '@streamax/poppy';
// 缩放的步长
const SCALE_STEP = CTRL_SCALE_LEVEL_STEP;
const SCALE_STEP_BTN = 1;

export interface ScaleControlProps {
    // 当前缩放等级 0-99
    level: number;
    // 是否正在剪辑中
    cliping?: boolean;
    // 最小缩放等级
    min?: number;
    // 最大缩放等级
    max?: number;
    // 缩放事件，参数为缩放等级
    onScale: (level: number) => void;
}

const ScaleControl: React.FC<ScaleControlProps> = (props) => {
    const {
        level = 0,
        cliping = false,
        min = 0,
        max = DEFAULT_CTRL_SCALE_LEVEL_MAX,
        onScale,
    } = props;
    // slider缩放值
    const [sliderValue, setSliderValue] = useState<number>(0);

    // 监听外部传递的缩放等级
    useLayoutEffect(() => {
        setSliderValue(level);
    }, [level]);

    // slider滑块改变缩放等级
    const sliderChange = (val: number) => {
        // 若操作后的值与当前值相同，则不处理
        if (val == sliderValue) return;
        //  数据初始化精度问题解决方案
        //
        // 问题场景：用户拖动滑块到94.7，但由于tipFormatter四舍五入显示为95
        // 导致实际值与显示值不一致，后续放大操作会产生累积误差
        //
        // 示例：初始值94.7的问题链
        // 1. 用户拖动 → 实际值94.7 → 显示95 → 用户以为是95
        // 2. 点击放大 → 94.7+1=95.7 → 显示96 → 看起来从95变96（正常）
        // 3. 继续放大 → 95.7+1=96.7 → 显示97 → 累积误差0.7
        // 4. 最终结果 → 实际值99.7 > max(99.33) → 用户多点击一次但无反应
        //
        // 解决方案：Math.round(val) 强制整数化，确保实际值与显示值一致
        //
        // 详细场景表格：
        // 操作步骤	实际值	tipFormatter显示	用户看到	问题描述	备注
        // 初始拖动	94.7	Math.round(94.7) = 95	95	用户拖动到94.7，但显示95	实际值与显示值不一致
        // 第1次放大	94.7 + 1 = 95.7	Math.round(95.7) = 96	96	用户点击放大，从95变成96	看起来正常，实际多放大了0.7
        // 第2次放大	95.7 + 1 = 96.7	Math.round(96.7) = 97	97	用户再次放大，从96变成97	继续累积误差
        // 第3次放大	96.7 + 1 = 97.7	Math.round(97.7) = 98	98	用户继续放大，从97变成98	误差继续累积
        // 第4次放大	97.7 + 1 = 98.7	Math.round(98.7) = 99	99	用户继续放大，从98变成99	误差继续累积
        // 第5次放大	98.7 + 1 = 99.7	被限制为max=99.33	99	用户点击放大但没反应	多放大一次！用户困惑
        //
        // 修复后：Math.round(94.7) = 95，后续每次+1都是整数，避免累积误差
        setSliderValue(Math.round(val));
        // 滑块拖放结束，发起onScale
        onScale(Math.round(val));
    };

    const handleSliderAfterChange = (val: number) => {
        // onScale(val);
    };

    const handleZoomOut = () => {
        if (sliderValue <= min) return;
        const targetVal = sliderValue - SCALE_STEP_BTN;
        const val = targetVal <= min ? min : targetVal;
        setSliderValue(val);
        onScale(val);
    };

    const handleZoomIn = () => {
        if (sliderValue >= max) return;
        const targetVal = sliderValue + SCALE_STEP_BTN;
        const val = targetVal >= max ? max : targetVal;
        setSliderValue(val);
        onScale(val);
    };

    const handleResizeZoomIn = () => {
        if (min === sliderValue) return;
        setSliderValue(min);
        onScale(min);
    };

    return (
        <div className={classNames(`${PAGE_CLS_PREFIX_CTRL}-scale-container`)}>
            <a
                onClick={handleZoomOut}
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                    {
                        disabled: sliderValue <= min,
                    },
                )}
                title={i18n.t('action', '缩小')}
            >
                <IconReduceLine />
            </a>
            <div
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-slider-box`,
                )}
            >
                <Slider
                    step={SCALE_STEP}
                    min={min}
                    max={max}
                    tipFormatter={(v) => Math.round(v as number)}
                    // disabled={cliping}
                    onChange={sliderChange}
                    onAfterChange={handleSliderAfterChange}
                    value={sliderValue}
                />
            </div>
            <a
                onClick={handleZoomIn}
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                    {
                        disabled: sliderValue >= max,
                    },
                )}
                title={i18n.t('action', '放大')}
            >
                <IconAddLine />
            </a>
            <Tooltip title={i18n.t('action', '重置缩放')}>
                <a
                    onClick={handleResizeZoomIn}
                    className={classNames(
                        `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                        `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-reset`,
                    )}
                >
                    <IconRefresh />
                </a>
            </Tooltip>
        </div>
    );
};

const MemoizedScaleControl = React.memo(ScaleControl);
export default MemoizedScaleControl;
