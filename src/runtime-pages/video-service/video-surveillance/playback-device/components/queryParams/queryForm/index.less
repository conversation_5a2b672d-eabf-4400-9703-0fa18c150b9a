@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
@import '../../../vars.less';

@main-background: @grey-900;
@green: #097845;
@red: #993035;
@white: #ffffffd9;

.page-playback-query-params-form {
    display: flex;
    align-items: center;
    background: @main-background;
    .query-item-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 24px;
    }
    .query-item {
        max-width: 250px;
        width: calc(25% - 24px);
        .poppy-picker-panel-container,
        .poppy-select-dropdown,
        .poppy-select-tree,
        .poppy-select-item-option-active:not(.poppy-select-item-option-disabled),
        .poppy-select-item {
            color: rgba(255, 255, 255, 0.65) !important;
            background-color: #454F5B;
        }
        .poppy-select-tree-node-selected,
        .poppy-select-tree-node-content-wrapper:hover {
            background: rgba(255, 255, 255, 0.15) !important;
        }
        .poppy-select-selector::light {
            background: #ffffff00;
            color: rgba(255, 255, 255, 0.45) !important;
            border: 1px solid rgba(255, 255, 255, 0.12) !important;
            .poppy-select-selection-item {
                color: rgba(255, 255, 255, 0.85) !important;
            }
        }
        .poppy-picker::light {
            border: 1px solid rgba(255, 255, 255, 0.12);
            .poppy-picker-input {
             input {
                  color: rgba(255, 255, 255, 0.85) !important;
             }
             input::placeholder,  .poppy-picker-suffix {
                   color: rgba(255, 255, 255, 0.45) !important;
                }
            }
        }
       .poppy-select-selection-placeholder {
            color: rgba(255, 255, 255, 0.45) !important;
        }
        .poppy-select-item-option-selected {
            color: #fff !important;
            font-weight: 600 !important;
            background:@primary-color-light  !important;
        }
        .poppy-select-item:not(.poppy-select-item-option-selected):hover {
            background: rgba(255, 255, 255,.12) !important;
        }
        .poppy-picker-cell,
        .poppy-empty-description {
            color: rgba(255, 255, 255, 0.25) !important;
        }
        .poppy-picker-cell:not(.poppy-picker-cell-disabled) {
            color: rgba(255, 255, 255, 0.65) !important;
        }
        .poppy-picker-header {
            border-bottom-color: rgba(255, 255, 255, 0.25);
        }
        .poppy-picker-panel .poppy-picker-footer {
            border-top-color: rgba(255, 255, 255, 0.25);
        }
        .poppy-picker-panel {
            border: none;
        }
        .poppy-picker-content th,
        .poppy-picker-header-view,
        .poppy-picker-header-prev-btn,
        .poppy-picker-header-super-next-btn,
        .poppy-picker-header-super-prev-btn,
        .poppy-picker-header-next-btn,
        .poppy-picker-today-btn {
            color: rgba(255, 255, 255, 0.65) !important;
            button:hover {
                color: rgba(255, 255, 255, 0.65) !important;
            }
        }
        .poppy-picker-cell-disabled {
            &:hover {
                cursor: not-allowed !important;
            }
        }

        .poppy-picker-clear {
            color: #eee;
            background-color: transparent;
        }
        .poppy-picker,
        .poppy-picker-input > input,
        .poppy-select-selection-item,
        .poppy-select-tree {
            color: rgba(255, 255, 255, 0.85);
        }
        .poppy-select-selector {
            background-color: #ffffff00;
            border-color: rgba(255, 255, 255, 0.25);
            .vehicle-names,
            .poppy-select-selection-search-input {
                color: rgba(255, 255, 255, 0.85);
            }
        }
        .poppy-picker-suffix,
        .poppy-select-arrow {
            color: rgba(255, 255, 255, 0.45);
        }
        .poppy-picker,
        .poppy-select:not(.poppy-select-disabled):hover .poppy-select-selector,
        .poppy-select-focused:not(.poppy-select-disabled).poppy-select:not(
                .poppy-select-customize-input
            )
            .poppy-select-selector {
            background-color: #ffffff00;
            border-color: rgba(255, 255, 255, 0.25);
        }
        .poppy-picker-cell:hover:not(.poppy-picker-cell-in-view) .poppy-picker-cell-inner,
        .poppy-picker-cell:hover:not(.poppy-picker-cell-selected):not(
                .poppy-picker-cell-range-start
            ):not(.poppy-picker-cell-range-end):not(.poppy-picker-cell-range-hover-start):not(
                .poppy-picker-cell-range-hover-end
            )
            .poppy-picker-cell-inner {
            background-color: rgba(89, 126, 247, 0.15) !important;
        }
        .poppy-select {
            &:hover {
                .poppy-select-selector {
                    border-color: @primary-5 !important;
                }
            }
        }
        .poppy-picker {
            &:hover {
                border-color: @primary-5 !important;
            }
            &-disabled {
                &:hover {
                    border-color: rgba(255, 255, 255, 0.25);
                }
            }
        }
        .poppy-select-disabled.poppy-select:not(.poppy-select-customize-input)
            .poppy-select-selector {
            background-color: #ffffff00 !important;
            border-color: rgba(255, 255, 255, 0.25);
        }
        .poppy-badge-status-dot {
            width: 10px;
            height: 10px;
        }
        .poppy-tree-select {
            width: 100%;
        }
        .vehicle-tree-select {
            .tree-children-frond-tail {
                .tree-children-frond-tail-first {
                    color: rgba(255, 255, 255, 0.45); // 回放页面只有暗黑风格，字体颜色定死
                }
            }
        }
    }
    .query-item-small {
        width: 20%;
    }
    // 重置antd 日历样式
    .ant-picker-cell {
        box-sizing: border-box;
        width: 31px;
        height: 31px;
        margin: 0 auto;
        padding: 2px;
        &-inner {
            position: relative;
            width: 100%;
            height: 100%;
            line-height: 25px;
            border-radius: 2px;
            .image-border {
                position: absolute;
                top: 0;
                right: 0;
                width: 100%;
                height: 100%;
                border: 2px solid #ffffffa6;
                border-radius: 2px;
            }
            .triangle {
                position: absolute;
                top: 0;
                right: 0;
                width: 0;
                height: 0;
                border-top: 8px solid #ffffffd9;
                border-left: 8px solid transparent;
                border-top-right-radius: 2px;
            }
        }
        &.green {
            .ant-picker-cell-inner {
                color: rgba(255, 255, 255, 0.65);
                background-color: @green;
            }
        }
        &.red {
            .ant-picker-cell-inner {
                color: rgba(255, 255, 255, 0.65);
                background-color: @red;
            }
        }
        &.current {
            border: 1px solid @green;
            border-radius: 4px;
        }
        &.current.red {
            border: 1px solid @red;
        }
        &.current.white-border {
            border: 1px solid @white;
        }
        &.extra {
            .ant-picker-cell-inner:after {
                position: absolute;
                right: -3px;
                bottom: -3px;
                width: 8px;
                height: 8px;
                border: 4px solid transparent;
                border-bottom-color: #fff;
                transform: rotate(135deg);
                content: '';
            }
        }
    }
    // .poppy-picker-footer {
    //     position: absolute;
    //     width: 100%;
    //     height: 100%;
    //     z-index: 9;
    //     background: rgba(255, 255, 255, .1);
    // }
    // .poppy-picker-footer-extra {
    //     width: 100%;
    //     height: 100%;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    // }
}
.device-alias-option-tooltip-item {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
    .poppy-badge-status-dot {
        width: 10px;
        height: 10px;
    }
}
