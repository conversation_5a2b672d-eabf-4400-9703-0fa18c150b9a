import {
    useAsyncEffect,
    useDeepCompareEffect,
    useUpdate,
    useUpdateEffect,
} from '@streamax/hooks';
import { g_emmiter, i18n, MosaicTypeEnum, useInitMap } from '@base-app/runtime-lib';
// @ts-ignore
import { Action, useMapLayerTool } from '@base-app/runtime-lib';
import { useHistory, useSelector } from '@base-app/runtime-lib/core';
import type { LayerGroup, Map } from 'leaflet';
import { useEffect, useRef, useState } from 'react';
import { ALL_PLAYBACK_PAGE_CODE } from '../../constant';
import { multiAuthCheck } from '../../utils';
import { renderMapPanel } from '@/utils/alarmToolTip';
import getVehicleMarkerHtmlStr from '@/utils/getVehicleMarkerHtmlStr';
import timeFun from '@/utils/time';
import { UseMapLayerToolParams } from '@/types/pageReuse/mapCommonTypes';
import { findLastIndex } from 'lodash';
import { getVehicleDetail } from '@/service/vehicle';
import { getVehicleTypeIconMap } from '@/utils/getVehicleIcon';

/**
 * 转换GPS数据为地图可用的经纬度数据并应用过滤器
 * @param gpsData GPS数据数组
 * @param L Leaflet库实例
 * @returns 过滤后的经纬度数组
 */
const transformGpsData = (gpsData: any[], L: any) => {
    const gpsList = gpsData.map((item: any) => {
        const latLng = new L.TsLatLng([
            item.lat / 1000000,
            item.lng / 1000000,
        ]);
        latLng._gpsTime = item.gpsTime;
        return latLng;
    });
    return L.Util.gpsFilter(gpsList);
};

const playerLineOptions = {};

const useInit = (
    getUseMapLayerOptions?: (
        params: UseMapLayerToolParams,
    ) => UseMapLayerToolParams,
) => {
    const [isInitDone, setIsInitDone] = useState(false);
    const mapRef = useRef<Map | null>(null);
    const warningMarkersRef = useRef<LayerGroup | null>();
    const mapSearchSelected = useRef(false);
    const forceUpdate = useUpdate();
    const { map, L, LR, setDefaultCenter } = useInitMap(
        'map-dom',
        {
            dragging: true,
        },
        true,
    );

    const { toolBarInstance } = useMapLayerTool({
        isInit: true,
        lrProps: {
            map,
            LR,
            options: getUseMapLayerOptions
                ? getUseMapLayerOptions({
                      toolOption: 'TOOL_BAR',
                      toolbarControlOptions: {
                          showTraffic: false,
                          showSatellite: false,
                          showTerrain: false,
                      },
                  })
                : {
                      toolOption: 'TOOL_BAR',
                      toolbarControlOptions: {
                          showTraffic: false,
                          showSatellite: false,
                          showTerrain: false,
                      },
                  },
        },
    });

    // 清空地图组件操作的内容
    const removeToolBarActions = () => {
        toolBarInstance?.remove();
        toolBarInstance?.addTo(map);
    };

    const handleAddMapEvent = () => {
        if (mapRef.current) {
            mapRef.current.on('searchControl:select', () => {
                // 标记是否有在地图上搜索后点击了搜索结果（即地位是否有定位到搜索点）
                mapSearchSelected.current = true;
            });
            mapRef.current.on('searchControl:remove', () => {
                if (mapSearchSelected.current) {
                    mapSearchSelected.current = false;
                }
            });
        }
    };

    useAsyncEffect(async () => {
        if (map) {
            mapRef.current = map;
            handleAddMapEvent();
            try {
                await setDefaultCenter();
            } catch (error) {
                console.error(error);
            }
            setIsInitDone(true);
            warningMarkersRef.current = L.canvasIconLayer({
                zIndex: 9999,
            }).addTo(map);
            // 强制更新组件，更新return的值，避免第一次为null
            forceUpdate();
        }
    }, [map]);
    useEffect(() => {
        return () => {
            warningMarkersRef.current = null;
        };
    }, []);

    return {
        L,
        map: mapRef.current,
        warningMarkers: warningMarkersRef.current,
        isInitDone,
        mapSearchSelected: mapSearchSelected.current,
        removeToolBarActions,
    };
};

const MapPanelParams = {
    resourceCode: MosaicTypeEnum.alarm,
    pageOrigin: 'device',
    videoParam: {
        videoDomId: 'alarmvideo',
        videoLengthDomId: 'alarmvideolength',
    },
    onClickAlarmFun: 'handleClick',
    onloadedmetadataFun: 'getVideoDuration',
};
// 报警点标记
const useRenderMarkers = async ({ map, L, warningMarkers, props }: any) => {
    const history = useHistory();
    const hasWatermark = useSelector(
        (state: { '@base:@model:global_state': any }) =>
            state['@base:@model:global_state']?.tenantWatermark,
    );
    const { warningPoints, vehicleId, deviceId } = props;
    // 获取当前范围的报警数据
    const getRangePoint = () => {
        const markerList = warningPoints.map((point: any) => {
            const markerItem = renderMapPanel(
                // @ts-ignore
                Object.assign(
                    {
                        data: Object.assign(point, {
                            alarmName: i18n.t(
                                `@i18n:@alarmType__${point.alarmType}`,
                                point.alarmName,
                            ),
                            vehicleId,
                            deviceId,
                        }),
                        type: 'alarm',
                        map,
                        L,
                        hasWatermark,
                    },
                    MapPanelParams,
                ),
            );
            return markerItem;
        });
        return markerList;
    };
    const loadMarkers = (L: any) => {
        if (!warningMarkers.current) {
            warningMarkers.current = L?.canvasIconLayer({
                zIndex: 9999,
            }).addTo(map);
        }
        const markers = getRangePoint();
        if (markers.length === 0) {
            warningMarkers.current.clearLayers();
            warningMarkers.current.redraw();
            return;
        }
        warningMarkers.current.clearLayers();
        warningMarkers.current.addLayers(markers);
        warningMarkers.current.redraw();
    };

    const getVideoDuration = () => {
        try {
            const video: any = document.getElementById('alarmvideo');
            const videoLength: any =
                document.getElementById('alarmvideolength');
            videoLength.innerHTML = timeFun.formatTime(
                Math.floor(video?.duration) * 1000,
            );
        } catch (error) {
            // console.warn('pop  closed');
        }
    };
    const handleClick = (data: any) => {
        const code = multiAuthCheck(
            ALL_PLAYBACK_PAGE_CODE.map((item) => `${item}@action:alarm.detail`),
            'code',
        );
        Action.openActionUrl({
            code,
            history,
            url: `/alarm-detail/:alarmId`,
            params: {
                alarmId: data,
                hasEvidence: 1,
            },
        });
    };

    useDeepCompareEffect(() => {
        if (map) {
            loadMarkers(L);
        }
    }, [warningPoints]);
    useEffect(() => {
        (window as any).handleClick = handleClick;
        (window as any).getVideoDuration = getVideoDuration;
    }, []);
};

const useRenderGps = ({
    map,
    L,
    // gpsLine,
    // playbackLine,
    mapSearchSelected,
    props,
}: any) => {
    const { gpsData, onTimeChange, hoverTime, cliping } = props;
    const hoverMarkerRef = useRef<any>(null);
    const moveMarkerRef = useRef<any>(null);
    const gpsLineRef = useRef<any>(null);
    const playbackLineRef = useRef<any>(null);
    const isMouseInRef = useRef(false);
    const hoverPointIcon = L?.divIcon?.({
        html: `<div><div class="center-point"/></div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10],
        className: 'gps-point-icon-hover',
    });

    const handleAddMapEvent = () => {
        map.on('searchControl:remove', () => {
            const bounds = gpsLineRef.current?.getBounds();
            bounds?.isValid() && map?.fitBounds(bounds);
        });
    };

    const loadLine = (L: any) => {
        gpsLineRef?.current?.remove();
        playbackLineRef?.current?.remove();
        if (gpsData && gpsData.length > 0) {
            const latlngArr = transformGpsData(gpsData, L);
            if (map) {
                handleAddMapEvent();
                gpsLineRef.current = L.polyline(latlngArr, {
                    color: '#597EF7',
                    weight: 4,
                });
                gpsLineRef.current.addTo(map);
                // 地图传入小于2个点时会报错，兼容处理。地图也需要处理121830
                if (latlngArr.length > 1) {
                    playbackLineRef.current = new L.PolylinePlayer(
                        gpsLineRef.current,
                        {
                            getDuration: function (prev: any, current: any) {
                                return current._gpsTime - prev._gpsTime;
                            },
                            playedStyle: {
                                color: '#12E02A',
                            },
                            unplayStyle: {
                                color: '597EF7',
                            },
                        },
                    ).addTo(map);
                }
                gpsLineRef.current.on('mouseover', (e: any) => {
                    // 根据移入点的经纬度得到距离最近点的相关信息
                    const point = gpsLineRef.current.snap(e.latlng);
                    // 三个参数分别为距离边起点比例、移入点经纬度、距离最近的边
                    const { factor, latlng: markerLatLng, segIndex } = point;
                    const closeIndex = factor > 0.5 ? segIndex + 1 : segIndex;
                    // 得到距离最近点的经纬度
                    const gpsItem = latlngArr[closeIndex];
                    const gpsLatlng = L.latLng(
                        gpsItem.lat,
                        gpsItem.lng,
                    );
                    // 计算最近点与移入点的经纬度距离
                    const distance = map
                        .latLngToLayerPoint(markerLatLng)
                        .subtract(map.latLngToLayerPoint(gpsLatlng));
                    // 距离小于一定值才绘制marker
                    if (Math.abs(distance.x) + Math.abs(distance.y) <= 10) {
                        moveMarkerRef.current = L.marker(gpsLatlng, {
                            icon: hoverPointIcon,
                        }).addTo(map);
                        // isMouseInRef.current = true;
                        moveMarkerRef.current.on('mouseover', (e: any) => {
                            isMouseInRef.current = true;
                        });
                        moveMarkerRef.current.on('mouseout', (e: any) => {
                            isMouseInRef.current = false;
                            if (moveMarkerRef.current) {
                                moveMarkerRef.current.remove();
                                moveMarkerRef.current = null;
                            }
                        });
                        moveMarkerRef.current.on('click', async () => {
                            !cliping && onTimeChange(gpsItem._gpsTime);
                        });
                    }
                });
                gpsLineRef.current.on('mouseout', (e: any) => {
                    setTimeout(() => {
                        if (moveMarkerRef.current && !isMouseInRef.current) {
                            moveMarkerRef.current.remove();
                            moveMarkerRef.current = null;
                            isMouseInRef.current = false;
                        }
                    }, 0);
                });
                if (!mapSearchSelected) {
                    map?.fitBounds(gpsLineRef.current.getBounds());
                }
            }
        }
    };

    const hoverGpsPoint = (L: any) => {
        if (hoverMarkerRef.current) {
            hoverMarkerRef.current.remove();
            hoverMarkerRef.current = null;
        }
        if (hoverTime) {
            const point = gpsData.find(
                (item: any) => item.gpsTime == Math.round(hoverTime),
            );
            if (point) {
                const position = L?.latLng(point.lat / 1e6, point.lng / 1e6);
                hoverMarkerRef.current = L?.marker(position, {
                    icon: hoverPointIcon,
                }).addTo(map);
            }
        }
    };

    useEffect(() => {
        hoverGpsPoint(L);
    }, [Math.round(hoverTime)]);

    useEffect(() => {
        if (map) {
            loadLine(L);
        }
    }, [map, gpsData]);

    useEffect(() => {
        if (map) {
            loadLine(L);
        }
    }, [cliping]);
    return {
        gpsLine: gpsLineRef.current,
        playbackLine: playbackLineRef.current,
    };
};

const usePlay = ({
    map,
    L,
    gpsLine,
    playbackLine,
    props,
    vehicleInfo,
    currentPlayState,
}: any) => {
    const { traceActive, gpsData, currentPlayTime } = props;
    const loadedCar = useRef(false);
    const currentTime = useRef(0);
    const timeChanges = useRef(0);
    const carMarkerRef = useRef<any | null>(null);
    const currentPlayInfo = useRef<{ currentTime: number; playIndex: number }>({
        currentTime: 0,
        playIndex: 0,
    });
    const seek = (time: number) => {
        if (!gpsLine || !playbackLine) return;

        currentTime.current = time;
        if (gpsData && gpsData.length > 0) {
            const latlngArr = transformGpsData(gpsData, L);
            // 找到当前播放时刻的gps下标
            const playIndex = findLastIndex(latlngArr, (item: any) => {
                return item._gpsTime <= time;
            });
            // 轨迹跳转到对应位置
            if (
                playIndex !== -1 &&
                (playIndex !== currentPlayInfo.current.playIndex ||
                    time < currentPlayInfo.current.currentTime)
            ) {
                if (currentPlayState === 'playing') {
                    playbackLine.play();
                }
                playbackLine.setProgressByIndex(playIndex);
            }
            // 跳转到起点，解决视频前部分没轨迹无法重置问题
            if (playIndex === -1 && latlngArr[0]._gpsTime > time) {
                playbackLine.pause();
                playbackLine.setProgressByIndex(0);
            }
            currentPlayInfo.current = {
                currentTime: time,
                playIndex,
            };
        }
    };

    useEffect(() => {
        if (traceActive) {
            seek(currentTime.current);
        }
    }, [traceActive]);
    useEffect(() => {
        if (playbackLine) {
            if (currentPlayState !== 'playing') {
                playbackLine.pause();
            }
        }
    }, [currentPlayState]);
    useAsyncEffect(async() => {
        const latlngs = gpsLine?.getLatLngs();
        if (latlngs?.length > 0 && vehicleInfo?.vehicleId) {
            carMarkerRef.current?.remove();
            const [rs] = await Promise.allSettled([getVehicleDetail({ vehicleId: vehicleInfo?.vehicleId }), getVehicleTypeIconMap()]);
            const vehicleDetail = rs.status === 'fulfilled' ? rs.value : {};
            const html = await getVehicleMarkerHtmlStr({
                vehicleInfo: {
                    vType: vehicleDetail?.vehicleType,
                },
                options: {
                    showAngle: false,
                    showLicense: false,
                },
            })
            carMarkerRef.current = L?.marker(latlngs?.[0], {
                icon: L?.divIcon?.({
                    html,
                    className: 'mark-car-wrapper-icon',
                    iconSize: [23, 23],
                    iconAnchor: [11, 11],
                }),
            }).addTo(map);
        }
    }, [gpsLine, vehicleInfo?.vehicleId]);

    useUpdateEffect(() => {
        if (currentPlayTime) {
            seek(currentPlayTime);
        }
    }, [currentPlayTime]);
    useEffect(() => {
        timeChanges.current = 0;
        if (!gpsData || (gpsData.length === 0 && carMarkerRef.current)) {
            loadedCar.current = false;
            carMarkerRef.current.remove();
        } else if (gpsData && gpsData.length > 0 && carMarkerRef.current) {
            seek(currentPlayTime);
        }
    }, [gpsData]);

    useEffect(() => {
        if (playbackLine && carMarkerRef.current) {
            // 播放轨迹改变回调
            playbackLine.on('update', (e: any) => {
                carMarkerRef.current.setLatLng(e.latlng);
            });
        }
    }, [playbackLine, carMarkerRef.current]);

    useEffect(() => {
        const token = g_emmiter.on('playback.map.playTimeChange', seek);
        return () => {
            g_emmiter.off('playback.map.playTimeChange', token);
        };
    });
};

export { useInit, useRenderGps, useRenderMarkers, usePlay };
