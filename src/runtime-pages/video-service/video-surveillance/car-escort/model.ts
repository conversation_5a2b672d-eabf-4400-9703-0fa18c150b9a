// https://umijs.org/zh-CN/plugins/plugin-dva
import { Reducer } from '@base-app/runtime-lib/core';

export interface PlaybackModelState {
    highlight: string; // 视频突出模式 => -1:非突出模式 0-4:突出对应index视频
    layout: number; // 视频显示个数
}

export interface PlayBackModel {
    namespace: 'deviceplayback';
    state: PlaybackModelState;
    reducers: {
        saveHighlightData: Reducer;
        saveLayout: Reducer;
        saveVideoShowData: Reducer;
    };
}

const initState: any = {
    highlight: '-1',
    layout: 4
};

const model: PlayBackModel = {
    namespace: 'deviceplayback',
    state: {
        ...initState
    },
    reducers: {
        saveHighlightData(state, action) {
            return {
                ...state,
                highlight: action.payload,
            };
        },
        saveLayout(state, action) {
            return {
                ...state,
                layout: action.payload,
            };
        },
        saveVideoShowData(state, action) {
            return {
                ...state,
                highlight: action.payload?.highlight || state.highlight,
                layout: action.payload?.layout || state.layout,
            };
        },
    },
};

export default model;
