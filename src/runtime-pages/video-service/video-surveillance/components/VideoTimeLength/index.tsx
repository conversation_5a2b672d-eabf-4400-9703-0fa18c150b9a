import { useEffect, useState, useRef, useMemo } from 'react';
import { useSize } from '@streamax/hooks';
import { i18n } from '@base-app/runtime-lib';
import { Progress, InputNumber, Tooltip, Space } from '@streamax/poppy';
import { StreamTypeEnum } from '../../constant';
import useStreamTypeMap from '../../hooks/useStreamTypeMap';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

const CSS_PREFIX = 'video-clip-video-time';
const defaultTime = 5;

interface VideoTimeLengthProps {
    streamType?: StreamTypeEnum; // 当前选中的视频类型
    timeNodes?: number[];
    value?: number; // 分钟值
    onChange?: (value: number) => void; // 返回分钟时间
    isAbroadStyle?: boolean
}

const VideoTimeLength: React.FC<VideoTimeLengthProps> = (props) => {
    const {
        streamType = StreamTypeEnum.MAJOR,
        timeNodes = [1, 2, 3, 5, 8, 10],
        value = defaultTime,
        onChange,
        isAbroadStyle,
    } = props;

    const progressBoxRef = useRef<HTMLDivElement>(null);
    const size = useSize(progressBoxRef);
    const [minute, setMinute] = useState(0);
    const [second, setSecond] = useState(0);
    const [streamTypeMap] = useStreamTypeMap();
    const activeColor = streamTypeMap[streamType].color;

    const [max, min] = useMemo(() => {
        return [timeNodes[timeNodes.length - 1], timeNodes[0]];
    }, [timeNodes]);

    useEffect(() => {
        setMinute(Math.floor(value / 60));
        setSecond(Math.round(value % 60));
    }, [value, max]);

    const onChangeTime = (value: number | null) => {
        if (value === null) return;
        if (value <= 0) {
            setMinute(0);
            onChange?.(second || 1);
        } else if (value >= max) {
            onChange?.(max * 60 + second);
        } else {
            onChange?.(Math.round(value) * 60 + second);
        }
    };

    const renderNode = (nodeTime: number) => {
        const len = (size?.width as number) || 0;
        const left = Math.round((nodeTime / max) * len) - 4; // 统一左移，让圆圈在进度条上面
        let borderColor = '#dbdada';
        if (nodeTime <= minute) {
            borderColor = activeColor;
        }

        const style = {
            left,
            borderColor,
        };
        return (
            <Tooltip title={nodeTime} key={nodeTime}>
                <span
                    className="time-node-item"
                    key={nodeTime}
                    style={style}
                    onClick={() => onChangeTime(nodeTime)}
                />
            </Tooltip>
        );
    };

    function onChangeSecond(value: number | null) {
        if (value === null) return;
        setSecond(Math.round(value));
        onChange?.(minute * 60 + Math.round(value));
    }

    return (
        <div className={`${CSS_PREFIX}-length`}>
            <div className={`${CSS_PREFIX}-length-left`}>
                <div className={`${CSS_PREFIX}-length-left-con`} ref={progressBoxRef}>
                    <Progress
                        percent={Math.round((minute / max) * 100)}
                        strokeLinecap="square"
                        strokeColor={activeColor}
                        showInfo={false}
                    />
                    {[...new Set(timeNodes.concat(minute))].map((time) => renderNode(time))}
                </div>
            </div>
            <div className={`${CSS_PREFIX}-length-right`}>
                <Space>
                    <div className="input-wrapper">
                        <InputNumber
                            size={isAbroadStyle ? 'large' : 'middle'}
                            className="input-number"
                            controls={true}
                            max={max}
                            min={0}
                            value={minute}
                            onStep={onChangeTime}
                            onChange={onChangeTime}
                        />
                        <OverflowEllipsisContainer maxWidth={40}>
                            <span className="unit">{i18n.t('name', '分')}</span>
                        </OverflowEllipsisContainer>
                    </div>
                    <div className="input-wrapper">
                        <InputNumber
                            size={isAbroadStyle ? 'large' : 'middle'}
                            className="input-number"
                            controls={true}
                            max={59}
                            min={minute ? 0 : 1}
                            value={second}
                            onStep={onChangeSecond}
                            onChange={onChangeSecond}
                        />
                        <OverflowEllipsisContainer maxWidth={40}>
                            <span className="unit">{i18n.t('name', '秒')}</span>
                        </OverflowEllipsisContainer>
                    </div>
                </Space>
            </div>
        </div>
    );
};

export default VideoTimeLength;
