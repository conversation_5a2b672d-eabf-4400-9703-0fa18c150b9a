import { getVehicleDeviceBindRecord } from '@/service/device';
import type { VehicleDeviceBindRecordParams } from '@/service/device';
import { Form, Input } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { ColumnsType } from '@streamax/poppy/lib/pro-table';
import { i18n, useUrlSearchStore, utils } from '@base-app/runtime-lib';
import { StarryTable } from '@base-app/runtime-lib';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import SelectStartEndTime from '../select-start-end-time';
import { BindTypeValue } from '../../const';
import { getInitTimeRange } from '@/utils/commonFun';
const { timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp } = utils.formator;
const initTimeRange = getInitTimeRange(7);
import moment from 'moment';
import './index.less';

export default function BindRecord(props: { vehicleId: string }) {
    const { vehicleId } = props;
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();

    const tableRef = useRef<any>();
    const timeInitValue = {
        type: 'startTime',
        value: initTimeRange,
    };

    useEffect(() => {
        const { deviceNo, bindingType, endTime, startTime, page, pageSize } = searchStore.get();
        if (deviceNo || bindingType || endTime || startTime || page || pageSize) {
            const time = {
                type: bindingType === BindTypeValue.BIND ? 'startTime' : 'endTime',
                value: [getLocalMomentByZeroTimeStamp(startTime), getLocalMomentByZeroTimeStamp(endTime)],
            };
            form.setFieldsValue({
                deviceNo,
                time,
            });
            tableRef.current.loadDataSource({
                deviceNo,
                time,
                page,
                pageSize,
            });
        }
    }, []);

    const columns: ColumnsType<any> | undefined = [
        {
            title: i18n.t('name', '设备编号'),
            dataIndex: 'deviceNo',
        },
        {
            title: i18n.t('name', '设备别称'),
            dataIndex: 'deviceAlias',
        },
        {
            title: i18n.t('name', '绑定时间'),
            dataIndex: 'startTime',
            sorter: true,
            render: (text: number) => utils.formator.zeroTimeStampToFormatTime(text) || "-",
        },
        {
            title: i18n.t('name', '解绑时间'),
            dataIndex: 'endTime',
            sorter: true,
            render: (text: number) => utils.formator.zeroTimeStampToFormatTime(text) || "-",
        },
        {
            title: i18n.t('name', '绑定状态'),
            dataIndex: 'binding',
            render: (text: number) => {
                return (
                    <div>
                        <span
                            className={classNames('state-icon', {
                                active: text === 1,
                            })}
                        />
                        <span className="state-text">
                            {text === 1 ? i18n.t('state', '绑定中') : i18n.t('state', '已解绑')}
                        </span>
                    </div>
                );
            },
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'updateUserName',
        },
    ];
    const getList = async (params?: VehicleDeviceBindRecordParams) => {
        const { time, ...rest } = params;
        const start = time.value && time.value[0].valueOf() ? time.value[0] : initTimeRange[0];
        const end = time.value && time.value[1].valueOf() ? time.value[1] : initTimeRange[1];
        
        const reqParams = {
            ...rest,
            vehicleId,
            bindingType: time.type === 'startTime' ? BindTypeValue.BIND : BindTypeValue.UNBIND,
            startTime: timestampToZeroTimeStamp(start),
            endTime: timestampToZeroTimeStamp(end),
        };
        searchStore.set(reqParams);
        const res = await getVehicleDeviceBindRecord(reqParams);
        return res;
    };
    const formItems: QueryFormProps['items'] = [
        {
            label: i18n.t('name', '设备编号'),
            name: 'deviceNo',
            field: Input,
            colSize: 1,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入设备编号'),
            },
        },
        {
            field: SelectStartEndTime,
            colSize: 2,
            name: 'time',
            itemProps: {
                initialValue: timeInitValue,
            },
            fieldProps: {
                mode: ['startTime', 'endTime'],
                defaultTime: [moment().startOf('days'),moment().endOf('days')]
            },
        },
    ];
    const handleReset = () => {
        form.setFieldsValue({
            deviceNo: undefined,
            time: timeInitValue,
        });
        tableRef.current.loadDataSource({
            ...form.getFieldsValue(),
            time: timeInitValue,
            page: 1,
            pageSize: 20,
        });
    };
    return (
        <div className="vehicle-manage-detail-bind-record">
            <StarryTable
                fetchDataFunc={getList}
                columns={columns}
                queryProps={{
                    items: formItems,
                    form,
                    onReset: handleReset,
                }}
                ref={tableRef}
                aroundBordered
                fetchDataAfterMount
                pagination={{
                    defaultCurrent: 1,
                    defaultPageSize: 20,
                }}
                rowKey="id"
            />
        </div>
    );
}
