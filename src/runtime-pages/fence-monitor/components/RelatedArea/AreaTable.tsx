import { Form, Input, InputNumber, Modal, Select, Space, Table, Tooltip } from '@streamax/poppy';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import { IconDelete, IconEdit, IconRequest } from '@streamax/poppy-icons';
import { Auth, getAppGlobalData, i18n } from '@base-app/runtime-lib';
import { StarryModal } from '@base-app/runtime-lib';
import {
    ListDataContainer,
    OverflowEllipsisContainer,
    StarryTransfer,
    TipConfirm,
} from '@streamax/starry-components';
import { getShapePointsByIds } from '@/service/fence';
import { cloneDeep, uniqBy } from 'lodash';
import type { Key } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import React from 'react';
import { SHAPE_TYPE, getShapeTypeText } from '@/utils/fenceMonitorConstant';
import type { FenceAreaItem } from '../../types';
import AddTable from '../AddTable';
import { getAreaAndLinePage } from '@/service/fence-monitor';
import { convertMileage, convertSpeed, getUnitName } from '@/utils/unit';
import { LINE_ACCURACY } from '@/utils/constant';

export interface AreaTableProps {
    dataSource: FenceAreaItem[];
    type: 'line' | 'area';
    selectedRowId?: string;
    onSelectRow?: (row: FenceAreaItem) => void; // 选中行回调
    onChange: (value: FenceAreaItem[], type: 'add' | 'edit' | 'delete') => void;
    pageType?: 'add' | 'detail';
}

type ChooseItem = FenceAreaItem & { disabled?: boolean; key: string };

const MAX_ADD_COUNT = 10; // 限制最大允许添加的条数

const AreaTable: React.FC<AreaTableProps> = (props) => {
    const { dataSource, onSelectRow, onChange, type, selectedRowId, pageType } = props;
    const [showModal, setShowModal] = useState(false);
    const [toChooseDataSource, setToChooseDataSource] = useState<ChooseItem[]>([]);
    const [selectedRows, setSelectedRows] = useState<FenceAreaItem[]>([]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editRecord, setEditRecord] = useState<FenceAreaItem>();

    const [editForm] = Form.useForm();
    const editAuth = Auth.check(
        '@base:@page:fence.monitor:detail@action:tab.link.mapping.areas:setings',
    );

    useEffect(() => {
        if (showModal) {
            const newArr = [...selectedRows, ...dataSource];
            setSelectedRows(uniqBy(newArr, 'id'));
        }
    }, [dataSource, showModal]);

    // 新增页删除
    const handleDelete = (record: FenceAreaItem) => {
        onChange([record], 'delete');
    };
    // 详情页删除
    const handleDeleteDetail = (record: FenceAreaItem) => {
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '确认删除'),
            content: type ==="area" ? i18n.t('message', '确认要删除“{name}”区域吗？', {
                name: record.areaName,
            }) : i18n.t('message', '确认要删除“{name}”线路吗？', {
                name: record.areaName,
            }),
            icon: <IconRequest />,
            onOk: () => {
                onChange([record], 'delete');
                modal.destroy();
            },
        });
    };

    const handleAdd = () => {
        setShowModal(true);
    };

    const handleEdit = (record: FenceAreaItem) => {
        setEditRecord(record);
        setShowEditModal(true);
    };

    const handleSelectChange = (keys: Key[], selectedRows: FenceAreaItem[]) => {
        setSelectedRows(selectedRows);
    };

    const fetchAreaData = async (params: any) => {
        const newParams = cloneDeep(params);

        if (type === 'line') {
            newParams.areaTypes = SHAPE_TYPE.line;
        } else if (!newParams.areaTypes) {
            newParams.areaTypes = `${SHAPE_TYPE.circle},${SHAPE_TYPE.polygon}`;
        }
        newParams.areaName = newParams?.areaName?.trim() ? encodeURIComponent(newParams?.areaName?.trim()) : ''
        return getAreaAndLinePage(newParams).then((rs) => {
            const chooseList: ChooseItem[] = rs.list.map((item) => {
                const newItem: ChooseItem = { ...item, key: item.id };
                if (dataSource.findIndex((p) => p.id === item.id) !== -1) {
                    newItem.disabled = true;
                }
                return newItem;
            });
            setToChooseDataSource(chooseList);
            return Promise.resolve({
                ...rs,
                list: chooseList,
            });
        });
    };

    const handleAddCancel = () => {
        setShowModal(false);
        setSelectedRows([]);
    };

    const handleAddOk = () => {
        const addList = selectedRows.filter(
            (p) => dataSource.findIndex((d) => d.id === p.id) === -1,
        );
        // 选择的区域、线路，请求详情数据
        const areaIds = addList.map((i) => i.id);
        if (!addList.length) {
            handleAddCancel();
            return;
        }
        getShapePointsByIds({ areaIds: areaIds.join(',') }).then((rs) => {
            if (rs) {
                onChange(rs, 'add');
                setShowModal(false);
                setSelectedRows([]);
            }
        });
    };

    const handleEditCancel = () => {
        setShowEditModal(false);
        setEditRecord(undefined);
        editForm.resetFields();
    };

    const handleEditOk = () => {
        editForm.validateFields().then(({ limitSpeed }) => {
            const line = {
                ...editRecord,
                extParam: { ...editRecord?.extParam, routeSpeedLimit: limitSpeed },
            } as FenceAreaItem;
            onChange([line], 'edit');
            handleEditCancel();
        });
    };
    const getColumns = () => {
        let columns: ColumnsType<FenceAreaItem>;
        if (type === 'area') {
            columns = [
                {
                    title: i18n.t('name', '区域名称'),
                    dataIndex: 'areaName',
                    ellipsis: true,
                },
                {
                    title: i18n.t('name', '区域形状'),
                    dataIndex: 'areaType',
                    ellipsis: true,
                    render: (v) => getShapeTypeText(v),
                },
                {
                    title: i18n.t('name', '操作'),
                    dataIndex: 'operate',
                    ellipsis: true,
                    render: (_, record) => {
                        return pageType === 'add' ? (
                            <TipConfirm
                                popOptions={{
                                    onConfirm: () => handleDelete(record),
                                }}
                            />
                        ) : (
                            editAuth && (
                                <Tooltip title={i18n.t('action', '删除')}>
                                    <a onClick={() => handleDeleteDetail(record)}>
                                        <IconDelete />
                                    </a>
                                </Tooltip>
                            )
                        );
                    },
                },
            ];
        } else {
            columns = [
                {
                    title: i18n.t('name', '线路名称'),
                    dataIndex: 'areaName',
                    ellipsis: true,
                },
                {
                    title: i18n.t('name', '线路长度({unit})', { unit: getUnitName('mileage') }),
                    dataIndex: 'routeLength',
                    ellipsis: true,
                    render: (_, record) => {
                        return record.extParam?.routeLength
                            ? convertMileage(
                                  (record.extParam?.routeLength as number) / LINE_ACCURACY,
                              ).toFixed(2)
                            : '-';
                    },
                },
                {
                    title: i18n.t('name', '线路宽度(m)'),
                    dataIndex: 'routeWidth',
                    ellipsis: true,
                    render: (_, record) => {
                        return record.extParam?.routeWidth || '-';
                    },
                },
                {
                    title: i18n.t('name', '线路限速({unit})', { unit: getUnitName('speed') }),
                    dataIndex: 'routeSpeedLimit',
                    ellipsis: true,
                    render: (_, record) => {
                        return record.extParam?.routeSpeedLimit
                            ? convertSpeed(record.extParam?.routeSpeedLimit as number, 0)
                            : '-';
                    },
                },
                {
                    title: i18n.t('name', '操作'),
                    dataIndex: 'operate',
                    ellipsis: true,
                    render: (_, record) => {
                        return (
                            <Space size={16}>
                                {pageType === 'add' ? (
                                    <Tooltip title={i18n.t('action', '编辑')}>
                                        <a onClick={() => handleEdit(record)}>
                                            <IconEdit />
                                        </a>
                                    </Tooltip>
                                ) : (
                                    editAuth && (
                                        <Tooltip title={i18n.t('action', '编辑')}>
                                            <a onClick={() => handleEdit(record)}>
                                                <IconEdit />
                                            </a>
                                        </Tooltip>
                                    )
                                )}

                                {pageType === 'add' ? (
                                    <TipConfirm
                                        popOptions={{
                                            onConfirm: () => handleDelete(record),
                                        }}
                                    />
                                ) : (
                                    editAuth && <Tooltip title={i18n.t('action', '删除')}>
                                        <a onClick={() => handleDeleteDetail(record)}>
                                            <IconDelete />
                                        </a>
                                    </Tooltip>
                                )}
                            </Space>
                        );
                    },
                },
            ];
        }
        return columns;
    };

    const getQueryFormItems = () => {
        if (type === 'area') {
            return [
                {
                    label: i18n.t('name', '区域名称'),
                    name: 'areaName',
                    field: Input,
                    fieldProps: {
                        placeholder: i18n.t('message', '请输入区域名称'),
                        allowClear: true,
                        maxLength: 50,
                    },
                },
                {
                    label: i18n.t('name', '区域形状'),
                    name: 'areaTypes',
                    field: Select,
                    fieldProps: {
                        placeholder: i18n.t('message', '请选择区域形状'),
                        options: [
                            {
                                label: i18n.t('name', '圆形'),
                                value: SHAPE_TYPE.circle,
                            },
                            {
                                label: i18n.t('name', '多边形'),
                                value: SHAPE_TYPE.polygon,
                            },
                        ],
                        allowClear: true,
                    },
                },
            ];
        }
        return [
            {
                label: i18n.t('name', '线路名称'),
                name: 'areaName',
                field: Input,
                fieldProps: {
                    placeholder: i18n.t('message', '请输入线路名称'),
                    maxLength: 50,
                },
            },
        ];
    };
    return (
        <React.Fragment>
            <AddTable
                onAdd={handleAdd}
                dataSource={dataSource}
                columns={getColumns()}
                type={type}
                onSelectRow={onSelectRow}
                selectedRowId={selectedRowId}
            />
            <Modal
                title={type === 'area' ? i18n.t('name', '添加区域') : i18n.t('name', '添加线路')}
                visible={showModal}
                width={920}
                onOk={handleAddOk}
                onCancel={handleAddCancel}
                destroyOnClose
            >
                <StarryTransfer
                    showClearAll
                    showSearch={false}
                    dataSource={toChooseDataSource as any}
                    selectedKeys={selectedRows.map((p) => p.id)}
                    disabledKeys={dataSource?.map((p) => p.id)}
                    defaultSelectedKeys={dataSource?.map((p) => p.id)}
                    onSelectChange={handleSelectChange as any}
                    renderTargetItem={(record: any) => {
                        return (
                            <OverflowEllipsisContainer>
                                {(record as FenceAreaItem).areaName}
                            </OverflowEllipsisContainer>
                        );
                    }}
                    percent={[560, 280]}
                    className="add-area-line-starry-transfer"
                >
                    {({ direction, onSelect, selectedKeys }) => {
                        if (direction === 'SOURCE') {
                            const rowSelection = {
                                hideSelectAll: true,
                                getCheckboxProps: (item: any) => ({
                                    disabled:
                                        item.disabled ||
                                        (selectedKeys?.length || 0) >= MAX_ADD_COUNT,
                                }),
                                onChange(selectedRowKeys: any[]) {
                                    onSelect(selectedRowKeys);
                                },
                                selectedRowKeys: selectedKeys,
                            };
                            return (
                                <ListDataContainer
                                    getDataSource={fetchAreaData as any}
                                    listRender={(data) => {
                                        const columns = getColumns();
                                        return (
                                            <Table
                                                dataSource={data}
                                                columns={columns.slice(0, columns.length - 1)}
                                                rowSelection={rowSelection}
                                                pagination={false}
                                            />
                                        );
                                    }}
                                    renderContainerStyle={{
                                        toolbarAndListRender: false,
                                        queryForm: false,
                                    }}
                                    pagination={{
                                        affix: false,
                                        size: 'small',
                                        showQuickJumper: false,
                                        showSizeChanger: false,
                                        defaultPageSize: 10,
                                    }}
                                    toolbar={false}
                                    queryForm={{
                                        items: getQueryFormItems(),
                                        resetText: false,
                                    }}
                                />
                            );
                        } else {
                            return null;
                        }
                    }}
                </StarryTransfer>
            </Modal>

            {/* 线路编辑弹窗 */}
            <Modal
                visible={showEditModal}
                destroyOnClose
                title={i18n.t('name', '线路限速')}
                width={420}
                onCancel={handleEditCancel}
                onOk={handleEditOk}
            >
                <Form form={editForm} layout="vertical">
                    <Form.Item
                        name="limitSpeed"
                        rules={[{ required: true, message: i18n.t('message', '请输入限制速度') }]}
                        label={i18n.t('name', '线路限速({unit})', { unit: getUnitName('speed') })}
                    >
                        <InputNumber
                            placeholder={i18n.t('message', '请输入限制速度')}
                            precision={0}
                            formatter={(val: any) => val}
                            min={1}
                            max={
                                Number(getAppGlobalData('APP_USER_CONFIG')?.speedUnit) === 1
                                    ? 1000
                                    : 621
                            }
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    );
};

export default AreaTable;
