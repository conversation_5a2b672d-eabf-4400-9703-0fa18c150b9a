import { i18n } from '@base-app/runtime-lib';
import type { PlatformFenceDetail } from '@/service/fence-monitor';

export const clsPrefix = 'fence-monitor';

export const getStatusMap = () => {
    return {
        0: i18n.t('state', '未开始'),
        1: i18n.t('state', '生效中'),
        2: i18n.t('state', '已过期'),
    };
};

export const getStatusColor = (status: number) => {
    const colorMap = {
        0: '#faad14',
        1: '#52c41a',
        2: 'rgba(0, 0, 0, 0.25)'
    };
    return colorMap[status] as string;
};

/** 出入参数类型常量 */
export const IN_OUT_PARAM = {
    /** 出围栏事件 */
    out: 1,
    /** 进围栏事件 */
    in: 2,
    /** 进出围栏事件 */
    inAndOut: 3,
};

export const getInOutParamText = (val?: number) => {
    const { in: inEvent, out, inAndOut } = IN_OUT_PARAM;
    const paramMap = {
        [out]: i18n.t('name', '出围栏事件'),
        [inEvent]: i18n.t('name', '进围栏事件'),
        [inAndOut]: i18n.t('name', '进出围栏事件'),
    };
    if (!val) {
        return '';
    }
    return paramMap[val] as string;
};

export const getStatusText = (status: number) => {
    return getStatusMap()[status] as string;
};

export const getShapeTypeText = (val: number) => {
    const shapeMap = {
        [SHAPE_TYPE.circle]: i18n.t('name', '圆形'),
        [SHAPE_TYPE.polygon]: i18n.t('name', '多边形'),
        [SHAPE_TYPE.line]: i18n.t('name', '线路'),
    };
    return shapeMap[val] as string;
};

export const getEffectiveText = (detail: PlatformFenceDetail) => {
    if (detail?.effectiveTime?.effectiveDate?.forever === TYPE_EFFECTIVE.forever) {
        return i18n.t('name', '永久有效') as string;
    }
    return `${detail?.effectiveTime?.effectiveDate?.startDate} ~ ${detail?.effectiveTime?.effectiveDate?.endDate}`;
};

export const TYPE_EFFECTIVE = {
    forever: 1,
    custom: 0,
};

export const REPEAT_PACE = {
    day: 1,
    week: 2,
    month: 3,
};

export const SPEED_LIMIT_TYPE = {
    max: 1, // 最高限速
    min: 2, // 最小限速
    average: 3, // 平均限速
};

export const SHAPE_TYPE = {
    circle: 1,
    polygon: 2,
    line: 3,
};
