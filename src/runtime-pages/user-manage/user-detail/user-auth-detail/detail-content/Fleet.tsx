import classNames from 'classnames';
import { i18n } from '@base-app/runtime-lib';
import { StarryTable } from '@base-app/runtime-lib';
import { userAuthorityCompanyPage } from '../../../../../service/user';

const FleetTable = (props: any) => {
    const { isInclude, userId, powerId } = props;
    // 车组相关
    const fleetColumns = [
        {
            title: i18n.t('name', '车组名称'),
            dataIndex: 'companyName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '是否包含下级'),
            dataIndex: 'isIncludeSub',
            ellipsis: true,
            render: (...args: any[]) =>
                args[1].includeChild === 1 ? i18n.t('state', '是') : i18n.t('state', '否'),
        },
    ];

    // 获取车组列表
    const getFleetList = async (params: any) => {
        const { list } = await userAuthorityCompanyPage({
            ...params,
            userId,
            powerId,
            isInclude: isInclude ? 1 : 0,
        });
        return Promise.resolve({
            list,
            total: 100,
        });
    };
    return (
        <div
            className={classNames({
                'included-fleet': isInclude,
                'unincluded-fleet': !isInclude,
            })}
        >
            <span className="title">
                {isInclude ? i18n.t('name', '包含车组') : i18n.t('name', '剔除车组')}
            </span>
            <StarryTable 
                fetchDataAfterMount 
                fetchDataFunc={getFleetList}
                columns={fleetColumns}
                rowKey="companyId"

            />
        </div>
    );
};

export default (props: any) => {
    const { userId, powerId } = props;
    return (
        <>
            <FleetTable isInclude userId={userId} powerId={powerId} />
            <FleetTable isInclud={false} userId={userId} powerId={powerId} />
        </>
    );
};
