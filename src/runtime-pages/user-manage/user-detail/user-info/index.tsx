import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { i18n, utils, Auth } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { StarryCard, Action } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer, Descriptions } from '@streamax/starry-components';
import { DesensitEmailNumber, desensItem } from '@/components/DesensitEmailNumber';
import AuthFleetShow from '@/components/AuthFleetShow';
import { getCustomItems } from '@/utils/pageReuse';
import { DetailPageItems } from '@/types/pageReuse/pageReuseBase';
import './index.less';
import { useEffect, useMemo, useState } from 'react';
import { isNil } from 'lodash';
import {getUserExpireDate} from "@/runtime-lib/utils/userExpirationUtil";
import {USER_EXPIRE} from "@/utils/constant";
import { queryApproverByUserId } from '@/service/user';

export type UserDetailInfoShareProps = DetailPageItems & {
    tenantConfigLoading: boolean;
    tenantConfigInfo: Record<string, string>;
    userInfo: any,
    highRiskOperation?: boolean
    userAutoExpireEnable?: boolean
};

const UserDetailInfo = (props: UserDetailInfoShareProps) => {
    /** 定制 */
    const { getDescriptionItems } = props;
    const { userInfo = {}, tenantConfigInfo, tenantConfigLoading ,userAutoExpireEnable = false, highRiskOperation } = props;
    const {
            userId,
            account,
            state,
            createTime,
            areaCode,
            phoneNumber,
            email,
            userDesc,
            createUserName,
            fleets,
            expireInfo,
            approver
        } = userInfo;
    // V2.16.5之前的需求并没有要求详情中的状态需要展示休眠状态，以前的逻辑是除了启用都展示停用，V2.16.5新加到期状态
    const USER_STATE = {
        1: i18n.t('state', '启用'),
        2: i18n.t('state', '停用'),
        4: i18n.t('state', '到期'),
    };
    const approverNameList = (approver || []).map(item => item.approverName).join("、");

    const showFleet = useMemo(() => {
        try {
            if (tenantConfigLoading) return false;
            const belongToIsOpen = JSON.parse(tenantConfigInfo["tenant.user.fleet.config"] || "{}")?.belongToIsOpen;
            return !!belongToIsOpen;
        } catch {
            return false;
        } finally {
            //
        }
    }, [tenantConfigInfo,tenantConfigLoading]);

    const zeroTimeStampToFormatTime = utils.formator.zeroTimeStampToFormatTime;
   
    const overlayStyle = { overlayStyle: { maxWidth: 600 } };

    const baseItems = [
        {
            label: i18n.t('name', '用户名'),
            content: account || '-',
            ellipsis: true
        },
        {
            label: i18n.t('name', '状态'),
            content: USER_STATE[state]|| i18n.t('state', '停用'),

            ellipsis: true
        },
        {
            label: i18n.t('name', '邮箱'),
            key: 'email',
            content: email ? (
                <DesensitEmailNumber
                    desensType={desensItem.email}
                    desensText={email}
                    userId={userId}
                />
            ) : (
                '-'
            ),
        },
        {
            label: i18n.t('name', '联系方式'),
            key: 'phoneNumber',
            content: phoneNumber ? (
                <DesensitEmailNumber
                    desensType={desensItem.phoneNumber}
                    desensText={phoneNumber}
                    userId={userId}
                    areaCode={areaCode}
                />
            ) : (
                '-'
            ),
        },
        {
            label: i18n.t('name', '到期日期'),
            content: getUserExpireDate(expireInfo?.expireTime,state === USER_EXPIRE ? undefined : expireInfo?.remainDays),
            ellipsis: true,
            show: userAutoExpireEnable
        },
        {
            label: i18n.t('name', '归属车组'),
            content: <AuthFleetShow fleetList={fleets} />,
            ellipsis: true,
            show: showFleet
        },
        {
            label: i18n.t('name', '指定审批人'),
            content: approverNameList || '-',
            ellipsis: true,
            show: highRiskOperation
        },
        {
            label: i18n.t('name', '创建人'),
            content: createUserName || '-',
            ellipsis: true
        },
        {
            label: i18n.t('name', '创建时间'),
            content: zeroTimeStampToFormatTime(createTime) || '-',
            ellipsis: true
        },
        {
            label: i18n.t('name', '描述'),
            content: userDesc || '-',
            span: 16,
            key: 'description',
        },
    ].filter(item => isNil(item?.show) || item.show);
    const infoItems = getCustomItems(getDescriptionItems, baseItems, userInfo);

    return (
        <div className="user-manage-detail">
            <StarryCard
                className="user-manage-detail-card-layout"
                title={i18n.t('name', '基本信息')}
                operation={
                    <Action
                        code={'@base:@page:user.manage@action:edit'}
                        url={'/user-manage/edit'}
                        params={{
                            userId,
                        }}
                    >
                        <a className="user-info-edit">{i18n.t('action', '编辑')}</a>
                    </Action>
                }
                operationWrap
            >
                <Descriptions>
                    {infoItems.map((item, index) => (
                        <Descriptions.Item label={item.label} key={index} ellipsis={item.ellipsis}>
                            {item.content}
                        </Descriptions.Item>
                    ))}
                </Descriptions>
            </StarryCard>
        </div>
    );
};
export default withSharePropsHOC(UserDetailInfo);
