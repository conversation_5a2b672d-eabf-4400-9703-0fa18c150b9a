@import '~@streamax/poppy-themes/starry/index.less';
.vehicle-container {
    width: 100%;
    .vehicle-container-table-wrapper {
        // width: 50%;
        // height: 630px;
        .header-wrapper {
            margin-bottom: 24px;
            .header-row {
                &:last-child {
                    margin-top: 12px;
                }
            }
            .title-text {
                font-weight: 600;
                font-size: 16px;
            }
            .operate-btn-wrapper {
                cursor: pointer;
                // margin-top: 12px;
                .add-icon {
                    color: @primary-color;
                }
            }
        }
        .table-wrapper {
            position: relative;
            // border: 1px solid rgba(0, 0, 0, 0.15);
            .report-table-container {
                // height: calc(100% - 7px);
            }
            .table-no-data {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate3d(-50%, -50%, 0);
            }
            .poppy-table-wrapper {
                height: 100%;
                .poppy-table-content {
                    border: none;
                }
                .poppy-spin-nested-loading {
                    height: 100%;
                    .poppy-spin-container {
                        height: 100%;
                        .poppy-table {
                            height: 100%;
                            overflow: auto;
                            .operate-btn {
                                color: @primary-color;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
            .delete-btn {
                color: @primary-color;
            }
        }
    }
}
