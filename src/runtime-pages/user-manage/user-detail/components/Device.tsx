import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
import { useEffect, useState, useRef } from 'react';
import { Auth, i18n } from '@base-app/runtime-lib';
import { Space, message, Input, Button, Modal, Table, Tooltip } from '@streamax/poppy';
import { IconRequest, IconDelete, IconSearch02 } from '@streamax/poppy-icons';
import Empty from './Empty';
import DeviceModal from './DeviceModal';
import {
    userAuthorityDevicePage,
    userAuthorityDeviceDelete,
    userAuthorityDevicePost,
} from '@/service/user';
import type { DriverPageItem } from '@/service/user';
import { OverflowEllipsisContainer, Total } from '@streamax/starry-components';
import { StarryModal } from '@base-app/runtime-lib';
import type { TablePaginationConfig } from '@streamax/poppy/lib/table';
import AuthFleetShow from '@/components/AuthFleetShow';
import './Device.less';
import { dataAuthCodes } from '../const';
import { EmptyTextParam } from '@/hooks/useCustomTableEmpty';
import { DevicePageItem } from '@/service/device';
import ResponsiveHorizontal from "@streamax/responsive-layout/lib/rsp-horizontal";
const DEFAULT_PAGE = 1;
const DEFAULT_PAGESIZE = 10;
interface DeviceProps {
    userId: string;
    appId: number;
    renderEmptyText?: (params: EmptyTextParam) => JSX.Element;
    updateSearchStatus?: (params: { device: boolean }) => void;
}
interface DeviceTableProps extends DeviceProps {
    isInclude: boolean;
    style?: React.CSSProperties;
}
const DeviceTable: React.FC<DeviceTableProps> = (props) => {
    const search = useRef<string>();

    const [currentPageData, setCurrentPageData] = useState<DriverPageItem[]>([]);
    const [pageLoading, setPageLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGESIZE,
        total: 0,
        size: 'middle',
        showTotal: Total.showTotal,
        showSizeChanger: true,
        showQuickJumper: true,
    });
    // 弹框是否显示
    const [visible, setVisible] = useState(false);

    const { isInclude, userId, appId, style, renderEmptyText, updateSearchStatus } = props;

    const addAuthCode = dataAuthCodes['addDataAuth'];
    const delAuthCode = dataAuthCodes['delDataAuth'];

    const addAuth = Auth.check(addAuthCode);

    useEffect(() => {
        // 切换应用的时候要看此应用下面有没有数据权限，没有显示点击添加，有直接显示表格
        getList();
    }, [appId]);

    // 获取设备列表
    const getList = async (queryPage?: number, queryPageSize?: number) => {
        setPageLoading(true);
        try {
            updateSearchStatus?.({
                device: !!search.current,
            });
            const { list, total, page, pageSize } = await userAuthorityDevicePage({
                userId,
                appId,
                isInclude: isInclude ? 1 : 0,
                page: queryPage || DEFAULT_PAGE,
                pageSize: queryPageSize || pagination.pageSize || DEFAULT_PAGESIZE,
                deviceNoOrAlias: search.current?  encodeURIComponent(search.current) : undefined,
            });
            setPageLoading(false);
            setCurrentPageData(list);
            setPagination((prev) => {
                return {
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: total,
                };
            });
        } catch (error) {
            setPageLoading(false);
        }
    };

    const handleTableChange = (pagination: TablePaginationConfig) => {
        getList(pagination.current, pagination.pageSize);
    };

    const deleteDevice = (record: DriverPageItem) => {
        const { deviceId, deviceNumber, deviceAlias } = record;
        StarryModal.confirm({
            size:'small',
            centered: true,
            title: i18n.t('name', '删除确认'),
            icon: <IconRequest />,
            content: i18n.t('message', '确认要删除“{name}”设备吗？', {
                name: deviceAlias || deviceNumber,
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    userAuthorityDeviceDelete({
                        userId,
                        deviceId,
                        isInclude: isInclude ? 1 : 0,
                    })
                        .then((data: unknown) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                getList();
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };

    // 设备相关
    const deviceColumns = [
        {
            title: i18n.t('name', '设备编号'),
            dataIndex: 'deviceNumber',
            ellipsis: { showTitle: false },
            render: (text: string, record: DevicePageItem) => {
                const { deviceNo, deviceNumber, deviceAlias = '' } = record;
                const devNo = deviceNo || deviceNumber;
                const name = deviceAlias || devNo;
                const title = deviceAlias ? `${deviceAlias}(${devNo})` : devNo;
                return (
                    <StarryAbroadOverflowEllipsisContainer
                        tooltip={title !== name && name ? { title } : undefined}
                    >
                        {name}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '设备车辆'),
            dataIndex: 'vehicleNumber',
            ellipsis: { showTitle: false },
            render: (text: string) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetList',
            ellipsis: { showTitle: false },
            render: (text: DriverPageItem['fleetList']) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={text} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            // @ts-ignore
            render: (_, record: DriverPageItem) => {
                return (
                    <Auth code={delAuthCode}>
                        <Tooltip title={i18n.t('action', '删除')}>
                            <IconDelete
                                className="operate-btn"
                                // @ts-ignore
                                onClick={() => deleteDevice(record)}
                            />
                        </Tooltip>
                    </Auth>
                )
            },
        },
    ];

    // 打开选择设备弹框
    const openModal = () => {
        setVisible(true);
    };

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        search.current = e.target.value.trim();
    };

    const handleOk = (type: string, data: { ids: string[] }) => {
        if (!data.ids.length) {
            setVisible(false);
            return;
        }

        userAuthorityDevicePost({
            deviceIds: data.ids.join(','),
            // addAll: args[1].checkAll ? 1 : 0,
            userId,
            appId,
            isInclude: isInclude ? 1 : 0,
        }).then((data: boolean) => {
            if (data) {
                message.success(i18n.t('message', '操作成功'));
                setVisible(false);
                getList();
            } else {
                message.error(i18n.t('message', '操作失败'));
            }
        });
    };

    const generateContent = () => {
        /* if (pagination.total == 0) {
            // 列表为空
            return (
                <Empty
                    descText={isInclude ? i18n.t(
                        'message',
                        '暂无包含设备',
                    ) : i18n.t(
                        'message',
                        '暂无剔除设备',
                    )}
                    clickText={i18n.t('action', '请添加')}
                    onClick={openModal}
                    className="table-no-data"
                />
            );
        } */
        // 不为空的情况下，展示表格
        return (
            <Table
                bordered="around"
                rowKey="deviceId"
                columns={deviceColumns}
                dataSource={currentPageData}
                loading={pageLoading}
                // @ts-ignore
                pagination={{
                    ...pagination,
                }}
                onChange={handleTableChange}
                locale={{
                    emptyText: renderEmptyText?.({
                        type: 'device',
                        showButton: addAuth,
                        onClick: async () => {
                            const showSafeVerify = await Auth.safeCheck(addAuthCode)
                            if (showSafeVerify){
                                openModal()
                            }
                        },
                    }),
                }}
            />
        );
    };

    return (
        <div className="device-container-table-wrapper" style={style}>
            <div className="header-wrapper">
                <div className="header-row">
                    <Space size={20}>
                        <div className="title-text">
                            {isInclude
                                ? i18n.t('message', '包含设备')
                                : i18n.t('message', '剔除设备')}
                        </div>
                    </Space>
                </div>
                <div className="header-row">
                <ResponsiveHorizontal gutter={[0,24]} justify="space-between" align="middle">
                    <Auth code={addAuthCode}>
                        <div className="operate-btn-wrapper">
                                <Button type="primary" onClick={openModal}>
                                    {i18n.t('action', '添加剔除设备')}
                                </Button>
                        </div>
                    </Auth>
                    <div className="search-wrapper">
                        <Input
                            placeholder={i18n.t('message', '请输入设备编号查询')}
                            prefix={<IconSearch02 />}
                            allowClear
                            maxLength={50}
                            onChange={onSearchChange}
                            // @ts-ignore
                            onPressEnter={() => {
                                getList();
                            }}
                        />
                        </div>
                </ResponsiveHorizontal>
                </div>
            </div>
            <div className="table-wrapper">{generateContent()}</div>
            <DeviceModal
                visible={visible}
                type={isInclude ? 'include' : 'exclude'}
                userId={userId}
                authorizeAppId={appId}
                onCancel={() => setVisible(false)}
                onOk={handleOk}
            />
        </div>
    );
};

const Device: React.FC<DeviceProps> = (props) => {
    const { userId, appId, renderEmptyText, updateSearchStatus } = props;
    return (
        <div className="device-container">
            <DeviceTable
                isInclude={false}
                userId={userId}
                appId={appId}
                renderEmptyText={renderEmptyText}
                updateSearchStatus={updateSearchStatus}
            />
        </div>
    );
};
export default Device;
