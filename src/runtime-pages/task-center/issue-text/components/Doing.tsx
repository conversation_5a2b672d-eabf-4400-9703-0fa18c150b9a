import { useEffect, useRef, useState } from 'react';
import { Progress, Table, Form, Tooltip, Select } from '@streamax/poppy';
import SearchRangePicker from '@/components/SearchRangePickerControl';
import { IconDetailsFill } from '@streamax/poppy-icons';
import {
    Auth,
    i18n,
    utils,
    useUrlSearchStore,
    StarryAbroadOverflowEllipsisContainer
} from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import {
    Action,
    // @ts-ignore
} from '@base-app/runtime-lib';
import { fetchTaskMainPage } from '@/service/task';
import moment from 'moment';
import { disabledAfterDate } from '@/utils/commonFun';
import useAppList from '@/hooks/useAppList';
import { initTimeRange } from './config';
import './index.less';
import { getPickerRangeTWM } from '@/utils/date-picker-config/index';


const { zeroTimeStampToFormatTime, timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp } = utils.formator;

const Doing = (props: { activeTab: string }) => {
    const { activeTab } = props;

    const searchStore = useUrlSearchStore();
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const [form] = Form.useForm();
    const tableRef = useRef<any>();
    const createTimeInitialValue = {
        type: 'create',
        value: initTimeRange,
    };
    useEffect(() => {
        if (((appList && appList.length) || !inSaaS) && activeTab === 'doing') {
            const {
                doingStartTime,
                doingEndTime,
                doingCreateTimeType,
                doingPage,
                doingPageSize,
            } = searchStore.get();
            const createTime = {
                type: doingCreateTimeType || createTimeInitialValue.type,
                value: doingStartTime ? 
                    [getLocalMomentByZeroTimeStamp(doingStartTime), getLocalMomentByZeroTimeStamp(doingEndTime)] 
                    : createTimeInitialValue.value,
            };
            form.setFieldsValue({ create: initTimeRange, appId: appList?.[0]?.value, createTime });
            tableRef.current.loadDataSource({ 
                appId: appList?.[0]?.value,
                page: Number(doingPage) || 1,
                pageSize: Number(doingPageSize) || 20
            });
            // tableRef.current.loadDataSource();
        }
    }, [appList, activeTab]);

    // useEffect(() => {
    //     if (activeTab === 'doing') tableRef.current.loadDataSource();
    // }, [activeTab]);

    //表格设计
    const columns = [
        {
            title: i18n.t('name', '任务名称'),
            key: 'taskName',
            dataIndex: 'taskName',
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => (
                <StarryAbroadOverflowEllipsisContainer>
                    <Action
                        code="@base:@page:issue.text@action:view.detail.doing"
                        url="/task/issue-text/detail-doing"
                        fellback={text}
                        params={{
                            activePanel: 'textInform',
                            taskId: record.taskId,
                            taskName: encodeURIComponent(record.taskName),
                            mainTaskStatus: 'doing',
                        }}
                    >
                        {text}
                    </Action>
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: i18n.t('name', '下发时间'),
            key: 'startTime',
            dataIndex: 'startTime',
            ellipsis: { showTitle: false },
            render: (text: number) => (
                <Tooltip title={zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {zeroTimeStampToFormatTime(text)}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '下发设备进度'),
            dataIndex: 'taskStatistics',
            key: 'taskStatistics',
            ellipsis: { showTitle: false },
            render: (text: any) => {
                if (!text || !Array.isArray(text)) {
                    return null;
                }
                let total = 0;
                let done = 0;
                text.forEach((item: any) => {
                    total += item.value;
                    item.status === 100 && (done += item.value);
                });
                return (
                    <div className="issue-text-progress-style">
                        <Progress percent={(done / total) * 100} showInfo={false} />
                        <div className="process-info">
                            {done} / {total}
                        </div>
                    </div>
                );
            },
        },
        inSaaS
            ? {
                  title: i18n.t('name', '归属应用'),
                  dataIndex: 'appId',
                  key: 'appId',
                  ellipsis: { showTitle: false },
                  render: (text: number, row: any) => (
                      <StarryAbroadOverflowEllipsisContainer>
                          {i18n.t(`@i18n:@app__${text}`, row.appName)}
                      </StarryAbroadOverflowEllipsisContainer>
                  ),
              }
            : undefined,
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'createUserName',
            key: 'createUserName',
            ellipsis: { showTitle: false },
            render: (text: string) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: { showTitle: false },
            render: (text: number) => (
                <Tooltip title={zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {zeroTimeStampToFormatTime(text)}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作'),
            key: 'action',
            width:120,
            ellipsis: { showTitle: false },
            render: (text: any, record: any) => (
                <Action
                    code="@base:@page:issue.text@action:view.detail.doing"
                    url="/task/issue-text/detail-doing"
                    params={{
                        activePanel: 'textDetailed',
                        taskId: record.taskId,
                        mainTaskStatus: 'doing',
                        taskName: encodeURIComponent(record.taskName),
                    }}
                >
                    <Tooltip title={i18n.t('name', '明细')}>
                        <IconDetailsFill />
                    </Tooltip>
                </Action>
            ),
        },
    ].filter((i) => i);
    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(columns, {
        storageKey: '@base:@page:issue.text.doing',
        disabledKeys: ['action'],
    });
    const onReset = () => {
        form.setFieldsValue({
            createTime: createTimeInitialValue,
        });
        tableRef.current.loadDataSource({
            appId: appList?.[0]?.value,
            createTime: createTimeInitialValue,
        });
    };
    //获取任务列表数据
    const getTaskServer = (params: any) => {
        let startTime;
        let endTime;
        if (params?.createTime?.value && params.createTime.value[0] && params.createTime.value[1]) {
            startTime = timestampToZeroTimeStamp(params.createTime.value[0]);
            endTime = timestampToZeroTimeStamp(params.createTime.value[1]);
        } else {
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        const timeQuery =
            params.createTime?.type === 'create'
                ? {
                      startCreateTime: startTime,
                      endCreateTime: endTime,
                  }
                : {
                      startExecutedTime: startTime,
                      endExecutedTime: endTime,
                      complexSort: 'start_time desc', //查询下发时间传入排序规则
                  };
        const newStoreTime = {
            doingStartTime: startTime || undefined,
            doingEndTime: endTime || undefined,
        };
        let query = {
            ...params,
            ...timeQuery,
        };
        delete query.createTime;
        delete query.textTime;
        const newSearchStore = {
            ...searchStore.get(),
            doingPage: query?.page || undefined,
            doingPageSize: query?.pageSize || undefined,
            ...newStoreTime,
            doingCreateTimeType: params.createTime?.type
        };
        searchStore.set(newSearchStore);
        query = { ...query, taskTypes: '32', mainTaskStatus: '1,75' };
        return fetchTaskMainPage(query).catch((res) => res);
    };
    return (
        <ListDataContainer
            ref={tableRef}
            getDataSource={getTaskServer}
            loadDataSourceOnMount={false}
            queryForm={{
                form,
                // @ts-ignore
                items: [
                    inSaaS
                        ? {
                              label: i18n.t('name', '归属应用'),
                              name: 'appId',
                              field: Select,
                              initialValue: appList?.[0]?.value,
                              fieldProps: {
                                  options: appList,
                                  placeholder: i18n.t('message', '请选择归属应用'),
                              },
                          }
                        : undefined,
                    {
                        name: 'createTime',
                        colSize: 2,
                        field: SearchRangePicker,
                        itemProps: {
                            initialValue: createTimeInitialValue,
                        },
                        fieldProps: {
                            maxInterval: {
                                value: 31,
                                unitOfTime: 'day',
                            },
                            pickerProps: {
                                allowClear: true,
                                showTime: {
                                    hideDisabledOptions: true,
                                    defaultValue: [
                                        moment('00:00:00', 'HH:mm:ss'),
                                        moment('23:59:59', 'HH:mm:ss'),
                                    ],
                                },
                                ranges: getPickerRangeTWM(),
                                disabledDate: disabledAfterDate,
                                style: { width: '100%' },
                            },
                        },
                    },
                ].filter((i) => i),
                onReset: onReset,
            }}
            toolbar={{
                extraIconBtns: [
                    <TableColumnSetting key="setting" {...(tableColumnSettingProps as any)} />,
                ],
            }}
            listRender={(data) => {
                return (
                    <Table
                        columns={tableColumns}
                        dataSource={data}
                        pagination={false}
                        rowKey="taskId"
                    />
                );
            }}
        />
    );
};
export default Doing;
