import React, { useState, useEffect } from 'react';
import {i18n, useSystemComponentStyle, useUrlSearchStore} from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { Tabs } from '@streamax/poppy';
import Done from './components/Done';
import Fail from './components/Fail';

import './index.less';

const { TabPane } = Tabs;

export default () => {
    const {isAbroadStyle} = useSystemComponentStyle();
    const [tabActiveKey, setTabActiveKey] = useState<string>('done');
    const searchStore = useUrlSearchStore();
    useEffect(() => {
        const { tabVal } = searchStore.get();
        setTabActiveKey(tabVal);
    }, []);
    const tabChange = (e: string) => {
        setTabActiveKey(e);
        searchStore.set({
            tabVal: e,
        });
    };
    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="page-task-clear">
                    <Tabs activeKey={tabActiveKey} onChange={tabChange} hiddenLine={isAbroadStyle}>
                        <TabPane tab={i18n.t('name', '已完成')} key="done">
                            <Done tab={tabActiveKey} />
                        </TabPane>
                        <TabPane tab={i18n.t('name', '失败')} key="fail">
                            <Fail tab={tabActiveKey} />
                        </TabPane>
                    </Tabs>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
