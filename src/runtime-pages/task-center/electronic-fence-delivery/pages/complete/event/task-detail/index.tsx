import { useRef, useState, useEffect } from 'react';
import { Button, message, Table, Form, Tooltip } from '@streamax/poppy';
import { i18n, utils, Auth, StarryAbroadIcon } from '@base-app/runtime-lib';
import {
    PageBreadcrumbLayout,
    PageCardLayout,
    StarryModal,
} from '@base-app/runtime-lib';
import { fetchTaskSubPage, fetchTaskSubStatistics, subTaskRetry } from '@/service/task';
import './index.less';
import { ListDataContainer, TableColumnSetting } from '@streamax/starry-components';
import Statistics, { StatisticsData } from './components/Statistics';
import MultiSearch from '@/components/MultiSearch';
import { IconRetry } from '@streamax/poppy-icons';
import { ColumnsType } from '@streamax/poppy/lib/pro-table';
import DeviceShow from '@/components/DeviceShow';
import type { DeviceData } from '@/components/DeviceShow';

const clsPrefix = 'task-center-electronic';

const TaskDetail = (props: any) => {
    const [form] = Form.useForm();
    const containerRef = useRef<any>(null);
    const [statistics, setStatistics] = useState<StatisticsData>({
        allNum: 0,
        failNum: 0,
        successNum: 0,
    });
    const selectedStateRef = useRef<undefined | string>(undefined);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const { taskId } = props;
    const fenceState = {
        wait: '0',
        doing: '1',
        fail: '50',
        success: '100',
    };
    const items = [
        {
            label: i18n.t('name', '车辆'),
            name: 'query',
            field: MultiSearch,
            fieldProps: {
                searchModule: ['vehicle'],
                placeholder: i18n.t('message', '请选择车辆'),
                prefix: null,
                forceRefresh: true
            },
        },
    ];
    const getDataSource: any = async (params: any) => {
        const { page, pageSize, query } = params;
        if (!taskId) {
            return { list: [], total: 0 };
        }
        let vehicleIds;
        if (query && query.module == 'vehicle') {
            vehicleIds = query?.data.vehicleId;
        }
        return fetchTaskSubPage({
            mainTaskId: taskId,
            page,
            pageSize,
            vehicleIds,
            taskStatus: selectedStateRef.current,
        })
            .then((res: any) => {
                const list = res.list;
                const total = res.total;

                return { list, total };
            })
            .catch(() => {
                return { list: [], total: 0 };
            })
            .finally(() => {
                setSelectedRowKeys([]);
            });
    };

    const getStatistics = () => {
        fetchTaskSubStatistics({
            mainTaskId: taskId,
        }).then((rs: any) => {
            let allNum = 0,
                failNum = 0,
                successNum = 0;
            (rs || []).map((item: any) => {
                allNum += item.value;
                if (item.status == fenceState.fail) {
                    failNum = item.value;
                } else if (item.status == fenceState.success) {
                    successNum = item.value;
                }
            });
            setStatistics({
                allNum,
                failNum,
                successNum,
            });
        });
    };
    useEffect(() => {
        if (taskId) getStatistics();
    }, [taskId]);
    const columns: ColumnsType<any> = [
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            ellipsis: true,
            render: (text: string) => (text ? text : '-'),
        },
        {
            title: i18n.t('name', '车辆设备'),
            dataIndex: 'deviceNumber',
            key: 'deviceNumber',
            ellipsis: true,
            render: (text: string, record: DeviceData) => <DeviceShow deviceInfo={record} />,
        },
        {
            title: i18n.t('name', '下发状态'),
            dataIndex: 'taskStatus',
            key: 'taskStatus',
            ellipsis: true,
            render: (text: string) => {
                let res;
                switch (String(text)) {
                    case fenceState.fail:
                        res = i18n.t('name', '下发失败');
                        break;
                    case fenceState.success:
                        res = i18n.t('name', '下发成功');
                        break;
                    default:
                        res = '-';
                        break;
                }
                return (
                    <span>
                        <span className={`dot dot-${text}`} />
                        <span>{i18n.t('name', res)}</span>
                    </span>
                );
            },
        },
        {
            title: i18n.t('name', '下发时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (text: any) => (text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'),
        },
        {
            title: i18n.t('name', '完成时间'),
            dataIndex: 'successTime',
            key: 'successTime',
            ellipsis: true,
            render: (text: any) => (text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'),
        },
        {
            title: i18n.t('name', '失败原因'),
            dataIndex: 'failCode',
            key: 'failCode',
            ellipsis: true,
            render: (text: any) =>
                text ? (
                    <span title={i18n.t(`@base:@return__${text}`)}>
                        {i18n.t(`@base:@return__${text}`)}
                    </span>
                ) : (
                    '-'
                ),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'handle',
            key: 'handle',
            ellipsis: true,
            iconResize:true,
            width:120,
            render: (text: any, record: any) => {
                if (record.taskStatus == fenceState.fail) {
                    return (
                        <Auth code="@base:@page:task.center.electronic:complete.detail@action:retry">
                            <Tooltip title={i18n.t('action', '重试')}>
                                <a onClick={() => retryOneComfirm(record)}>
                                    <StarryAbroadIcon>
                                        <IconRetry />
                                    </StarryAbroadIcon>
                                </a>
                            </Tooltip>
                        </Auth>
                    );
                } else {
                    return '-';
                }
            },
        },
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (selectedKeys: any) => {
            setSelectedRowKeys(selectedKeys);
        },
        getCheckboxProps: (record: any) => ({
            disabled: record.taskStatus != fenceState.fail,
        }),
    };
    // 单个重试 点击右侧重试时触发，触发时有vehicleNumber;
    const retryOne = (record: any) => {
        subTaskRetry({ subTaskIds: record.taskId }).then(() => {
            message.success(i18n.t('message', '操作成功'));
            setTimeout(() => {
                containerRef.current.loadDataSource();
            }, 500);
        });
    };
    const retryOneComfirm = (record: any) => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '重试确认'),
            content: `${i18n.t('message', '确认重试吗')}?`,
            okText: i18n.t('name', '确定'),
            cancelText: i18n.t('name', '取消'),
            closable: true,
            onOk: async () => {
                retryOne(record);
            },
        });
    };
    // 批量重试 点击批量重试按钮时触发
    const retryMul = () => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '批量重试确认'),
            content: `${i18n.t('message', '确认批量重试吗')}?`,
            okText: i18n.t('name', '确定'),
            cancelText: i18n.t('name', '取消'),
            closable: true,
            onOk: async () => {
                subTaskRetry({ subTaskIds: selectedRowKeys.join(',') }).then(() => {
                    message.success(i18n.t('message', '操作成功'));
                    setTimeout(() => {
                        containerRef.current.loadDataSource();
                        setSelectedRowKeys([]);
                    }, 500);
                });
            },
        });
    };

    const handleStatusChange = (e: string | undefined) => {
        selectedStateRef.current = e;
        containerRef.current.loadDataSource();
    };
    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(columns, {
        storageKey: 'completeDetailTaskDetailtable',
    });
    const toolbar: any = {
        extraLeft: (
            <Button
                onClick={() => {
                    retryMul();
                }}
                disabled={selectedRowKeys.length == 0}
            >
                <IconRetry />
                {i18n.t('action', '批量重试')}
            </Button>
        ),
        extraIconBtns: [<TableColumnSetting key="setting" {...tableColumnSettingProps} />],
    };

    return (
        <div className="task-electronic-detail">
            <div className={`${clsPrefix}-container`}>
                <Statistics data={statistics} onChange={handleStatusChange} />
                <ListDataContainer
                    getDataSource={getDataSource}
                    queryForm={{
                        items: items,
                        form,
                    }}
                    toolbar={toolbar}
                    ref={containerRef}
                    listRender={(data) => {
                        return (
                            <Table
                                columns={tableColumns}
                                rowSelection={rowSelection}
                                dataSource={data}
                                pagination={false}
                                rowKey="taskId"
                            />
                        );
                    }}
                />
            </div>
        </div>
    );
};
export default TaskDetail;
