import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import {i18n, useSystemComponentStyle} from '@base-app/runtime-lib';
import { useDeepCompareEffect, useSize } from '@streamax/hooks';
import { Space, Tooltip } from '@streamax/poppy';
import { gbTransform } from '@/utils/commonFun';
import { MAX_GB } from '@/utils/constant';
type EChartsOption = echarts.EChartsOption;
interface Item {
    name: string;
    value: number | string;
    key: string;
}
interface Detail {
    vehicle: number | string;
    tenant: number | string;
}
interface Props {
    data: Detail;
}
const COLORS = [ '#847FFD','#42B5EA'];
export default function StorageChart(props: Props) {
    const { isDark } =useSystemComponentStyle();
    const { tenant, vehicle } = props.data || {
        tenant: 0,
        vehicle: 0,
    };
    const total = Number(tenant) + Number(vehicle);

    const { data = {} } = props;
    const items: Item[] = [
        {
            name: i18n.t('name', '单车空间'),
            value: vehicle ?? '-',
            key: 'vehicle',
        },
        {
            name: i18n.t('name', '租户空间'),
            value: tenant ?? '-',
            key: 'tenant',
        }
    ];
    const legendItems = [
        ...items,
        {
            name: i18n.t('name', '总存储空间'),
            value: total ?? '-',
            key: 'total',
        },
    ];
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartExample = useRef<Record<string, any>>();
    const size = useSize(chartContainerRef);

    useEffect(() => {
        if (!chartExample.current) {
            chartExample.current = echarts.init(chartContainerRef.current as HTMLDivElement);
        }
    }, []);

    useEffect(() => {
        chartExample.current?.resize?.();
    }, [size?.width, size?.height]);
    useDeepCompareEffect(() => {
        const option: EChartsOption = {
            grid: {
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
            },
            color: COLORS,
            xAxis: {
                type: 'value',
                max: items.map((item) => item.value).reduce((a, b) => Number(a) + Number(b)),
                show: false,
            },
            yAxis: {
                type: 'category',
                show: true,
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                axisLabel: {
                    show: false,
                },
                splitArea: {
                    show: true,
                    areaStyle: {
                        color: [isDark? 'rgba(145,158,171,0.12)': 'rgba(0,0,0,0.04)'],
                    },
                },
            },
            series: items.map((item: Item) => ({
                name: item.name,
                type: 'bar',
                data: [item.value],
                stack: 'total',
                barWidth: '20px',
                emphasis: {
                    disabled: true,
                },
            })),
            aria: {
                enabled: true,
                decal: {
                    show: true,
                    decals: [
                        {
                            symbolSize: 0,
                        },
                        {
                            symbolSize: 0,
                        },
                        {
                            symbolSize: 0,
                        },
                        // 绘制斜线填充
                        {
                            color: 'rgba(154, 154, 154, 0.4)',
                            dashArrayX: [1, 0],
                            dashArrayY: [1, 4],
                            symbolSize: 1,
                            rotation: -Math.PI / 4,
                        },
                    ],
                },
            },
        };
        chartExample.current?.setOption(option);
    }, [data,isDark]);

    const setLegend = (arr: Item[]) => {
        const result = arr.map((item, index) => ({
            ...item,
            color: COLORS[index],
        }));
        return result;
    };

    const title = (
        <Space size={8} direction={'vertical'}>
            {setLegend(legendItems).map((item: Item & Record<'color', string>, index: number) => (
                <div key={item.name + item.value}>
                    <span
                        className="legend-block"
                        style={{
                            width: props.data?.[item.key] ? 16 : 0,
                            height: props.data?.[item.key] ? 10 : 0,
                            marginRight: 8,
                            display: 'inline-block',
                            borderRadius: 2,
                            background:
                                index === 3
                                    ? 'repeating-linear-gradient(-45deg, #f4f4f4, #9a9a9a 3px)'
                                    : item.color,
                        }}
                    />
                    {`${item.name}：` +
                        (item.value === '-'
                            ? '-'
                            : `${gbTransform(Number(item.value), false)}${
                                  (props.data?.[item.key] || total) > MAX_GB ? 'TB' : 'GB'
                              }`)}
                </div>
            ))}
        </Space>
    );

    return (
        <Tooltip title={title} arrowPointAtCenter>
            <div ref={chartContainerRef} style={{ width: '100%', height: 20 }} />
        </Tooltip>
    );
}
