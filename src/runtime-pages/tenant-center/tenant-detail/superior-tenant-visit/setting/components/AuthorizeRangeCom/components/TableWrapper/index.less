@import '~@streamax/poppy-themes/starry/index.less';

.authorize-range-table-wrapper {
    .header-wrapper {
        // height: 40px;
        margin-bottom: 15px;
        .title-text {
            color: rgba(0, 0, 0, 0.85);
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .operate-btn-wrapper {
            display: flex;
            justify-content: space-between;
            .search-wrapper {
                // float: right;
                &.error {
                    .poppy-input-affix-wrapper {
                        border: 1px solid #ff4d4f;
                    }
                    .poppy-input-prefix {
                        color: #ff4d4f;
                    }
                }
            }
        }
    }

    .table-content-wrapper {
        position: relative;
        height: 507px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        .table-no-data {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
        }
        .poppy-table-wrapper {
            height: 100%;
            .poppy-spin-nested-loading {
                height: 100%;
                .poppy-spin-container {
                    height: 100%;
                    .poppy-table-cell{
                        padding: 12px 16px;
                    }
                }
            }
        }
        .delete-btn {
            color: @primary-color;
        }
    }
    .table-content-wrapper::abroad {
        height: 532px;
    }
    .pagination-total-nums {
        color: @primary-color;
    }
    td.poppy-table-cell-ellipsis {
        a, span {
            display: inline-block;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .data-authorize-table-wrapper{
        .poppy-table-pagination{
            margin-right: 16px;
        }
    }
}
