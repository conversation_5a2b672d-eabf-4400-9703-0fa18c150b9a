import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { TreeSelect } from '@streamax/poppy';
import { useDispatch, getDvaApp } from '@base-app/runtime-lib/core';
import { i18n, useConstructor } from '@base-app/runtime-lib';
import { StarryModal } from '@base-app/runtime-lib';
import PermissionCenterModel from '../../../../../../model';
import type { OperateType } from '../../index';
import type { Dispatch } from '@base-app/runtime-lib/core';
import './index.less';

interface CompanyModalProps {
    visible: boolean;
    type: OperateType;
    targetList: any[];
    authorizeAppId?: number | undefined;
    onCancel?: () => void;
    onOk?: (type: OperateType, values: any[]) => void;
}

const CompanyModal: React.FC<CompanyModalProps> = (props) => {
    const { visible, type, targetList, onCancel, onOk, authorizeAppId } = props;
    const [treeData, setTreeData] = useState();
    const [selectedList, setSelectedList] = useState<any>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const [originData, setOriginData] = useState<any[]>([]);
    const dispatch: Dispatch = useDispatch();

    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(PermissionCenterModel);
    });

    const findChildren = (data: any[], id: number): any[] => {
        const children = data.filter((item1: any) => {
            if (item1.parentId === id) {
                // 把该条数据标记为已push，后续不再遍历
                item1.hasPush = true;
                item1.children = findChildren(data, item1.fleetId);
                return true;
            }
            return false;
        });
        return children;
    };

    const generateTreeData = (data: any[]): any[] => {
        const disabledKeys = targetList.map((p) => p.fleetId as string);
        if (!data) return [];
        data.sort((a: any, b: any) => a.parentId - b.parentId);
        const treeDataList: any[] = [];
        data.forEach((item: any) => {
            item.key = item.fleetId;
            item.title = item.fleetName;
            item.value = item.fleetId;
            if (disabledKeys.includes(item.fleetId)) {
                item.disabled = true;
            }
            if (!item.hasPush) {
                const newData = {
                    ...item,
                };
                const children = findChildren(data, item.fleetId);
                if (children.length) {
                    newData.children = children;
                }
                treeDataList.push(newData);
            }
        });
        return treeDataList;
    };

    useEffect(() => {
        if (visible && authorizeAppId) {
            dispatch({
                type: 'permissionCenter/fetchChooseCompanyList',
                payload: {
                    page: 1,
                    pageSize: 1e8,
                    appId: authorizeAppId,
                },
            }).then((rs: any) => {
                setOriginData(rs.list);
                // @ts-ignore
                setTreeData(generateTreeData(rs.list));
            });
        }
    }, [visible, authorizeAppId]);

    const handleChange = (values: any, node: any) => {
        node.children = null;
        node.isIncludeSub = 1;
        setSelectedList([node]);
    };

    const handleOk = () => {
        setSearchValue('');
        setSelectedList([]);
        onOk?.(type, [...selectedList, ...targetList]);
    };

    const handleSearch = (s: string) => {
        setSearchValue(s);
    };
    const treeDropdownRender = (originNode: React.ReactNode) => {
        if (!searchValue) {
            return originNode;
        }
        const searchResult = (originData || []).filter((p: any) => {
            return (p.fleetName || '').toLowerCase().indexOf(searchValue.toLowerCase()) !== -1;
        });
        const disabledKeys = targetList.map((p) => p.fleetId as string);
        return (
            <div className="search-result-list-container">
                {searchResult.map((item: any) => {
                    const disabled = disabledKeys.findIndex((p: any) => p === item.fleetId) !== -1;
                    return (
                        <div
                            className={classNames('result-item', { disabled: disabled })}
                            key={item.fleetId}
                            style={{ margin: '6px 12px' }}
                        >
                            <p
                                onClick={() => {
                                    if (disabled) return;
                                    item.children = null;
                                    item.isIncludeSub = 1;
                                    setSelectedList([item]);
                                    setSearchValue('');
                                }}
                            >
                                {item.fleetName}
                            </p>
                        </div>
                    );
                })}
            </div>
        );
    };
    return (
        <StarryModal
            className="data-authorize-add-modal"
            centered
            destroyOnClose
            maskClosable={false}
            visible={visible}
            width={420}
            title={
                type === 'include'
                    ? i18n.t('message', '添加包含车组')
                    : i18n.t('message', '添加剔除车组')
            }
            onCancel={() => {
                setSearchValue('');
                setSelectedList([]);
                onCancel?.();
            }}
            onOk={handleOk}
            focusTriggerAfterClose={false}
        >
            <div className="placeholder-text" style={{ marginBottom: '10px' }}>
                {type === 'include'
                    ? i18n.t('message', '请选择授权包含车组')
                    : i18n.t('message', '请选择授权剔除车组')}
            </div>
            <div className="tree-select-wrapper">
                <TreeSelect
                    showSearch
                    // treeCheckStrictly
                    // treeCheckable
                    // multiple
                    placeholder={i18n.t('message', '请选择')}
                    treeData={treeData}
                    treeNodeFilterProp="title"
                    style={{
                        width: '100%',
                    }}
                    onSelect={handleChange}
                    value={selectedList.map((p: any) => p.fleetId as string)}
                    onSearch={handleSearch}
                    // @ts-ignore
                    dropdownRender={treeDropdownRender}
                    searchValue={searchValue}
                />
            </div>
        </StarryModal>
    );
};

export default CompanyModal;
