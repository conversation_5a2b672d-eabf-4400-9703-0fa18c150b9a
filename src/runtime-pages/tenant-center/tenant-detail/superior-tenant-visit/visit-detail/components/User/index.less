//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.user-container {
    display: flex;
    width: 100%;
    .user-container-table-wrapper {
        width: 100%;
        // height: 630px;
        .header-wrapper {
            margin-bottom: 15px;
            .header-row {
                display: flex;
                justify-content: space-between;
                &:last-child {
                    margin-top: 12px;
                }
            }
            .title-text {
                color: @starry-text-color-primary;
                font-weight: 700;
                font-size: 16px;
            }
            .operate-btn-wrapper {
                cursor: pointer;
                // margin-top: 12px;
                .add-icon {
                    color: @primary-color;
                }
            }
        }
        .table-wrapper {
            position: relative;
            min-height: 559px;
            .table-no-data {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate3d(-50%, -50%, 0);
            }
            .poppy-table-wrapper {
                height: 100%;
                .poppy-table-content {
                    border: none;
                }
                .poppy-spin-nested-loading {
                    height: 100%;
                    .poppy-spin-container {
                        height: 100%;
                        .poppy-table {
                            height: 100%;
                            overflow: auto;
                            .operate-btn {
                                color: @primary-color;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
            .delete-btn {
                color: @primary-color;
            }
        }
    }
}
