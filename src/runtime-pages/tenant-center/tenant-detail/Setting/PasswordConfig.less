@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.password-config {
    .number-range-wrapper {
        display: flex;
        align-items: center;
        width: 200px;
    }
    &::abroad {
        .number-range-wrapper {
            width: 100%;
        }
        .starry-info-panel {
            padding-bottom: 0;
        }
    }
    .desc-wrapper {
        &::abroad {
            font-size: 0px;
        }
    }
    .question-icon {
        color: @starry-text-color-secondary;
    }
    .question-icon:hover {
        color: @primary-color;
    }
    .checkbox-container {
        .checkbox-item {
            display: flex;
            align-items: center;
            .checkbox-label {
                margin-left: 8px;
            }
        }
        .checkbox-wrapper {
            margin-bottom: 13px;
        }
        .poppy-checkbox-wrapper {
            margin-bottom: 13px;
            margin-right: 8px;
            margin-left: 0px;
        }
    }
    .radiobox-wraper-container {
        margin-bottom: 4px;
        .radiobox-container {
            .radiobox-item {
                display: flex;
                align-items: center;
            }
            .poppy-radio-wrapper {
                margin-bottom: 13px;
            }
        }
    }
    .expiration-time-wrap {
        padding-left: 24px;
        &::abroad {
            padding-left: 0;
        }
        .expiration-time {
            display: flex;
            line-height: 32px;
            &::abroad {
                align-items: center;
            }
            &-describe::abroad {
                align-self: end;
                padding-bottom: 17px;
                line-height: 22px;
            }
            &-selector {
                width: 150px;
                &::abroad {
                    flex: 1;
                    width: unset;
                    margin-bottom: 0;
                }
            }
            > span,
            img {
                display: inline-block;
                margin-bottom: 24px;
                &::abroad {
                    margin-bottom: -16px;
                }
            }
            > span {
                margin-right: 8px;
                margin-left: 8px;
                &::abroad {
                    margin-right: 0;
                    margin-left: 0;
                }
            }
        }
    }
}
