import React, { useEffect, useState } from 'react';
import { useMount } from 'ahooks';
import { i18n, getAppGlobalData } from '@base-app/runtime-lib';
import LevelAndTypeSetting from './LevelAndTypeSetting';
import type { AlarmLevelDataItem } from './interface';
import {
    getTenantTemplateConfigParsed,
    templateAlarmUpdate,
    AlarmTypeItem,
} from '@/service/tenant-template';
import { useHistory } from '@base-app/runtime-lib/core';
import { getI18nText } from '@/runtime-pages/tenant-center/tenant-template/commonFun';
interface Props {
    activeTab: string;
    inheritStatus: boolean;
    openIsWhen?: (flag: boolean) => void;
}
const configType = 'alarmLevel';
const LevelSetting: React.FC<Props> = (props) => {
    const { activeTab, openIsWhen, inheritStatus } = props;
    const history = useHistory();
    const { templateId, appId } = history.location.query;
    const [levelAlarmData, setLevelAlarmData] = useState<any[]>([]); // 组合后的等级和等级下的报警类型数据
    const [levelData, setLevelData] = useState<any[]>([]); // 等级数据
    const [alarmData, setAlarmData] = useState<any[]>([]); // 报警数据
    type ParamsObject = Record<string, any>;
    // 返回不被等级或者分类关联过的报警类型数据
    const handleOtherAlarmData = (alarmData: any[], levelOrTypeData: any[]) => {
        // 遍历所有报警类型
        return alarmData.filter((alarm) => {
            // 在每个等级的关联报警中查找，若每个等级都没有关联这个报警，则返回true，最后返回这样的报警数据列表
            return levelOrTypeData.every((val) => {
                return !val.alarmTypes.includes(alarm.alarmType?.toString());
            });
        });
    };
    const fetchLevelData = async () => {
        const params: ParamsObject = {
            page: 1,
            pageSize: 1e8,
        };
        const { list } = await getTenantTemplateConfigParsed({
            ...params,
            templateId,
            configType: configType,
        });
        setLevelData(list.reverse());
    };

    const fetchAlarmData = async () => {
        const params = {
            page: 1,
            pageSize: 1e8,
        };
        const { list } = await getTenantTemplateConfigParsed({
            ...params,
            templateId,
            configType: 'alarmType',
        });
        setAlarmData(list);
    };
    // 进入页面，执行请求报警数据
    useMount(() => {
        fetchAlarmData();
    });

    useEffect(() => {
        fetchLevelData();
    }, [inheritStatus]);

    useEffect(() => {
        if (levelData) {
            // 过滤报警类型不在报警等级关联报警中，放到其它中
            const otherAlarm = handleOtherAlarmData(alarmData, levelData);
            // 组合等级和报警数据

            const levelAlarmData = levelData.map((item: any) => {
                return {
                    name: getI18nText(
                        {
                            parentId: item.uniqueCode,
                            configureName: item.levelName,
                            translationList: item.translationList,
                        },
                        'alarmLevel',
                        inheritStatus,
                    ),
                    levelName: item.levelName,
                    id: item.id,
                    levelColor: item.levelColor,
                    levelCode: item.levelCode,
                    appId: item.appId,
                    priority: item.priority,
                    translationList: item.translationList,
                    uniqueCode: item.uniqueCode,
                    alarmDataList: (item.alarmTypes || []).map((val: any) => {
                        return {
                            value: val,
                            label: i18n.t(`@i18n:@alarmType__${val}`, val),
                            disabled: false,
                        };
                    }),
                };
            });
            levelAlarmData.push({
                name: i18n.t('name', '报警类型'),
                levelName: i18n.t('name', '报警类型'),
                id: 0,
                levelColor: null,
                levelCode: 0,
                appId: null,
                priority: 0,
                uniqueCode: 0,
                translationList: [],
                alarmDataList: (otherAlarm || []).map((val) => {
                    return {
                        value: val.alarmType,
                        label: i18n.t(`@i18n:@alarmType__${val.alarmType}`, val.typeName ),
                        disabled: false,
                    };
                }),
            });
            setLevelAlarmData(levelAlarmData);
        }
    }, [levelData, alarmData]);

    const onEditSave = (data: AlarmLevelDataItem[]) => {
        const alarmLevelTypeList: AlarmTypeItem[] = data
            .map((item) => {
                return {
                    templateConfigId: item.id,
                    alarmTypes: item.alarmDataList.map((val) => val.value),
                };
            })
            .filter((item) => item.templateConfigId !== 0);
        return templateAlarmUpdate({
            configType: configType,
            alarmTypeList: alarmLevelTypeList,
            templateId,
        }).then(() => {
            // 保存成功，获取新数据
            fetchLevelData();
        });
    };

    return (
        <LevelAndTypeSetting
            activeTab={activeTab}
            data={levelAlarmData}
            onEditSave={onEditSave}
            appId={appId || getAppGlobalData('APP_ID')}
            openIsWhen={openIsWhen}
            updateData={fetchLevelData}
            templateId={templateId}
            carData={levelData}
            inheritStatus={inheritStatus}
        />
    );
};
export default LevelSetting;
