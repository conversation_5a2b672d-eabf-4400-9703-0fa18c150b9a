@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

@width: 306px;
.alarm-settings-level-data-list {
    display: block;
    padding: 0;
    overflow-x: auto;
    white-space: nowrap;
    list-style: none;
    .level-data-list-item-drag {
        display: inline-block;
        width: @width;
        height: 100%;
        margin-right: 16px;
        vertical-align: middle;
        border: 1px solid @starry-border-level-1-color;
        border-radius: 2px;
        box-shadow: none;
        &:last-child {
            margin-right: 0;
        }
        .poppy-checkbox {
            top: 0.3em;
        }
        .level-data-header {
            display: flex;
            align-items: center;
            width: 100%;
            height: 48px;
            padding: 0 16px;
            overflow: hidden;
            color: @starry-text-color-primary;
            font-weight: bold;
            font-size: 14px;
            line-height: 48px;
            white-space: nowrap;
            text-overflow: ellipsis;
            border-bottom: 1px solid @starry-border-level-1-color;
            .poppy-checkbox-wrapper {
                color: @starry-text-color-primary;
                font-size: 14px;
                line-height: 48px;
            }
            .alarm-level-title {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: space-between;
                width: calc(100% - 48px);
                .alarm-level-operate {
                    .anticon {
                        color: @primary-color;
                        cursor: pointer;
                    }
                    .operate-btn {
                        padding: 0 8px;
                    }
                }
            }
            .color-box {
                display: inline-block;
                flex-shrink: 0;
                width: 16px;
                height: 16px;
                margin-right: 8px;
                border-radius: 2px;
            }
        }
        .default-level-data-header {
            background-color: @starry-bg-color-component;
        }
        .level-data-list {
            flex-direction: column;
            box-sizing: border-box;
            height: 600px;
            padding: 0;
            overflow-y: auto;
            list-style: none;
            .level-data-alarm-item {
                position: relative;
                height: 48px;
                margin-bottom: 4px;
                padding-left: 16px;
                color: @starry-text-color-primary;
                font-size: 14px;
                line-height: 48px;
                transition: background-color 0.1s;
            }
            .relate-class-edit-normal:hover {
                background: @starry-bg-color-component;
                .relate-class-edit-normal-delete-icon {
                    display: block;
                }
            }
            .relate-class-move-alarm-item-none {
                background-color: @starry-bg-color-container !important;
            }
            .relate-class-move-alarm-item-exclusive {
                background-color: @primary-color-lighter !important;
                border: 1px solid @starry-text-color-brand;
            }
            .relate-class-move-alarm-item-include {
                border: 1px solid @starry-text-color-brand;
            }
            .relate-class-edit-normal-delete-icon {
                position: absolute;
                top: 0;
                right: 16px;
                display: none;
            }
        }
        .poppy-checkbox-group {
            height: 48px;
            padding-left: 16px;
            line-height: 48px;
        }
        .level-data-list-overflow {
            display: block;
            width: calc(@width - 70px);
            .alarm-type-item-count {
                margin-left: 6px;
                font-size: 12px;
            }
            .color-box {
                position: relative;
                top: 4px;
            }
        }
    }
}
.alarm-settings-level-setting-main {
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background: @starry-scroll-track-color;
    }

    ::-webkit-scrollbar-track {
        border-radius: 6px;
        -webkit-box-shadow: inset 0 0 6px @starry-scroll-track-color;
    }

    ::-webkit-scrollbar-thumb {
        height: 300px;
        background-color: @starry-scrollbar-color;
        border-radius: 6px;
    }
    ::-webkit-scrollbar-thumb:hover {
        background: @starry-scrollbar-hover-color;
    }

    ::-webkit-scrollbar-thumb:active {
        background: @starry-scrollbar-hover-color;
    }
}

.level-data-list-item-drag {
    display: inline-block;
    width: @width;
    height: 100%;
    margin-right: 16px;
    vertical-align: middle;
    background: @starry-bg-color-container;
    border: 1px solid @starry-border-level-1-color;
    border-radius: 2px;
    box-shadow: 0 0 16px @starry-card-box-shadow-color;
    &:last-child {
        margin-right: 0;
    }
    .poppy-checkbox {
        top: 0.3em;
    }
    .level-data-header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 48px;
        padding: 0 16px;
        overflow: hidden;
        color: @starry-text-color-primary;
        font-weight: 600;
        font-size: 14px;
        line-height: 48px;
        white-space: nowrap;
        text-overflow: ellipsis;
        border-bottom: 1px solid @starry-border-level-1-color;
        .poppy-checkbox-wrapper {
            color: @starry-text-color-primary;
            font-size: 14px;
            line-height: 48px;
        }
        .alarm-level-title {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            width: calc(100% - 48px);
            .alarm-level-operate {
                .anticon {
                    color: @primary-color;
                    cursor: pointer;
                }
                .operate-btn {
                    padding: 0 8px;
                }
            }
        }
        .color-box {
            display: inline-block;
            flex-shrink: 0;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border-radius: 2px;
        }
    }
    .default-level-data-header {
        background-color: @starry-bg-color-component;
    }
    .level-data-list {
        flex-direction: column;
        box-sizing: border-box;
        height: 600px;
        padding: 0;
        overflow-y: auto;
        list-style: none;
        .level-data-alarm-item {
            position: relative;
            height: 48px;
            margin-bottom: 4px;
            padding-left: 16px;
            color: @starry-text-color-primary;
            font-size: 14px;
            line-height: 48px;
            transition: background-color 0.1s;
        }
        .relate-class-edit-normal:hover {
            background: @starry-bg-color-component;
            .relate-class-edit-normal-delete-icon {
                display: block;
            }
        }
        .relate-class-move-alarm-item-none {
            background-color: @starry-bg-color-container !important;
        }
        .relate-class-move-alarm-item-exclusive {
            background-color: @primary-color-lighter !important;
            border: 1px solid @starry-text-color-brand;
        }
        .relate-class-move-alarm-item-include {
            border: 1px solid @starry-text-color-brand;
        }
        .relate-class-edit-normal-delete-icon {
            position: absolute;
            top: 0;
            right: 16px;
            display: none;
        }
    }
    .poppy-checkbox-group {
        height: 48px;
        padding-left: 16px;
        line-height: 48px;
    }
    .level-data-list-overflow {
        display: block;
        width: calc(@width - 70px);
        .alarm-type-item-count {
            margin-left: 6px;
            font-size: 12px;
        }
        .color-box {
            position: relative;
            top: 4px;
        }
    }
}

.level-data-list-item-drag ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: @starry-scroll-track-color;
}

.level-data-list-item-drag ::-webkit-scrollbar-track {
    border-radius: 6px;
    -webkit-box-shadow: inset 0 0 6px @starry-scroll-track-color;
}

.level-data-list-item-drag ::-webkit-scrollbar-thumb {
    height: 300px;
    background-color: @starry-scrollbar-color;
    border-radius: 6px;
}
.level-data-list-item-drag ::-webkit-scrollbar-thumb:hover {
    background: @starry-scrollbar-hover-color;
}

.level-data-list-item-drag ::-webkit-scrollbar-thumb:active {
    background: @starry-scrollbar-hover-color;
}


.alarm-settings-level-setting-main::abroad {
    .alarm-settings-level-data-list{
        padding-bottom: 15px;
    }
}
.level-data-list-item-drag::abroad ::-webkit-scrollbar {
    background: unset;
}

.level-data-list-item-drag::abroad ::-webkit-scrollbar-track {
    -webkit-box-shadow: unset;
}

.level-data-list-item-drag::abroad ::-webkit-scrollbar-thumb:hover {
    background: @starry-scrollbar-hover-color;
}

.level-data-list-item-drag::abroad ::-webkit-scrollbar-thumb:active {
    background: @starry-scrollbar-hover-color;
}


.alarm-settings-level-data-list::abroad {
    .level-data-list-item-drag::abroad {
        overflow: hidden;
        border-radius: 16px;
        .level-data-header::abroad {
            border-radius: 16px 16px 0 0;
        }
        .default-level-data-header::abroad {
            background-color: @starry-bg-color-container;
        }
        .relate-class-move-alarm-item-exclusive::abroad {
            border-radius: 8px;
        }
    }
}

.level-data-list-item-drag-abroad{
    border-radius: 16px;
}

.template-detail-ability-setting-container .alarm-settings-level-setting-main .alarm-settings-level-data-list::abroad {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
