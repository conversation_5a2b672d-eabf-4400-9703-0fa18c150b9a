@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.template-detail-preset-file-container {
    display: flex;
    gap: 24px;
    &>div>div:first-child{
        gap:24px!important;
    }
    .left-tabs {
        flex-shrink: 0;
        width: 200px;
        background: @starry-bg-color-container;
        height: 100%;
        box-sizing: border-box;
        .poppy-tabs {
            height: 100%;
        }
        .poppy-tabs-tab {
            padding: 2px 16px;
        }
        .poppy-tabs-nav {
            width: 100%;
            .poppy-tabs-tab-btn {
                max-width: 100%;
            }
        }
        .poppy-tabs-nav-operations {
            display: none;
        }
    }
    .left-tabs::abroad {
        border-radius: @border-radius-16;
        padding: 16px;
    }
    .right-content {
        flex: 1;
        overflow: hidden;
        .param-setting-table {
            .starry-table-wrapper  {
                .poppy-container:last-child::abroad {
                    margin-bottom: 0;
                }
            }
        }
        .starry-table-wrapper {
            .ability-tab-search-wrapper {
                width: 280px;
            }
        }
    }
}

// 抽屉样式
.template-detail-preset-file-container-drawer {
    height: 100%;
    .left-tabs{
        height: 100%;
        .poppy-tabs{
            height: 100%;
            .poppy-tabs-nav{
                border-left: 1px solid var(--poppy-border-color-split);
                margin-bottom: 0;
                max-width: 100%;
                .poppy-tabs-tab-btn{
                    max-width: 100%;
                }
            }
            .poppy-tabs-content-holder{
                display: none;
            }
        }
    }
}

.template-detail-preset-file-container {
    .switch-container {
        font-weight: 700;
        font-size: 20px;
        .switch-icon {
            margin-left: 8px;
            font-size: 16px;
            &::abroad {
                font-size: 20px;
            }
        }
    }
}