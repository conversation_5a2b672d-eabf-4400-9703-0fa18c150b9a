@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.tenant-template-data-inherit-container {
    display: flex;
    align-items: center;
    margin: 0 0 24px 0;
    .title {
        color: @starry-text-color-primary;
        font-weight: 600;
        font-size: 16px;
    }
    .icon {
        margin-left: 8px;
        svg {
            color: @starry-text-color-secondary;
            cursor: pointer;
        }
        svg:hover {
            color: @primary-color;
        }
    }
    .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-bottom: 2px;
        margin-left: 24px;
        background-color: #bfbfbf;
        border-radius: 50%;
    }
    .dot-active {
        background-color: @success-color;
    }
    .label {
        margin-left: 8px;
    }
    .switch {
        margin-left: 16px;
    }
}
