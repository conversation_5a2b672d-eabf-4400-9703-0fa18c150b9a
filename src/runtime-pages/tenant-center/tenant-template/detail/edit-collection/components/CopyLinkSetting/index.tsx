import { useState, useEffect } from 'react';
import { Modal, Form, Select, Tag } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';

interface CopyLinkSettingProps {
    visible: boolean;
    alarmTypeList: AlarmType[];
    onCancel?: () => void;
    onOk?: (keys: number[]) => void;
    disabledKeys?: number[] | string[];
}
export interface AlarmType {
    value: string;
    label: string;
}

const CopyLinkSetting = (props: CopyLinkSettingProps) => {
    const {
        visible = false,
        alarmTypeList = [], // 所有的报警类型列表
        onCancel = () => {},
        onOk = () => {},
        disabledKeys = [],
    } = props;

    const [form] = Form.useForm();
    const [selectType, setSelectType] = useState<any[]>([]);

    useEffect(() => {
        if (!visible) {
            setSelectType([]);
            form.resetFields();
        }
    }, [visible]);

    function handleOk() {
        form.validateFields().then((value) => {
            const { selectType } = value;
            onOk(selectType);
        });
    }

    function handleChange(value: number[], option: any[]) {
        setSelectType(option);
    }

    function onFinish(values: any) {
        const { selectType } = values;
        onOk(selectType);
    }

    function deleteType(value: number) {
        setSelectType((types) => {
            const next = types.filter((item) => item.value !== value);
            form.setFieldsValue({
                selectType: next.map((item) => item.value),
            });
            return next;
        });
    }

    return (
        <Modal
            title={i18n.t('name', '复制配置')}
            visible={visible}
            maskClosable={false}
            onOk={handleOk}
            onCancel={onCancel}
            width={420}
        >
            <Form form={form} onFinish={onFinish}>
                <Form.Item
                    name="selectType"
                    label={i18n.t('name', '报警类型')}
                    rules={[
                        {
                            required: true,
                            message: i18n.t('message', '请选择报警类型'),
                        },
                    ]}
                >
                    <Select
                        placeholder={i18n.t('message', '请选择报警类型')}
                        mode="multiple"
                        maxTagCount={1}
                        onChange={handleChange}
                        filterOption={(inputValue, option) => {
                            return option?.children
                                .toLowerCase()
                                .includes(inputValue.toLowerCase());
                        }}
                    >
                        {alarmTypeList.map((item: AlarmType) => (
                            <Select.Option
                                key={item.value}
                                value={item.value}
                                disabled={disabledKeys.includes(item.value)}
                            >
                                {item.label}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            <div>
                <div style={{ marginBottom: '12px' }}>{i18n.t('name', '已选报警')}</div>
                <div style={{height: '150px', overflowY: 'auto'}}>
                    {selectType.map((item) => (
                        <Tag
                            style={{ marginBottom: '12px' }}
                            key={item.value}
                            closable
                            onClose={() => deleteType(item.value)}
                        >
                            {item.children}
                        </Tag>
                    ))}
                </div>
            </div>
        </Modal>
    );
};

export default CopyLinkSetting;
