/*
 * @LastEditTime: 2025-03-26 10:30:21
 */
import { Auth, i18n } from '@base-app/runtime-lib';
import { isValid } from '@/utils/commonFun';
import { ParamItem } from '@/modules/strategy/types';

// 转换paramList参数
export const paramListTransfer = {
    booleanKeys: ['mapVehicleLock', 'messageRolePush', 'openVideo', 'voicePrompt'],
    arrayObjectKeys: ['messageRoleReceiver'],
    filterKeys: ['mapVehicleLock', 'messageRolePush', 'openVideo', 'voicePrompt', 'messageRoleReceiver'],
    toSubmit: (setting: any) => {
        // 组装为数组结构
        const paramList: ParamItem[] = [];
        Object.keys(setting).forEach((alarmType) => {
            if(isValid(alarmType)) {
                const itemVal: any = setting[alarmType];
                // 转成paramList前处理一波信息下发（音频+文本）参数
                Object.keys(itemVal).forEach((key) => {
                    // 如下 key 的值转为字符串（后端只存 string）
                    let paramValue = itemVal[key];
                    if (paramListTransfer.booleanKeys.includes(key)) {
                        paramValue = paramValue ? '1' : '0';
                    }
                    if (paramListTransfer.arrayObjectKeys.includes(key)) {
                        paramValue = paramValue ? paramValue?.map(item => item)?.join(',') : '';
                    }
                    if(paramListTransfer.filterKeys.includes(key)) {
                        paramList.push({
                            alarmType,
                            paramName: key,
                            paramValue,
                        });
                    }
                });
            }
        });
        // 去重
        const newArr: any[] = []; //存新数组
        paramList.forEach((item: any) => {
            const isHas = newArr.findIndex(
                (newItem) =>
                    newItem.alarmType == item.alarmType && newItem.paramName == item.paramName,
            );
            if (isHas == -1) {
                newArr.push(item);
            }
        });
        return newArr;
    },
    toRender: (paramList: any) => {
        const setting: any = {};
        paramList.forEach((item: any) => {
            const { alarmType, paramName } = item;
            let paramValue = item.paramValue;
            // 如下 key 的值转为字符串（后端只存 string）
            if (paramListTransfer.booleanKeys.includes(paramName)) {
                paramValue = paramValue == '1';
            }
            if (paramListTransfer.arrayObjectKeys.includes(paramName)) {
                paramValue = paramValue
                    ? paramValue.split(',').filter((item: string) => item != '')
                    : [];
            }
            if (!setting.hasOwnProperty(alarmType)) {
                setting[alarmType] = {};
            }
            if(paramListTransfer.filterKeys.includes(paramName)) {
                setting[alarmType][paramName] = paramValue;
            }
        });
        // 类型不同聚合不到 bug 22103
        Object.keys(setting).forEach((item) => {
            // 针对不同时区配置相同，时区不同数据处理,消息推送没有字段返回时，加上默认字段
            const itemObj = setting[item];
            if (!itemObj.hasOwnProperty('messageRolePush')) {
                itemObj.messageRolePush = false;
            }
            if (!itemObj.hasOwnProperty('messageRoleReceiver')) {
                itemObj.messageRoleReceiver = [];
            }
        });
        return setting;
    },
};
export const parseEmptyData = (settingItem: any) => {
    if (!settingItem.hasOwnProperty('messageRolePush')) {
        settingItem.messageRolePush = false;
    }
    if (!settingItem.hasOwnProperty('messageRoleReceiver')) {
        settingItem.messageRoleReceiver = [];
    }
    return settingItem;
};