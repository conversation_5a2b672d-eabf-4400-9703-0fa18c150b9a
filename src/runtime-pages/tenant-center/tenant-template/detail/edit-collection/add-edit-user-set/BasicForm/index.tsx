import { Col, Container, Form, Input, InputNumber } from '@streamax/poppy';
import { i18n, utils, UserFormItems, useCountryOption, StarryAbroadFormItem, useSystemComponentStyle, InfoBack } from '@base-app/runtime-lib';
import { useLocation } from '@base-app/runtime-lib/core';
import React, { forwardRef, useEffect, useRef, useState } from 'react';
import InternationalInput from './../../../../components/InternationalInput';
import { MODEL } from '@/runtime-pages/tenant-center/tenant-template/base/types';
import { useAsyncEffect, useUpdate } from '@streamax/hooks';
import { getSummerTimeState } from '@/service/strategy';
import './index.scoped.less';
import { RspFormLayout } from '@streamax/responsive-layout';
import VideoCloseTimeFormItem from '@/modules/strategy/UserStrategy/VideoCloseTimeFormItem';

interface BasicFormProps {
    // 区别新增（复制）、编辑
    type: 'add' | 'edit';
    internationalType: string;
    configureName?: string;
    configureType?: number;
    configureId?: string;
    isDefault?: boolean;
    TMapCode: string; // 矿山地图资源
    HaMiMapCode: string; // 哈密矿山地图资源
    onMount?: (r: any) => void;
    onInternationInfoSave?: (value: any) => void;
    onFieldsValueChange?: (values: any) => void;
    model?: MODEL;
    templateId: string;
    translationList: any[];
}

const { TextArea } = Input;
const BasicForm = (props: BasicFormProps, ref: any) => {
    const {
        isDefault,
        internationalType,
        onMount,
        configureId,
        configureName,
        translationList,
        templateId,
        type,
        onInternationInfoSave,
    } = props;

    const [isUnfoldCloseTime, setisUnfoldCloseTime] = useState(true);
    const [internationalInfo, setInternationalInfo] = useState<any>({});
    const [userBaseInfoStatus, setUserBaseInfoStatus] = useState<string>('block');
    const [countrySummerTimeTip, setCountrySummerTimeTip] = useState<string>('');
    const [userPersonalInfoStatus, setUserPersonalInfoStatus] = useState<string>('block');
    const { countryOption } = useCountryOption();
    const initRef = useRef(false);
    const update = useUpdate();
    const {
        // @ts-ignore
        query: { model },
    } = useLocation();
    const internationalRef = useRef<any>();
    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();
    useEffect(() => {
        model === MODEL.PERSONAL && setUserBaseInfoStatus('none');
        model === MODEL.BASE && setUserPersonalInfoStatus('none');
    }, [model]);

    useAsyncEffect(async () => {
        onMount?.(internationalRef.current.form);
    }, []);

    useEffect(() => {
        type == 'edit' && setisUnfoldCloseTime(false);
    }, []);

    const limitDecimals = (v: any) => {
        if (!v) return v;
        const val = Math.round(v);
        return val;
    };

    const onCountryChange = (value) => {
        if(Array.isArray(value) && value.length) {
            getSummerTimeState({
                areaCodes: value[value.length - 1],
            }).then((res) => {
                if (Array.isArray(res) && res.length) {
                    let text = i18n.t('message', '当前该国家夏令时已结束');
                    if (res[0].areaDst) {
                        text = i18n.t('message', '当前该国家已进入夏令时');
                    }
                    setCountrySummerTimeTip(text);
                    update();
                }
            });
        }
    };
    // 第一次进入的时候要查询该国家地区是否进入夏令时
    const getCountryValue = () => {
        const values = ref.current?.getFieldsValue();
        // 是否实行夏令时
        const item = countryOption.find((item: any) => item.areaCode === values.timeZone);
        if (values?.timeZone && item?.summerTimeState) {
            if (!initRef.current) {
                initRef.current = true;
                // 是否在夏令时范围内
                onCountryChange(values?.timeZone);
            }
            return true;
        }
        return false;
    };

    return (
        <Form
            className="user-policy-form"
            layout="vertical"
            scrollToFirstError={{
                behavior: 'smooth',
                block: 'center',
                scrollMode: 'always',
            }}
            onValuesChange={(chanedValues: any, allValues: any) => {
                props?.onFieldsValueChange?.(allValues);
            }}
            ref={ref}
        >
            <div className="form-layout">
                <div
                    className="layout-box"
                    style={{ display: userBaseInfoStatus, width: '100%' }}
                >
                    <Container>
                        {isAbroadStyle ? (
                            <p className="user-policy-form-title">
                                {i18n.t('name', '基本信息')}
                            </p>
                        ) : null}
                        <RspFormLayout layoutType="auto">
                        <StarryAbroadFormItem
                            label={i18n.t('name', '设置名称')}
                            name={'configureName'}
                            rules={[
                                {
                                    required: true,
                                    whitespace: true,
                                },
                                {
                                    validator: utils.validator.illegalCharacter,
                                },
                                {
                                    min: 1,
                                    type: 'string',
                                },
                                {
                                    max: 50,
                                    type: 'string',
                                },
                            ]}
                            style={{width: '100%', paddingRight: '28px'}}
                            hidden={isDefault}
                        >
                            <InternationalInput
                                allowClear
                                className="form-item"
                                // allowClear
                                maxLength={50}
                                
                                placeholder={i18n.t(
                                    'message',
                                    '请输入设置名称',
                                )}
                                internationalType={internationalType as any}
                                modalType={type}
                                entryKey={configureName}
                                entryIdOrCode={configureId}
                                ref={internationalRef as any}
                                translationList={translationList}
                                templateId={templateId}
                                onSave={(values) => {
                                    setInternationalInfo(values);
                                    onInternationInfoSave?.(values);
                                }}
                            />
                        </StarryAbroadFormItem>
                        <Form.Item
                            shouldUpdate
                            label={i18n.t('name', '设置名称')}
                            hidden={!isDefault}
                            style={{width: '100%', paddingRight: '28px'}}
                        >
                            {i18n.t(
                                `@i18n:@${internationalType}__${configureId}`,
                                configureName,
                            )}
                        </Form.Item>
                        <Col span={12} />
                        <RspFormLayout.SingleRow>
                        <StarryAbroadFormItem
                            label={i18n.t('name', '描述')}
                            name={'description'}
                            rules={[
                                {
                                    type: 'string',
                                    min: 0,
                                },
                                {
                                    type: 'string',
                                    max: 500,
                                },
                            ]}
                        >
                            <TextArea
                                className="form-textarea"
                                placeholder={i18n.t('message', '不多于500字')}
                                allowClear
                                showCount
                                style={{width: '100%'}}
                                maxLength={500}
                            />
                        </StarryAbroadFormItem>
                        </RspFormLayout.SingleRow>
                        </RspFormLayout>
                    </Container>
                </div>
                <div
                    style={{ display: userPersonalInfoStatus}}
                    className={
                        model === undefined
                            ? 'layout-box-wrap-set'
                            : 'layout-box-wrap'
                    }
                >
                    <Container>
                        {model === undefined ? (
                            <div className="user-policy-form-title">
                                {i18n.t('name', '个性化配置')}
                            </div>
                        ) : null}
                          <InfoBack style={{ margin: "24px 0"}} title={ i18n.t("message","根据选择的国家地区，系统会自动设置时间格式、度衡量单位")} />
                        <div className="layout-box">
                            <UserFormItems
                                countrySummerTimeTip={countrySummerTimeTip}
                                onCountryChange={onCountryChange}
                                getCountryValue={getCountryValue}
                                formRef={ref}
                            />
                            <RspFormLayout layoutType="auto">
                            <StarryAbroadFormItem
                                label={i18n.t('name', '报警回放提前时间')}
                                name={'devicePlaybackForeheadTime'}
                                rules={[
                                    {
                                        type: 'number',
                                        min: 5,
                                    },
                                    {
                                        type: 'number',
                                        max: 3600,
                                    },
                                ]}
                            >
                                <InputNumber
                                    className="form-item"
                                    min={5}
                                    max={3600}
                                    precision={0}
                                    placeholder={i18n.t(
                                        'message',
                                        '请输入5~3600的数字',
                                    )}
                                    formatter={limitDecimals}
                                    parser={limitDecimals}
                                />
                            </StarryAbroadFormItem>
                            </RspFormLayout>
                        </div>
                        <div
                            className="video-time-close"
                        >
                            <StarryAbroadFormItem 
                                name="autoCloseVideoConfig" 
                                style={{width: '100%'}} 
                                initialValue={{autoCloseVideo: 1}}
                            >
                                <VideoCloseTimeFormItem parentName='autoCloseVideoConfig' require={{ 'videoCloseTime': true }} />
                            </StarryAbroadFormItem>
                        </div>
                    </Container>
                </div>
            </div>
        </Form>
    );
};
export default forwardRef(BasicForm);
