import React, { useState, useEffect } from 'react';
import { i18n, StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import { Modal, Select, Form, message } from '@streamax/poppy';
import {
    fetchApplicationPageList,
    grantApplicationToTenant,
    updateAppTenantRecord,
} from '../../../../../service/application';
import { getTenantPage } from '../../../../../service/tenant';
import moment from 'moment';
import { getLanguageList } from '@/service/language';
import { flatten } from 'lodash';

const { Option } = Select;

interface OpenModalProps {
    visible: boolean;
    appId?: number | string;
    tenantId?: number | string;
    parentTenantId?: number | string;
    dateRange?: [any, any] | null;
    onCancel?: (reload: boolean) => void;
    applicationRecordId?: number | string;
    tenateName: string;
}

const OpenModal: React.FC<OpenModalProps> = (props) => {
    const {
        visible,
        onCancel,
        appId,
        tenantId,
        dateRange,
        applicationRecordId,
        parentTenantId,
        tenateName,
    } = props;

    const [form]: any = Form.useForm();
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [appList, setAppList] = useState<any[]>([]);
    const [tenantList, setTenantList] = useState<any[]>([]);

    let DetailTypeCode: string;
    let TypeCode: string;
    if (appId) {
        DetailTypeCode = 'edit';
        TypeCode = 'edit';
    } else {
        DetailTypeCode = 'open';
        TypeCode = 'create';
    }

    useEffect(() => {
        if (visible) {
            getAppList();
            getTenantList();
        }
        if (dateRange) {
            form.setFieldsValue({
                dateRange: [moment(dateRange[0] * 1000).utc(), moment(dateRange[1] * 1000).utc()],
            });
        }
    }, [visible]);

    // 获取应用列表
    function getAppList() {
        fetchApplicationPageList({
            tenantId: parentTenantId,
            states: '1',
            page: 1,
            pageSize: 10000,
        }).then((data) => {
            setAppList(data.list);
            appId &&
                form.setFieldsValue({
                    appId: +appId,
                });
        });
    }

    // 获取租户列表
    function getTenantList() {
        getTenantPage({
            page: 1,
            pageSize: 10000,
            parentId: parentTenantId,
        }).then((data) => {
            setTenantList(data.list);
            tenantId &&
                form.setFieldsValue({
                    tenantId: +tenantId,
                });
        });
    }

    // 开通应用
    async function onFinish(values: any) {
        setConfirmLoading(true);
        const { tenantId, appId } = values;
        const params = {
            appId: +appId,
            tenantId: +tenantId,
        };

        const app = appList.find((item) => item.applicationId === appId);
        const langKey = `@i18n:@app__${appId}`;
        const data = await getLanguageList({ langKey });

        if (applicationRecordId) {
            // 调整时间
            updateAppTenantRecord({
                ...params,
                applicationRecordId,
                operationModelCode: 'subtenantApplication',
                operationDetailModelCode: tenateName,
                operationTypeCode: 'edit',
                operationDetailTypeCode: DetailTypeCode,
                logParams: [
                    {
                        data: app.applicationName,
                        translationList: flatten(data?.languageList),
                    },
                ],
            })
                .then(() => {
                    message.success(i18n.t('message', '操作成功'));
                    form.setFieldsValue({});
                    onCancel?.(true);
                })
                .finally(() => {
                    setConfirmLoading(false);
                });
        } else {
            // 开通
            grantApplicationToTenant({
                ...params,
                operationModelCode: 'subtenantApplication',
                operationDetailModelCode: tenateName,
                operationTypeCode: TypeCode,
                operationDetailTypeCode: DetailTypeCode,
                logParams: [
                    {
                        data: app.applicationName,
                        translationList: flatten(data?.languageList),
                    },
                ],
            })
                .then(() => {
                    message.success(i18n.t('message', '操作成功'));
                    form.setFieldsValue({});
                    onCancel?.(true);
                })
                .finally(() => {
                    setConfirmLoading(false);
                });
        }
    }

    return (
        <Modal
            visible={visible}
            title={i18n.t('name', '开通应用')}
            className="tenant-center"
            destroyOnClose
            onCancel={() => {
                onCancel?.(false);
                form.resetFields();
            }}
            onOk={() => form.submit()}
            confirmLoading={confirmLoading}
            size="small"
            maskClosable={false}
        >
            <Form layout="vertical" form={form as any} onFinish={onFinish} preserve={false}>
                <AFormItem
                    name="tenantId"
                    label={i18n.t('name', '租户')}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Select placeholder={i18n.t('message', '请选择开通租户')} disabled={!!tenantId}>
                        {tenantList.map((tenant) => (
                            <Option value={tenant.tenantId}>{tenant.tenantName}</Option>
                        ))}
                    </Select>
                </AFormItem>
                <AFormItem
                    name="appId"
                    label={i18n.t('name', '应用')}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Select
                        disabled={!!appId}
                        showSearch
                        placeholder={i18n.t('name', '请选择开通应用')}
                        filterOption={(input, option: any) => {
                            return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                    >
                        {appList.map((app) => (
                            <Option value={app.applicationId} key={app.applicationId}>
                                {i18n.t(`@i18n:@app__${app.applicationId}`, app.applicationName)}
                            </Option>
                        ))}
                    </Select>
                </AFormItem>
            </Form>
        </Modal>
    );
};

export default OpenModal;
