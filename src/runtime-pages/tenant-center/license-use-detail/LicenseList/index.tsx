/*
 * @LastEditTime: 2024-07-31 18:23:48
 */
import { ListDataContainer } from '@streamax/starry-components';
import { useLocation } from '@base-app/runtime-lib/core';
import {
    i18n,
    utils,
    StarryAbroadOverflowEllipsisContainer,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { useEffect, useRef, useState } from 'react';
import { Badge, Container, Table, Tooltip } from '@streamax/poppy';
import { IconRefresh } from '@streamax/poppy-icons';
import {
    type LicenseData,
    LicenseDeviceBindData,
    queryLicensePackageSubPage,
    type LicenseBindDeviceData,
    getLicenseDeviceBindPage,
} from '@/service/license';
import { VALID_TIME_TYPE_ENUM } from '@/utils/license-common';
import { State } from '../LicenseState';
import StateBadge from '../StateBadge';
import uuid from '@/utils/uuid';
import { useDebounceEffect } from '@streamax/hooks';
import './index.less';
import moment from 'moment';

const { getLocalMomentByZeroTimeStamp } = utils.formator;

const classPreFix = 'license-state-list-layout';
type LicenseListProps = {
    state?: State;
    licenseId?: string;
};
type ExtendLicenseData = LicenseData & {
    expireTime: number;
    licenseStatus: number;
};
type ExtendLicenseBindDeviceData = LicenseBindDeviceData & {
    useDuration: number;
}
const stateValue = {
    unused: 0,
    unactive: 1,
    active: 2,
    expired: 3,
    nobind: 4,
    expireded: 5, // 已失效，自动过期的license过期
    invilid: 6, // 已作废
};

const stateMap = {
    used: [stateValue.active, stateValue.expired, stateValue.nobind, stateValue.unactive, stateValue.expireded, stateValue.invilid].join(','),
    nobind: [stateValue.nobind, stateValue.expireded, stateValue.invilid].join(','),
    bind: [stateValue.unactive, stateValue.active, stateValue.expired].join(','),
    active: stateValue.active,
    unactive: stateValue.unactive,
    expired: stateValue.expired,
};

const everyDay = -1;

const bindStatus = {
    bind: 1,
    nobind: 0,
};

const LicenseList = (props: LicenseListProps) => {
    const { state } = props;
    const [currentLicense, setCurrentLicense] = useState<ExtendLicenseData>(
        {} as ExtendLicenseData,
    );
    const [listData, setListData] = useState<ExtendLicenseData[]>([]);
    const listDataContainerRef = useRef<any>(null);
    const [sortType, setSortType] = useState('');

    const { isDark } = useSystemComponentStyle();

    const location = useLocation() as any;
    const { packageId } = location.query || {};
    const requestKeyRef = useRef('');

    // 查询左侧列表
    const queryStateLicense = async (uid: string) => {
        if (!state) return;
        const { list } = await queryLicensePackageSubPage({
            packageId,
            licenseStatus: stateMap[state] as string,
            pageSize: 1e6,
        });
        if (requestKeyRef.current !== uid) return;
        (list || []).sort((a, b) => a.licenseNumber - b.licenseNumber);
        // 设置无限期的为无限大好排序
        list.forEach((item: any, index) => {
            item.leftRemainder = item.remainder === everyDay ? Infinity : item.remainder;
            item.licenseIndex = index + 1;
        });
        // 排序
        if(sortType){
            sortData(list, sortType);
        }
        setListData(list);
        if (list.length) {
            setCurrentLicense(list[0]);
        } else {
            setCurrentLicense({} as ExtendLicenseData);
        }
    };
    
    const handleQuery = () => {
        const uid = uuid();
        requestKeyRef.current = uid;
        queryStateLicense(uid);
    };
    useEffect(() => {
        handleQuery();
    }, [state]);

    useDebounceEffect(
        () => {
            listDataContainerRef.current?.loadDataSource({
                page: 1,
            });
        },
        [currentLicense],
        { wait: 300 },
    );
    // 查询右侧列表
    const getDataSource = async (params: any) => {
        if (currentLicense && currentLicense.licenseId) {
            const result = await getLicenseDeviceBindPage({
                ...params,
                licenseId: currentLicense.licenseId,
            });
            (result.list || []).forEach((record: LicenseBindDeviceData) => {
                let useDuration = 0;
                const { bindingStatus } = record;
                if (!record.activationTime) {
                    useDuration = 0;
                } else if (bindingStatus === bindStatus.bind) {
                    if (currentLicense.licenseStatus === stateValue.active) {
                        // 如果是绑定中，且license激活中，则用当前时间减去激活时间
                        const activationTimeMoment = getLocalMomentByZeroTimeStamp(
                            record.activationTime,
                        );
                        useDuration = moment().diff(activationTimeMoment, 'days');
                    } else if(currentLicense.licenseStatus === stateValue.expired) {
                        // 如果是绑定中，但license已过期，则用过期时间减去激活时间
                        useDuration = moment(currentLicense.expireTime * 1000).diff(
                            moment((record.activationTime as number) * 1000),
                            'days',
                        );
                    } else {
                        useDuration = 0;
                    }
                } else if (
                    currentLicense.validTimeType === VALID_TIME_TYPE_ENUM.BUYOUT ||
                    (record.unBindingTime as number) < currentLicense.expireTime || 
                    currentLicense.licenseStatus !== stateValue.expired
                ) {
                    // 如果license是永久有效
                    useDuration = moment((record.unBindingTime as number) * 1000).diff(
                        moment((record.activationTime as number) * 1000),
                        'days',
                    );
                } else if (currentLicense.expireTime) {
                    useDuration = moment(currentLicense.expireTime * 1000).diff(
                        moment((record.activationTime as number) * 1000),
                        'days',
                    );
                }
                (record as ExtendLicenseBindDeviceData).useDuration = useDuration;
            });
            return result;
        } else {
            return Promise.resolve({ list: [], page: 1, pageSize: 10, total: 0 });
        }
    };
    const getBadge = (type: 'success' | 'warning' | 'error' | 'default') => {
        if (type === 'default') {
            return <Badge color={isDark ? 'rgba(255,255,255,0.25)' : 'rgba(0,0,0,0.25)'} />;
        }
        return <Badge status={type} />;
    };
    const columns = [
        {
            title: i18n.t('name', '车辆设备'),
            dataIndex: 'deviceAlias',
            render: (deviceAlias: any, record: LicenseBindDeviceData) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {deviceAlias || record.deviceNo || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            render: (vehicleNumber: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {vehicleNumber || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '绑定状态'),
            dataIndex: 'bindingStatus',
            ellipsis: true,
            render: (bindingStatus: number) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {bindingStatus === bindStatus.bind
                            ? getBadge('success')
                            : getBadge('default')}{' '}
                        {bindingStatus === bindStatus.bind
                            ? i18n.t('state', '绑定中')
                            : i18n.t('state', '已解绑')}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '使用时长（天）'),
            dataIndex: 'useDuration',
            ellipsis: true
        },
        {
            title: i18n.t('name', '激活时间'),
            dataIndex: 'activationTime',
            render: (text: number) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '解绑时间'),
            dataIndex: 'unBindingTime',
            render: (text: number) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
    ];
    const leftColumns = [
        {
            title: i18n.t('name', 'License序号'),
            width: 125,
            dataIndex: 'licenseIndex',
            width: 100,
            render: (licenseIndex: number) => `License_${licenseIndex}`,
        },
        {
            title: i18n.t('name', '剩余使用时长（天）'),
            dataIndex: 'remainder',
            ellipsis: { showTitle: false },
            sorter: true,
            render: (remainder: number) =>
                remainder === everyDay ? i18n.t('name', '永久有效') : remainder,
        },
        {
            title: () => {
                return (
                    <div className="left-list-state-title">
                        <span>{i18n.t('name', '状态')}</span>
                        <Tooltip title={i18n.t('name', '刷新')}>
                            <IconRefresh onClick={handleQuery} className="refresh-icon" />
                        </Tooltip>
                    </div>
                );
            },
            width: 125,
            dataIndex: 'licenseStatus',
            width: 140,
            render: (licenseStatus: number) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <StateBadge state={licenseStatus} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
    ];

    const handleClickRow = (record: ExtendLicenseData) => {
        setCurrentLicense(record);
    };
    // @ts-ignore
    const onChange = (pagination, filters, sorter, extra) => {
        const sortList = sortData(listData, sorter.order);
        setSortType(sorter.order);
        setListData(sortList);
    };
    const sortData = (list: LicenseData[], order: string) => {
        if (order == 'descend') {
            (list || []).sort((a, b) => b.leftRemainder - a.leftRemainder);
        } else {
            (list || []).sort((a, b) => a.leftRemainder - b.leftRemainder);
        }
        return list;
    };
    return (
        <div className={`${classPreFix}`}>
            <div className="list-left">
                <Container>
                    <Table
                        scroll={{ y: 600 }}
                        columns={leftColumns as any}
                        rowKey="id"
                        dataSource={listData as any}
                        pagination={false}
                        onRow={(record: ExtendLicenseData) => {
                            return { onClick: () => handleClickRow(record) };
                        }}
                        rowClassName={(record: ExtendLicenseData) => {
                            return record.licenseId === currentLicense?.licenseId
                                ? 'row-selected'
                                : '';
                        }}
                        onChange={onChange}
                    />
                </Container>
            </div>
            <div className="list-right">
                <ListDataContainer
                    ref={listDataContainerRef}
                    loadDataSourceOnMount={false}
                    getDataSource={getDataSource}
                    toolbar={{
                        hasReload: false,
                    }}
                    listRender={(data: LicenseBindDeviceData[]) => {
                        return <Table columns={columns} dataSource={data} rowKey={'id'} />;
                    }}
                />
            </div>
        </div>
    );
};
export default LicenseList;
