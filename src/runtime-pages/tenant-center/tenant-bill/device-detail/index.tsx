import {i18n, StarryAbroadOverflowEllipsisContainer, useUrlSearchStore} from '@base-app/runtime-lib';
import {
    StarryBreadcrumb,
    StarryCard,
    // @ts-ignore
} from '@base-app/runtime-lib';
import { ListDataContainer, Table } from '@streamax/starry-components';

import {Badge, Form, Input, Select, Space, TableColumnSetting} from '@streamax/poppy';
import React, { useEffect, useRef } from 'react';
import type { ListDataContainerProps } from '@streamax/starry-components/lib/list-data-container';
import { utils } from '@base-app/runtime-lib';
import DateRange from '@/components/DateRange';
import { getInitTimeRange, getPickerRanges } from '@/utils/commonFun';
import moment from 'moment';
import { getTenantBillDevicePage } from '@/service/tenant';
import {Instances, ListPageQueryForm, ListPageTableBase, PageBase} from "@/types/pageReuse/pageReuseBase";
import {getCustomItems, getCustomJsx, runCustomFun} from "@/utils/pageReuse";
import {useTimeUrlStorageTransform} from "@/hooks/useTimeUrlStorageTransform";
import {withShareRootHOC} from "@streamax/page-sharing-core";

const { timestampToZeroTimeStamp } = utils.formator;

enum VehicleStateEnum {
    enable = 1,
    disable = 2,
}
/**列表页table定制复用 */
export type DeviceDetailProps = PageBase & ListPageTableBase & Instances & ListPageQueryForm;

const TenantBillDeviceDetail = (props: DeviceDetailProps) => {
    /**列表页table定制复用 */
    const {
        getColumns,
        injectSearchList,
        getQueryForm,
        getTableLeftRender,
        getColumnSetting,
        getInstances
    } = props;
    /**end */
    const { localTimeTransform } = useTimeUrlStorageTransform();
    const searchStore = useUrlSearchStore();
    const { billId } = searchStore.get();
    const containerRef = useRef<any>(null);
    const [form] = Form.useForm();

    const billingStatusOptions = [
        { label: i18n.t('state', '计费设备'), value: 1 },
        { label: i18n.t('state', '未计费设备'), value: 0 },
    ];

    useEffect(() => {
        const {
            page,
            pageSize,
            billingState,
            deviceNo,
            fleetName,
            activatedTimeStart,
            activatedTimeEnd,
            tenantCode,
            /** 设备SIM卡号
             *  中台后端已经支持，前端开放复用供行业层覆写，需要支持该参数的搜索条件保存
             * */
            simNo
        } = searchStore.get();
        const state = billingStatusOptions.find(i=>i.value == billingState) ? Number(billingState) : undefined
        const parseDeviceNo = deviceNo ? decodeURIComponent(deviceNo) : undefined;
        const parseFleetName = fleetName ? decodeURIComponent(fleetName) : undefined;
        const parseTenantCode = tenantCode ? decodeURIComponent(tenantCode) : undefined;
        const parseSimNo = simNo ? decodeURIComponent(simNo) : undefined;
        const param = {
            billingState: state,
            deviceNo: parseDeviceNo,
            fleetName: parseFleetName,
            tenantCode: parseTenantCode,
            simNo: parseSimNo,
            timeRange: localTimeTransform(activatedTimeStart, activatedTimeEnd)
        }
        form.setFieldsValue({
            ...param,
        });
        containerRef.current?.loadDataSource({
            ...param,
            page: Number(page) || 1,
            pageSize: Number(pageSize) || 20,
        });
    }, []);


    runCustomFun(getInstances, {
        form,
        table: containerRef.current,
    });



    const queryFormItems = [
        {
            label: i18n.t('name', '设备编号'),
            name: 'deviceNo',
            field: Input,
            fieldProps: {
                placeholder: i18n.t('message', '请输入设备编号'),
                allowClear: true,
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '激活日期'),
            name: 'timeRange',
            field: DateRange,
            colSize: 2,
            fieldProps: {
                maxInterval: {
                    value: 365,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    allowClear: true,
                    ranges: getPickerRanges(),
                    style: {
                        width: '100%',
                    },
                },
            },
        },
        {
            label: i18n.t('name', '计费状态'),
            name: 'billingState',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('message', '请选择设备计费状态'),
                options: billingStatusOptions,
                allowClear: true,
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'fleetName',
            field: Input,
            fieldProps: {
                placeholder: i18n.t('message', '请输入车组名称'),
                allowClear: true,
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '归属租户'),
            name: 'tenantCode',
            field: Input,
            fieldProps: {
                placeholder: i18n.t('message', '请输入租户编码'),
                allowClear: true,
                maxLength: 50,
            },
        },
    ]

    const queryForm: ListDataContainerProps['queryForm'] = {
        // @ts-ignore
        items:  getCustomItems(getQueryForm, queryFormItems, undefined),
        form,
    };

    const columns = [
        {
            title: i18n.t('name', '序号'),
            dataIndex: 'index',
            key: 'index',
        },
        {
            title: i18n.t('name', '设备编号'),
            dataIndex: 'deviceNo',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '关联车牌号'),
            dataIndex: 'vehicleNumber',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetNames',
            render: (text: any, record: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '车辆状态'),
            dataIndex: 'vehicleState',
            ellipsis: true,
            render(text: number) {
                if (text === VehicleStateEnum.enable) {
                    return (
                        <span>
                            <Badge status="success" />
                            {i18n.t('state', '启用')}
                        </span>
                    );
                } else if (text === VehicleStateEnum.disable) {
                    return (
                        <span>
                            <Badge status="error" />
                            {i18n.t('state', '停用')}
                        </span>
                    );
                }
                return '-';
            },
        },
        {
            title: i18n.t('name', '归属租户'),
            dataIndex: 'tenantCode',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '激活日期'),
            dataIndex: 'activateDateString',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '首次上线时间'),
            dataIndex: 'firstOnlineTimeString',
            ellipsis: true,
        },
    ].filter((item) => item);

    const newColumns = getCustomItems(getColumns, columns);
    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(newColumns, {
        storageKey: '@base:@page:tanant.settlement.bill:device.detail',
        ...getColumnSetting?.(),
    });

    const fetchData = async (params: {
        timeRange?: [number, number];
        deviceNo: string;
        fleetName: string;
        tenantCode: string;
        activatedTimeStart: number;
        activatedTimeEnd: number;
        simNo?: string;
    }) => {
        if (injectSearchList) {
            return await injectSearchList({
                billId,
                ...params,
            })
        }
        const { timeRange, deviceNo,tenantCode, fleetName, activatedTimeStart,activatedTimeEnd, simNo } = params;
        let startTime = activatedTimeStart ||  undefined;
        let endTime = activatedTimeEnd || undefined;
        if (timeRange) {
            startTime = timestampToZeroTimeStamp(timeRange[0]);
            endTime = timestampToZeroTimeStamp(timeRange[1]);
        }
        delete params.timeRange;
        searchStore.set(
            {
                ...searchStore.get(),
                ...params,
                deviceNo: deviceNo? encodeURIComponent(deviceNo) : undefined,
                fleetName: fleetName? encodeURIComponent(fleetName) : undefined,
                tenantCode: tenantCode? encodeURIComponent(tenantCode) : undefined,
                /** 设备SIM卡号
                 *  中台后端已经支持，前端开放复用供行业层覆写，需要该字段的搜索条件保存
                 * */
                simNo: simNo? encodeURIComponent(simNo) : undefined,
                billId,
                activatedTimeStart: startTime || undefined,
                activatedTimeEnd: endTime || undefined,
            }
        )
        const data = await getTenantBillDevicePage({
            ...params,
            deviceNo: (deviceNo || '').trim() || undefined,
            fleetName: (fleetName || '').trim()  || undefined,
            tenantCode: (tenantCode || '').trim()  || undefined,
            /** 设备SIM卡号
             *  中台后端已经支持，前端开放复用供行业层覆写，请求接口需要支持该参数
             * */
            simNo: (simNo || '').trim()  || undefined,
            billId,
            activatedTimeStart: startTime || undefined,
            activatedTimeEnd: endTime || undefined,
        });
        data.list = data.list.map((item: any, index: number) => ({
            ...item,
            index: index + 1,
        }));
        return data;
    };

    return (
        <StarryBreadcrumb>
            <StarryCard className="ap-setting">
                <ListDataContainer
                    queryForm={queryForm}
                    loadDataSourceOnMount={false}
                    // @ts-ignore
                    getDataSource={fetchData}
                    ref={containerRef}
                    toolbar={{
                        extraLeft: (
                            <Space>
                                {getCustomJsx(
                                    getTableLeftRender,
                                    [],
                                )}
                            </Space>
                        ),
                        extraIconBtns: [
                            <TableColumnSetting {...tableColumnSettingProps} />,
                        ],
                    }}
                    // @ts-ignore
                    listRender={(data) => {
                        return (
                            <Table
                                columns={tableColumns}
                                dataSource={data}
                                pagination={false}
                                rowKey="deviceId"
                            />
                        );
                    }}
                />
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(TenantBillDeviceDetail);
