@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.evidence-done {
    .query-form-container {
        padding-bottom: 0 !important;
     }
    .query-form-custom-picker {
        .poppy-row {
            .poppy-form-item-control-input {
                .poppy-form-item-control-input-content {
                    .poppy-picker {
                        width: 100%;
                    }
                    .poppy-new-picker {
                        width: 100%;
                    }
                }
            }
        }
    }
    .evidence-pagination-box-has-container::abroad {
        background: @starry-bg-color-layout;
        backdrop-filter: blur(6px);
    }

    .switch-layout-container {
        // margin-bottom: 10px;
        // text-align: right;
        // line-height: 1;
        display: flex;
        justify-content: space-between;
        .layout-type-button {
            color: @starry-text-color-primary;
            font-size: 16px;
            line-height: 1;
            cursor: pointer;
            &.selected {
                color: @primary-color;
            }
        }
    }
    .card-top-area {
        .switch-layout-container {
            margin-bottom: 8px;
        }
    }
    .video-library-pagination {
        float: right;
    }
    .layout-type-button {
        color: @starry-text-color-primary;
        font-size: 16px;
        line-height: 1;
        cursor: pointer;
        &.selected {
            color: @primary-color;
        }
    }
    .layout-type-button::abroad {
        font-size: 20px;
    }
    .card-container {
        position: relative;
        margin-top: 24px;
        margin-bottom: 9px;
        border-bottom: 0;
    }
    .vspin-container {
        position: absolute;
        top: 0;
        bottom: 0;
        z-index: 100;
        width: 100%;
        background: rgba(255, 255, 255, 0.6);
        .poppy-spin {
            position: absolute;
            top: 45%;
            left: 50%;
            margin-left: -10px;
        }
    }
    .evidence-list-table-scroll {
        .poppy-table-sticky-scroll {
            display: none;
        }   
    }
}
.share-dropdown .evidence-list-dropdown-icon::abroad{
    font-size: 20px !important;
}
