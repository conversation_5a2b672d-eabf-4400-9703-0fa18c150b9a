/*
 * @LastEditTime: 2024-04-29 21:23:39
 */
import React, { forwardRef, useRef, useState, useImperativeHandle, useEffect } from 'react';
import './index.less';
import { useDebounceFn, useScroll } from '@streamax/hooks';
import type { VehicleItem, PlayMode } from '../index';
import { DeviceItem } from '@/components/StarryPlayer/types';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import {
    AiIconArrowLeft,
    AiIconArrowRight,
    BusIconClose,
    IconAddFill,
    IconCloseCircleFill,
} from '@streamax/poppy-icons';
import { Tooltip, message } from '@streamax/poppy';
import { getAppGlobalData, i18n } from '@base-app/runtime-lib';
import ContextHandle from '../ContextHandle';
import { VehicleOrderState } from '@/service/vehicle';
import { StarryPlayer } from '@base-app/runtime-lib';
import { StateActive } from '@/modules/device/useRTDeviceData';
import { useRTDataStore } from '@/modules/vehicle-tree/data/realtime-data/useRTDataManager';

const { PlayerContextProvider } = StarryPlayer;

const operatePrefix = 'scroll-operate';
type DeviceListMenuProps = {
    deviceList: DeviceItem[];
    currentDevice: DeviceItem | null
    recordStateMessage: () => boolean;
    vehicleStateConfig: VehicleOrderState[];
    alarmVehicleList: VehicleItem[];
    playMode?: PlayMode | null;
    allVehicleMap: Record<string, string[]>;
    multi: boolean;
    onDeviceSelect: (deviceInfo: DeviceItem | null) => void; //设备选择切换
    onDeviceClose: (deviceInfo: DeviceItem) => void;
    onCloseAll: () => void; //关闭所有设备视频
    onSaveMonitorGroup: (deviceList: DeviceItem[]) => void; //保存监控组
};
type DeviceListMenuRef = {
    scrollToDevice: (deviceId: number) => void;
};
const itemWidth = 142;
const ALARM_CODE = '1003';
const DeviceListMenu: React.ForwardRefRenderFunction<DeviceListMenuRef, DeviceListMenuProps> = (
    {
        deviceList,
        currentDevice,
        playMode,
        multi,
        recordStateMessage,
        vehicleStateConfig,
        onDeviceSelect,
        onDeviceClose,
        onCloseAll,
        onSaveMonitorGroup,
        allVehicleMap,
    },
    ref,
) => {
    const scrollRef = useRef<any>(null);
    const takeUpPlaceRef = useRef<HTMLDivElement>(null);
    const scroll = useScroll(scrollRef);
    const vehicleStateMapRef = useRef<Record<string, string[]>>({});
    const [pageLeftState, setPageLeftState] = useState(true);
    const [pageRightState, setPageRightState] = useState(true);
    const [selectedDevice, setSelectedDevice] = useState<DeviceItem | null>(currentDevice);
    const [activeDeviceId, setActiveDeviceId] = useState<string | null>(null);
    const emptyDom = useRef<any>(null);
    const [themeColorRgb, setThemeColorRgb] = useState('');
    const themeColor = getAppGlobalData('APP_THEME') || themeColorRgb;

    // 翻页状态
    useEffect(() => {
        calculateScroll();
        setEmptyDivWidth();
    }, [scroll]);
    useEffect(() => {
        const resizeObserverInstance = resizeObserver();
        // 开始观察目标元素
        resizeObserverInstance.observe(scrollRef.current);
    }, []);
    const resizeObserver = () => {
        // 创建一个ResizeObserver实例并定义回调函数
        return new ResizeObserver((entries) => {
            calculateScroll();
            setEmptyDivWidth();
        });
    };

    const _calculateScroll = () => {
        // 下一页
        // @ts-ignore
        if (scrollRef.current?.scrollWidth - scroll?.left === scrollRef.current?.clientWidth) {
            setPageRightState(true);
        } else {
            setPageRightState(false);
        }
        // 上一页
        // @ts-ignore
        if (scroll?.left < 1) {
            setPageLeftState(true);
        } else {
            setPageLeftState(false);
        }
    };
    const { run: calculateScroll } = useDebounceFn(_calculateScroll, {
        wait: 60,
    });
    useEffect(() => {
        calculateScroll();
        setEmptyDivWidth();
    }, [deviceList]);

    const _setEmptyDivWidth = () => {
        const remainingWidth = scrollRef.current?.clientWidth % itemWidth;
        if (takeUpPlaceRef.current) takeUpPlaceRef.current.style.width = `${remainingWidth}px`;
        try {
            const number = scroll?.left / itemWidth;
            scrollRef.current?.scrollTo(itemWidth * Math.ceil(number), 0);
        } catch (error) {
            console.error(error);
        }
    };
    const { run: setEmptyDivWidth } = useDebounceFn(_setEmptyDivWidth, {
        wait: 30,
    });
    const onDeviceClick = (deviceInfo: DeviceItem) => {
        if (recordStateMessage?.()) return;
        let currentDevice = null;
        if (!selectedDevice || selectedDevice?.deviceId !== deviceInfo?.deviceId) {
            if (deviceInfo.flowLimit) {
                message.warn({
                    content: i18n.t('message', '流量使用超额，功能暂停使用'),
                    key: 'flowLimit',
                });
                return;
            }
            currentDevice = deviceInfo;
        }
        onDeviceSelect?.(currentDevice);
        setSelectedDevice(currentDevice);
    };
    const { run: debounceOnDeviceClick } = useDebounceFn(onDeviceClick, {
        wait: 500,
    });

    const onDeviceTagClose = (deviceInfo: DeviceItem) => {
        if (recordStateMessage?.()) return;
        onDeviceClose?.(deviceInfo);
        // if (deviceInfo.deviceId === selectedDevice?.deviceId) {
        //     setSelectedDevice(null);
        // }
    };
    const onCloseAllDevice = () => {
        if (recordStateMessage?.()) return;
        onCloseAll?.();
    };
    const turnPage = (isNext: boolean) => {
        const pageCount = Math.floor(scrollRef.current?.clientWidth / itemWidth);
        const pageWidth = pageCount * itemWidth;
        if (isNext) {
            // @ts-ignore
            scrollRef.current?.scrollTo(scroll?.left + pageWidth, 0);
        } else {
            if (pageRightState && deviceList.length / pageCount > 2) {
                scrollRef.current?.scrollTo((deviceList.length - pageCount * 2) * itemWidth, 0);
            } else {
                // @ts-ignore
                scrollRef.current?.scrollTo(scroll?.left - pageWidth, 0);
            }
        }
    };
    // 滚动到指定设备
    const scrollToDevice = (deviceId: string) => {
        const deviceIndex = deviceList.findIndex((item) => item.deviceId === deviceId);
        if (deviceIndex === -1) return;
        const pageWidth = deviceIndex * itemWidth;
        scrollRef.current?.scrollTo(pageWidth, 0);
    };
    const onActiveChannelChange = (channel: string) => {
        if (typeof channel === 'string') {
            const deviceId = channel?.split('-')[0];
            setActiveDeviceId(deviceId);
            scrollToDevice(deviceId);
        }
    };

    useEffect(() => {
        if (currentDevice && deviceList.length > 0 && currentDevice !== selectedDevice) {
            setSelectedDevice(currentDevice);
        }
    }, [currentDevice]);

    useEffect(() => {
        // 主题色可能没配置，所以要重新获取
        const domStyle = window.getComputedStyle(emptyDom.current);
        const rgb = getRGBValues(domStyle.color);
        if (rgb) {
            setThemeColorRgb(rgbToHex(rgb.R, rgb.G, rgb.B));
        }
    }, []);
    useEffect(() => {
        multi && setSelectedDevice(null);
    }, [multi]);
    useImperativeHandle(ref, () => {
        return {
            scrollToDevice,
            checkDevice: (deviceInfo: DeviceItem) => {
                onDeviceSelect?.(deviceInfo);
                setSelectedDevice(deviceInfo);
            },
        };
    });
    const renderDeviceTag = (device: DeviceItem) => {
        let name = device.vehicleNumber;
        // 多设备车辆才展示设备别名和编号
        if (allVehicleMap[device.vehicleId]?.length > 1) {
            if (device.deviceAlias) {
                name = name + `（${device.deviceAlias}）`;
            } else {
                name = name + `（${device.deviceNo}）`;
            }
        }
        return name;
    };
    const getRGBValues = (rgbString: string) => {
        const match = rgbString.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
        return match
            ? {
                  R: parseInt(match[1], 10),
                  G: parseInt(match[2], 10),
                  B: parseInt(match[3], 10),
              }
            : null;
    };
    const rgbToHex = (red: number, green: number, blue: number) => {
        const toHex = (colorValue: number) => {
            const hex = colorValue.toString(16);
            return hex.length == 1 ? '0' + hex : hex;
        };
        return '#' + toHex(red) + toHex(green) + toHex(blue);
    };
    const hexToRgba = (hexColor: string, alpha = 1) => {
        if (!hexColor) return '';
        // 使用正则表达式移除可能的前导#符号
        const hex = hexColor.replace(/^\s*#|\s*$/g, '');
        // 将16进制的红、绿、蓝分量转换为十进制
        const red = parseInt(hex.substring(0, 2), 16);
        const green = parseInt(hex.substring(2, 4), 16);
        const blue = parseInt(hex.substring(4, 6), 16);
        return `rgba(${red}, ${green}, ${blue}, ${alpha})`;
    };
    const rtDataMap = useRTDataStore((state)=>state.rtDataMap);
    const getColorByDevice = (device: DeviceItem, type: 'border' | 'background') => {
        // 找出激活状态且优先级最高的状态的 color
        const vId = device?.vehicleId;
        const activeStateList = (
            (rtDataMap[vId]?.deviceList || []).find((i) => i.deviceId === device.deviceId)
                ?.stateList || []
        ).filter((i) => Number(i.stateActive) === Number(StateActive.ACTIVE));
        let statusCode = vehicleStateConfig.find((item) =>
            activeStateList.some((i) => i.stateId === item.stateId),
        )?.stateCode;
        let color = themeColor;
        if (statusCode && statusCode.indexOf(ALARM_CODE) > -1) {
            color =
                vehicleStateConfig?.find((i: any) => i.stateCode === ALARM_CODE)?.stateColor ||
                '#D9D9D9';
        }
        if (selectedDevice?.deviceId === device?.deviceId) {
            return color;
        }
        if (activeDeviceId === device?.deviceId) {
            return type === 'background' ? hexToRgba(color, 0.25) : color;
        }
        // 只要报警 就展示颜色
        if (statusCode && statusCode.indexOf(ALARM_CODE) > -1) {
            return type === 'background' ? hexToRgba(color, 0.25) : color;
        }
        return '';
    };

    return (
        // @ts-ignore
        <div className="multi-video-device-list-wrap">
            <div
                className="multi-video-device-list"
                ref={scrollRef}
                style={{ height: '60px', overflowX: 'auto', border: '1px solid', width: '100%' }}
            >
                {deviceList.map((device) => {
                    return (
                        <span
                            key={device.deviceId}
                            className={`device-item 
                    ${activeDeviceId === device?.deviceId ? 'device-item-active' : ''}
                    ${selectedDevice?.deviceId === device?.deviceId ? 'device-item-check' : ''}`}
                            style={{
                                backgroundColor: getColorByDevice(device, 'background'),
                                borderColor: getColorByDevice(device, 'border'),
                            }}
                            onClick={(e) => {
                                e.stopPropagation();
                                debounceOnDeviceClick(device);
                            }}
                        >
                            <OverflowEllipsisContainer
                                tooltip={{
                                    preventBubble: true,
                                }}
                            >
                                {renderDeviceTag(device)}
                            </OverflowEllipsisContainer>
                            <div
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onDeviceTagClose(device);
                                }}
                            >
                                <BusIconClose />
                            </div>
                        </span>
                    );
                })}
                {/* 防止滚动到最后一页出现半截的场景 */}
                <div ref={takeUpPlaceRef} className="take-up-place"></div>
            </div>
            <div ref={emptyDom} className="empty-dom"></div>
            <div className={operatePrefix}>
                <Tooltip overlay={i18n.t('name', '上一页')}>
                    <div
                        className={`${operatePrefix}-page-button ${
                            pageLeftState ? `${operatePrefix}-page-button-disabled` : ''
                        } `}
                        onClick={() => turnPage(false)}
                    >
                        <AiIconArrowLeft />
                    </div>
                </Tooltip>
                <Tooltip overlay={i18n.t('name', '下一页')}>
                    <div
                        className={`${operatePrefix}-page-button ${
                            pageRightState ? `${operatePrefix}-page-button-disabled` : ''
                        } `}
                        onClick={() => turnPage(true)}
                    >
                        <AiIconArrowRight />
                    </div>
                </Tooltip>
                {playMode === 'normal' ? (
                    <div className={`${operatePrefix}-button ${operatePrefix}-button-add`}>
                        <Tooltip placement="topLeft" overlay={i18n.t('name', '保存监控组')}>
                            <IconAddFill onClick={() => onSaveMonitorGroup(deviceList)} />
                        </Tooltip>
                    </div>
                ) : null}
                <div className={`${operatePrefix}-button`}>
                    <Tooltip placement="topLeft" overlay={i18n.t('name', '关闭视频')}>
                        <IconCloseCircleFill onClick={onCloseAllDevice} />
                    </Tooltip>
                </div>
            </div>
            <PlayerContextProvider>
                <ContextHandle onActiveChannelChange={onActiveChannelChange} />
            </PlayerContextProvider>
        </div>
    );
};
export default forwardRef(DeviceListMenu);
