/*
 * @LastEditTime: 2024-04-24 16:01:38
 */

import { useLocation } from '@base-app/runtime-lib/core';
import { StarryPlayer } from '@base-app/runtime-lib';

const {
    templates: { LiveVideoMoreSetting },
} = StarryPlayer;

const MultiMoreSetting = () => {
    const { pathname } = useLocation();
    const getMoreSettingsList = (components: any) => {
        const showList = [
            'FrameRateAndBitRate',
            'PlayMode',
            'LayoutCount',
            'PlayAspectRatio',
        ];
        const filterComponents = (components || []).filter((component) =>
            showList.includes(component.componentName),
        );
        return filterComponents;
    };
    return (
        <LiveVideoMoreSetting
            key="LiveVideoMoreSetting"
            getMoreSettingsList={getMoreSettingsList}
            defaultMoreSettingCacheKey={'collected-key-multi-' + pathname}
        />
    );
};
export default MultiMoreSetting;
