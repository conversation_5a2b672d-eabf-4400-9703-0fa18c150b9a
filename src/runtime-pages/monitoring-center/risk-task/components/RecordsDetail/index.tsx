import VolumePlayer from '@/components/VolumePlayer';
import { fetchFileUrls } from '@/service/fms';
import { fetchFileDownloadUrl } from '@/service/gss';
import { getAllHandleRecords } from '@/service/risk';
import { getOperateInfo } from '@/service/s17-media';
import { message, Popover, Row, Table, Tooltip, Modal } from '@streamax/poppy';
import type { PopoverProps } from '@streamax/poppy/lib/popover';
import type { ColumnType } from '@streamax/poppy/lib/table';
import { i18n, utils } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import React, { useState } from 'react';
import './index.less';
import { judgeIsDisplay } from '@/utils/util';
import { IconClose02Line } from '@streamax/poppy-icons';

type Placement = PopoverProps['placement'];

interface RecordsProps {
    titleText: React.ReactNode;
    placement?: Placement;
    riskId: string;
}

interface Record {
    id: string;
    handleTime: number;
    handleType: number;
    handleTypeDesc: string;
    content: string;
    seizeUserAccount: string;
}

const LOOK_TASk = 1;
const TETX_SEND = 2;
const AUDIO_SEND = 3;
const START_TALK = 4;
const END_TALK = 5;
const ACCEPT_TASK = 6;
const HANDLED = 7;

const RecordsDetail: React.FC<RecordsProps> = (props) => {
    const { children, titleText, placement, riskId } = props;
    const [popOpen, setPopOpen] = useState(false);
    const [list, setList] = useState<Record[]>([]);
    const [volumeInfo, setVolumeInfo] = useState<any>();
    const [loading, setLoading] = useState(false);

    const handleType_map = {
        1: i18n.t('name', '查看任务'),
        2: i18n.t('name', '文本下发'),
        3: i18n.t('name', '语音下发'),
        4: i18n.t('name', '开始对讲'),
        5: i18n.t('name', '结束对讲'),
        6: i18n.t('name', '接受任务'),
        7: i18n.t('name', '完成处理'),
    };

    const handlePlayVolume = async (e: any, record: any) => {
        if (!record.content) {
            message.error(i18n.t('name', '暂无文件'));
            return;
        }

        const { clientY } = e;
        let title = '';
        const operateInfo = [];
        if (record.handleType === AUDIO_SEND) {
            const { voiceFileId, voiceFileName, mp3_32000 } = JSON.parse(record.content || '{}');
            // 未转换成功提示
            if (!mp3_32000 && !judgeIsDisplay(voiceFileName)) {
                message.warn(i18n.t('message', '浏览器不支持该音频格式播放'));
                return;
            }
            operateInfo.push({
                fileId: mp3_32000 || voiceFileId,
                ...record,
            });
        } else {
            const data = await getOperateInfo({ optIds: record.content });
            operateInfo.push(...(data || []));
        }

        title =
            record.handleType === AUDIO_SEND
                ? i18n.t('name', '下发语音')
                : i18n.t('name', '对讲录音');
        if (operateInfo?.[0]?.fileUuid || operateInfo?.[0]?.fileId) {
            const result =
                record.handleType === AUDIO_SEND
                    ? await fetchFileDownloadUrl({ fileIdList: operateInfo?.[0]?.fileId })
                    : await fetchFileUrls({ fileUuids: operateInfo[0].fileUuid });
            if (record.handleType == AUDIO_SEND && result[0]?.fileUrl) {
                setVolumeInfo({
                    title,
                    url: result[0].fileUrl,
                    x: 0,
                    y: clientY,
                });
            } else if (result[0].url) {
                setVolumeInfo({
                    title,
                    url: result[0].url,
                    x: 0,
                    y: clientY,
                });
            }
        } else if (operateInfo?.[0]?.fileId == 0) {
            message.error(i18n.t('name', '暂无文件'));
        } else {
            message.error(i18n.t('message', '请求失败'));
        }
    };

    const columns: ColumnType<Record>[] = [
        {
            title: i18n.t('name', '处理时间'),
            dataIndex: 'handleTime',
            ellipsis: true,
            render: (time) => {
                return utils.formator.zeroTimeStampToFormatTime(time, undefined, 'HH:mm:ss');
            },
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'seizeUserAccount',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '处理操作'),
            dataIndex: 'handleType',
            ellipsis: true,
            render: (handleType) => {
                return handleType_map[handleType];
            },
        },
        {
            title: i18n.t('name', '处理内容'),
            dataIndex: 'content',
            ellipsis: true,
            render: (text, record) => {
                let com: React.ReactNode = '-';

                switch (record.handleType) {
                    case LOOK_TASk:
                        com = '-';
                        break;
                    case TETX_SEND:
                        com = (
                            <Popover
                                overlayClassName="popover-cls-wraper-box"
                                content={text}
                                title={i18n.t('message', '文本下发')}
                            >
                                <a>{i18n.t('message', '文本下发')}</a>
                            </Popover>
                        );
                        break;
                    case AUDIO_SEND:
                        com = (
                            <Tooltip title={i18n.t('action', '语音下发')}>
                                <a onClick={(e) => handlePlayVolume(e, record)}>
                                    {i18n.t('action', '语音下发')}
                                </a>
                            </Tooltip>
                        );
                        break;
                    case START_TALK:
                        com = '-';
                        break;
                    case END_TALK:
                        com = (
                            <Tooltip title={i18n.t('action', '对讲录音')}>
                                <a onClick={(e) => handlePlayVolume(e, record)}>
                                    {i18n.t('action', '对讲录音')}
                                </a>
                            </Tooltip>
                        );
                        break;
                    case ACCEPT_TASK:
                        com = '-';
                        break;
                    case HANDLED:
                        com = <OverflowEllipsisContainer>{text || '-'}</OverflowEllipsisContainer>;
                        break;
                    default:
                        break;
                }
                return com;
            },
        },
    ];

    const TableContent = (
        <div style={{ width: '652px' }}>
            <Table
                scroll={{
                    y: '250px',
                }}
                loading={loading}
                dataSource={list}
                columns={columns}
            />
            {volumeInfo && (
                <VolumePlayer
                    url={volumeInfo.url}
                    title={volumeInfo.title}
                    y={volumeInfo.y - 248}
                    axis="y"
                    onClose={() => {
                        setVolumeInfo(null);
                    }}
                    top={-300}
                    showWrapper={true}
                    wrapperClassName="dragable-outer-wrapper"
                    wrapperStyle={{ top: '408px' }}
                />
            )}
        </div>
    );

    const fetchRecords = () => {
        if (!loading) {
            setLoading(true);
            riskId
                ? getAllHandleRecords(riskId)
                      .then((rs) => {
                          rs.sort((a: any, b: any) => b.handleTime - a.handleTime);
                          setList(rs);
                      })
                      .finally(() => setLoading(false))
                : setLoading(false);
        }
    };

    return (
        <div className="risk-task-records-detail">
            {React.cloneElement(children as React.ReactElement, {
                onClick: () => {
                    setPopOpen(!popOpen);
                    fetchRecords();
                },
            })}
            <Modal
                visible={popOpen}
                title={titleText}
                mask={false}
                width={700}
                onCancel={() => {
                    setPopOpen(false);
                    setVolumeInfo(null);
                }}
                /* onVisibleChange={(open) => {
                    if (!open) {
                        setVolumeInfo(null);
                    }
                    setPopOpen(open);
                    open && fetchRecords();
                }} */
                footer={null}
                style={{ position: 'absolute', top: 125, right: 40 }}
                maskClosable={true}
            >
                {TableContent}
            </Modal>
        </div>
    );
};

export default RecordsDetail;
