import { InputProps } from '@streamax/poppy/lib/input';
import { ReactNode } from 'react';

export type SearchModuleNameType = 'fleet' | 'vehicle' | string;

export interface SearchStrategy {
    /** 策略标题，如 "车队" 或 "车辆" */
    title: ReactNode | string;
    /** 列表数据的唯一ID字段，用于渲染列表或点击时区分 */
    key: string;
    /**
     * 获取搜索列表
     * @param value 搜索关键字
     * @param limit 搜索结果条数限制
     */
    fetchListData: (
        value: string,
        option?: {
            limit: number; // 搜索结果条数限制
            [key: string]: any; // 其他参数
        },
    ) => Promise<any[]>;
    /**
     * 用于展示名称
     */
    renderItem: (item: any) => ReactNode;
    /** 策略标题，如 "车队" 或 "车辆" */
    nameField: string;

    [key: string]: any;
}

export interface MultiSearchProps
    extends Omit<InputProps, 'onSelect' | 'onChange'> {
    /**	是否是简单树	*/
    simpleTree?: boolean;
    /**	是否展示标题	*/
    showTitle?: boolean;
    /**	是否可以开始搜索；默认开启；内部展示loading状态	*/
    enable?: boolean;
    /**
     * 搜索下拉数量控制：默认展示50条
     */
    searchCountLimit?: number;
    /**
     * 搜索模块名称控制是否展示，默认为["fleet", "vehicle]
     */
    searchModule?: SearchModuleNameType[];
    /**
     * 选中事件
     * @param module 搜索模块名称
     * @param data 选中的数据
     * @param e 事件
     * @returns
     */
    onSelect: (
        e: React.MouseEvent,
        module: SearchModuleNameType,
        data: any,
    ) => void;
    /**
     * 第一次是否加载；默认开启
     */
    mountedLoad?: boolean;
    /**
     * 搜索下拉框的位置；默认为right
     */
    placement?: 'left' | 'right';
    /**
     * 关闭事件
     * @returns 关闭事件
     */
    onClear?: () => void;
    /**
     * 搜索框onChange事件
     * @param value
     * @returns
     */
    onChange?: (value?: { module: SearchModuleNameType; data: any }) => void;
    /**
     * 是否缓存组件，默认关闭
     */
    cacheComponentName?: string;
    /**
     * 每次打开是否强制刷新，默认关闭
     */
    forceRefresh?: boolean;
    /**
     * 应用id
     */
    appId?: number;
    /**
     * 搜索策略，默认使用内部策略
     */
    externalStrategies?: Record<SearchModuleNameType, SearchStrategy>; // 外部传入的搜索策略
}

export interface DynamicTitleProps {
    children: React.ReactDOM;
    asyncTitleFun?: () => any;
    delayTime?: number; // 延迟时间，鼠标停留一定时间后才会发送请求
}

export type FleetItem = {
    path: string;
};
