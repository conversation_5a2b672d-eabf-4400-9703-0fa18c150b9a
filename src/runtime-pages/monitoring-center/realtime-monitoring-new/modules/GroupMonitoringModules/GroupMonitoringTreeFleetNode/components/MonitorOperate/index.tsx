/*
 * @LastEditTime: 2024-11-19 10:53:56
 */
import { i18n } from '@base-app/runtime-lib';
import { Space, Tooltip } from '@streamax/poppy';
import {
    IcListEditFill,
    IconDeleteFill,
    IconPlay02Fill,
} from '@streamax/poppy-icons';
import React from 'react';

export interface CustomMonitorOperateProps {
    onMonitor?: (e: React.MouseEvent) => void;
    onEditMonitor?: (e: React.MouseEvent) => void;
    omDeleteMonitor?: (e: React.MouseEvent) => void;
}

export default React.memo((props: CustomMonitorOperateProps) => {
    const { onMonitor, onEditMonitor, omDeleteMonitor } = props || {};

    return (
        <Space size={8} onClick={(e) => e.stopPropagation()}>
            <Tooltip key="monitor" title={i18n.t('name', '监控')}>
                <span onClick={onMonitor}>
                    <a>
                        <IconPlay02Fill style={{ fontSize: 16 }} />
                    </a>
                </span>
            </Tooltip>
            <Tooltip key="editMonitor" title={i18n.t('name', '编辑监控组')}>
                <span onClick={onEditMonitor}>
                    <a>
                        <IcListEditFill style={{ fontSize: 16 }} />
                    </a>
                </span>
            </Tooltip>
            <Tooltip key="deleteMonitor" title={i18n.t('name', '删除监控组')}>
                <span onClick={omDeleteMonitor}>
                    <a>
                        <IconDeleteFill style={{ fontSize: 16 }} />
                    </a>
                </span>
            </Tooltip>
        </Space>
    );
});
