import type { RTDataGeneratorPlugin } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/RTDataGenerator';
import { RTDataGeneratorV1 } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/RTDataGenerator';
import { create } from 'zustand';
import type { DataStoreType } from './types';
import { initialValue } from './types';
import RTDeviceStatusGeneratorPlugin from './plugins/RTDeviceStatusGeneratorPlugin';
import RTDriverGeneratorPlugin from './plugins/RTDriverGeneratorPlugin';
import RTGPSGeneratorPlugin from './plugins/RTGPSGeneratorPlugin';
import RTReuseGeneratorPlugin from './plugins/RTReuseGeneratorPlugin';
import RTInnerPlugin from './plugins/RTInnerPlugin';
import RTDisableVehicleGeneratorPlugin from './plugins/RTDisableVehicleGeneratorPlugin';
import rmPageConfig from '@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config';
import BaseReuse from '@/class/BaseReuse';

/**
 * 实时数据管理类
 * 负责整合各种实时数据生成器并管理数据状态
 */
export default class RTData extends BaseReuse<{}> {
    useDataStore = create<DataStoreType>(() => ({
        ...initialValue,
    }));
    private reuseData = {};
    private plugins = new Map<string, RTDataGeneratorPlugin>();
    private RTDataGeneratorV1: RTDataGeneratorV1;
    getReuse() {
        return this.reuseData;
    }
    setReuse(): void {}
    constructor() {
        super();
        this.RTDataGeneratorV1 = new RTDataGeneratorV1();

        // 注册默认插件并初始化到生成器
        const defaultPlugins = [
            ['rtDriverGenerator', new RTDriverGeneratorPlugin()],
            ['rtDeviceStatusGenerator', new RTDeviceStatusGeneratorPlugin()],
            ['rtGPSGenerator', new RTGPSGeneratorPlugin()],
            ['rtReuseGenerator', new RTReuseGeneratorPlugin()],
            ['rtInner', new RTInnerPlugin(this.useDataStore)],
            ['rtDisableVehicle', new RTDisableVehicleGeneratorPlugin()],
        ] as const;

        for (const [key, plugin] of defaultPlugins) {
            this.registerPlugin(key, plugin);
        }
    }
    /**
     * 注册新的插件
     * @param key 插件唯一标识
     * @param plugin 插件实例
     * @throws Error 如果插件已存在
     */
    public registerPlugin(key: string, plugin: RTDataGeneratorPlugin): void {
        if (this.plugins.has(key)) {
            throw new Error(`Plugin with key ${key} already exists`);
        }
        this.plugins.set(key, plugin);
        this.RTDataGeneratorV1.addPlugin(key, plugin);
    }

    /**
     * 注销插件
     * @param key 插件唯一标识
     * @returns 是否成功注销
     */
    public unregisterPlugin(key: string): boolean {
        const plugin = this.plugins.get(key);
        if (!plugin) {
            return false;
        }

        // 停止插件的数据源（如果存在）
        if (
            'dataSource' in plugin &&
            typeof (plugin as any).dataSource?.stop === 'function'
        ) {
            (plugin as any).dataSource.stop();
        }

        this.plugins.delete(key);
        return this.RTDataGeneratorV1.removePlugin(key);
    }

    /**
     * 重置数据存储
     * 在组件卸载时调用
     */
    private resetDataStore() {
        this.useDataStore.setState(initialValue);
    }

    /**
     * 获取已注册的插件列表
     */
    public getRegisteredPlugins(): string[] {
        return Array.from(this.plugins.keys());
    }
    private getPollTime(count: number) {
        const frequencyTime = Number(
            (window as any).APP_CONFIG[
                'realtime.monitoring.update.frequency'
            ] || 1000,
        );
        // 按照 1w 0.5s 标准延迟，最低0.5一次
        return Math.max((count / 10000) * frequencyTime, 500);
    }
    /**
     * 启动数据生成器
     */
    // 是否调用了 start，在start时为true，在stop为false。为了避免发布订阅时序问题，增加判断准确信
    private started  = false;
    // 取消静态数据订阅控制
    private unsubscribeStaticDataFn = () => {};
    private unsubscribeStaticData = () => {
        this.unsubscribeStaticDataFn();
        this.unsubscribeStaticDataFn = () => {};
    }
    /**
     * 重要！！！由外部页面调用，外部页面必须保证页面进入调用start，离开调用stop
     */
    async start() {
        this.started = true;
        // 订阅静态数据车辆列表加载情况
        this.unsubscribeStaticDataFn =
            rmPageConfig.data.StaticDataInstance.useDataStore.subscribe(
                (state, prevState) => {
                    if (
                        this.started &&
                        state.vehicleLoaded &&
                        state.vehicleLoaded !== prevState.vehicleLoaded
                    ) {
                        // 设置数据生成器的轮询时间并启动
                        this.RTDataGeneratorV1.setIntervalTime(
                            this.getPollTime(state.vehicleList.length),
                        );
                        this.RTDataGeneratorV1.start();
                        // 取消订阅
                        this.unsubscribeStaticData();
                    }
                },
            );
    }

    /**
     * 停止数据生成器，离开页面时调用
     */
    async stop() {
        this.started = false;
        // 去掉订阅基础数据并重置
        this.unsubscribeStaticData();
        await this.RTDataGeneratorV1.stop();
        this.resetDataStore();
    }
    /**
     * 更新可视范围内车辆的订阅（此部分数据会及时推送，不收限频影响）
     */
    async updateVehicleSubscribe(subscribeVids: string[]) {
        return await Promise.all([
            (
                this.plugins.get('rtDriverGenerator') as RTDriverGeneratorPlugin
            )?.dataSource?.updateVehicleSubscribe(subscribeVids),
            (
                this.plugins.get(
                    'rtDeviceStatusGenerator',
                ) as RTDeviceStatusGeneratorPlugin
            )?.dataSource?.updateVehicleSubscribe(subscribeVids),
        ]);
    }
    /**
     * 重新加载全量设备状态
     */
    async reLoadDeviceState() {
        await (
            this.plugins.get(
                'rtDeviceStatusGenerator',
            ) as RTDeviceStatusGeneratorPlugin
        )?.dataSource?.reLoadDeviceState();
    }
}
