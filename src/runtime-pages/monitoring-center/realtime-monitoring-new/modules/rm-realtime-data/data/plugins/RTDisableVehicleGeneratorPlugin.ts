import { DisableVehicleDataGeneratorV1 } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/DisableVehicleDataGenerator';
import type { RTDataGeneratorV1, RTDataBox } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/RTDataGenerator';

/**
 * 停用车辆数据生成器插件
 * 负责将停用车辆实时数据集成到RTDataGeneratorV1中
 */
export default class RTDisableVehicleGeneratorPlugin {
    private readonly HOOK_NAME = 'disableVehicleStatus';
    dataSource: DisableVehicleDataGeneratorV1;

    constructor() {
        this.dataSource = new DisableVehicleDataGeneratorV1();
    }

    apply(generator: RTDataGeneratorV1) {
        // 查询数据钩子
        generator.hooks.onQuery.tapPromise(
            this.HOOK_NAME,
            async (generator: RTDataGeneratorV1, dataBox: RTDataBox) => {
                // 检查当前操作是否仍然是最新的
                if (dataBox.isLatest && !dataBox.isLatest()) {
                    return;
                }
                
                // 获取最新停用车辆数据
                const { changed, data } = await this.dataSource.getData();
                // 设置到dataBox
                dataBox.disableVehicleStatus = {
                    disableVehicleSet: data,
                    changed,
                };
            },
        );

        // 启动钩子
        generator.hooks.onStart.tap(this.HOOK_NAME, () => {
            if (this.dataSource?.start) {
                this.dataSource.start();
            }
        });

        // 停止钩子
        generator.hooks.onStop.tap(this.HOOK_NAME, () => {
            if (this.dataSource?.stop) {
                this.dataSource.stop();
            }
        });
    }

    unApply(generator: RTDataGeneratorV1) {
        // 使用RTDataGenerator的untapHook方法取消所有注册的钩子
        generator.untapHook(generator.hooks.onQuery, this.HOOK_NAME);
        generator.untapHook(generator.hooks.onStart, this.HOOK_NAME);
        generator.untapHook(generator.hooks.onStop, this.HOOK_NAME);
    }
}
