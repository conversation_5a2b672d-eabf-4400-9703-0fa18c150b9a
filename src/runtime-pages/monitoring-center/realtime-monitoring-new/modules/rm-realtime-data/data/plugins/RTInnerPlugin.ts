import type { RTDataGeneratorV1, RTDataBox } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/RTDataGenerator';
import type { StoreApi } from 'zustand';
import type { DataStoreType } from '@/runtime-pages/monitoring-center/realtime-monitoring-new/modules/rm-realtime-data/data/types';

/**
 * 内部插件
 * 负责将RTDataGeneratorV1的数据同步到zustand store
 */
export default class RTInnerPlugin {
    private readonly HOOK_NAME = 'inner';
    private readonly useDataStore: StoreApi<DataStoreType>;

    constructor(useDataStore: StoreApi<DataStoreType>) {
        this.useDataStore = useDataStore;
    }

    apply(generator: RTDataGeneratorV1) {
        // 处理结果
        generator.hooks.onResult.tap(
            this.HOOK_NAME,
            (generator: RTDataGeneratorV1, dataBox: RTDataBox) => {
                // 检查当前操作是否仍然是最新的
                if (dataBox.isLatest && !dataBox.isLatest()) {
                    return undefined;
                }
                
                // 将RTDataGeneratorV1的数据同步到store
                // 反复高频在监控页和其他页面使用浏览器前进后退切换，残留任务执行时可能 dataBox 中部分数据为空，所以需要判断+老数据兜底
                const oldState = this.useDataStore.getState();
                this.useDataStore.setState({
                    deviceStatus:
                        dataBox.deviceStatus as DataStoreType['deviceStatus'] || oldState.deviceStatus,
                    disableVehicle:
                        dataBox.disableVehicleStatus as DataStoreType['disableVehicle']  || oldState.disableVehicle,
                    driver: dataBox.driverStatus as DataStoreType['driver']  || oldState.driver,
                    gps: dataBox.gpsStatus as DataStoreType['gps']  || oldState.gps,
                    reuse: dataBox.reuseStatus as DataStoreType['reuse']  || oldState.reuse,
                });
                return undefined; // SyncLoopHook requires undefined return to stop the loop
            },
        );

        // 处理错误
        generator.hooks.onError.tap(this.HOOK_NAME, (error: Error) => {
            console.error('RTData error:', error);
            return undefined; // SyncLoopHook requires undefined return to stop the loop
        });
    }

    unApply(generator: RTDataGeneratorV1) {
        // 使用RTDataGenerator的untapHook方法取消所有注册的钩子
        generator.untapHook(generator.hooks.onResult, this.HOOK_NAME);
        generator.untapHook(generator.hooks.onError, this.HOOK_NAME);
    }
}
