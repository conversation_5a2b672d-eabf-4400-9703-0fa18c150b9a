// 强制显示为车辆图标的地图缩放等级
export const maxShowMarkerZoom = 18;
export const DEFAULT_CENTER_TIME = 30000; //地图拖动或者放大操作后，30秒内不会因为车辆轮巡而重新将车辆居中显示
export const MAX_DEFAULT_GRID_SIZE = 50;
// 车辆聚合规则启聚合车辆总数
export const aggregationCarTotal = 2000;
// 在线 stateId
export const ONLINE_STATE = '1461206870592231111';
// 离线 stateId
export const OFFLINE_STATE = '1461206870592232222';
// 休眠 stateId
export const DORMANCY_STATE = '1461206870592252222';
// 报警 stateId
export const ALARM_STATE = '1461206870592233333';
export const VEHICLE_STATES = {
    ONLINE_STATE,
    OFFLINE_STATE,
    DORMANCY_STATE,
};
// 车辆类型枚举
export const VEHICLE_TYPE = {
    DEFAULT: -1,
    NORMAL_TRUCK: 1,
    HEAVY_TRUCK: 2,
    LIGHT_TRUCK: 3,
    DANGER_CARRY: 4,
    CEMENT_MIXER: 5,
    COLD_CHAIN_CAR: 6,
    TRAILER: 7,
    MINIBUS: 8,
    OTHERS: 9,
    FORKLIFT: 10,
    WIDE: 11, // 宽体车
    BULLDOZER: 12,
    DIGGER: 13, // 挖掘机
    TROLLEY: 14,
    TRACKLESS: 15,
};
// 车辆类型对应的车辆图标枚举
export const VEHICLE_TYPE_ICON = {
    [VEHICLE_TYPE.NORMAL_TRUCK]: require('@/assets/images/icon_map_car_current.png'),
    [VEHICLE_TYPE.HEAVY_TRUCK]: require('@/assets/images/zhongxinghuoche.png'),
    [VEHICLE_TYPE.LIGHT_TRUCK]: require('@/assets/images/qingxinghuoche.png'),
    [VEHICLE_TYPE.DANGER_CARRY]: require('@/assets/images/weiyunche.png'),
    [VEHICLE_TYPE.CEMENT_MIXER]: require('@/assets/images/shuinijiaobanche.png'),
    [VEHICLE_TYPE.COLD_CHAIN_CAR]: require('@/assets/images/lenglianche.png'),
    [VEHICLE_TYPE.TRAILER]: require('@/assets/images/tuoche.png'),
    [VEHICLE_TYPE.MINIBUS]: require('@/assets/images/xiangshimianbaoche.png'),
    [VEHICLE_TYPE.OTHERS]: require('@/assets/images/icon_map_car_current.png'),
    [VEHICLE_TYPE.FORKLIFT]: require('@/assets/images/chache.png'),
    [VEHICLE_TYPE.WIDE]: require('@/assets/images/kuantiche.png'), // 宽体车
    [VEHICLE_TYPE.BULLDOZER]: require('@/assets/images/tuituche.png'),
    [VEHICLE_TYPE.DIGGER]: require('@/assets/images/wajueji.png'), // 挖掘机
    [VEHICLE_TYPE.TROLLEY]: require('@/assets/images/icon_map_car_current.png'),
    [VEHICLE_TYPE.TRACKLESS]: require('@/assets/images/wuguiche.png'),
    [VEHICLE_TYPE.DEFAULT]: require('@/assets/images/icon_map_car_current.png'),
};
