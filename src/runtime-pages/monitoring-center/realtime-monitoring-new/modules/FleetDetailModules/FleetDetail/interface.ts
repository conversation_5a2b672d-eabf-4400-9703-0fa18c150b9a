export interface FleetDetailProps {
    visible: boolean;
    title: string | React.ReactNode;
    onClose?: () => void;
    children: React.ReactNode
}
export type FleetDetailState = {
    fleetId: string;
    fleetInfo: {
        fId: string;
        parentId: string;
        fName: string;
        path: string;
        childrenIds: string[];
        vNumber: number;
        createTime: number;
        fleetName: string
    };
    fleetPathList: any[];
    staticDrivers: string[];
    visible: boolean;
};