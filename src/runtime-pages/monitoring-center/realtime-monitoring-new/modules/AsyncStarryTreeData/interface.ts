import type { StateActive } from "@/modules/device/useRTDeviceData";
import type { Key, MutableRefObject, ReactNode } from "react";


//  统一的父子节点映射类型 - 所有节点都使用children数组
export type FleetChildrenMap = Record<string, {
    children: string[];  // 统一的子节点数组
}>;

/**
 * 节点类型
 */
export enum NodeType {
    FLEET = 'fleet',
    VEHICLE = 'vehicle',
    OPERATION = 'operation',
    DEVICE="device"
}


/**
 * 构建静态树的基础数据结构
 */
export interface BuildStaticTreeBaseData {
    id: string;
    parentId: string;
    type: NodeType;
    [key: string]: any;
}

/**
 * 树节点基本数据结构
 */
export interface BaseNode {
    key?: Key;
    parentKey: string;
    level: number; // 树节点的层级，0开始
    pos: string | number ; // 树节点在同级别下的下标信息
    nameIndex?: number; // 树节点在同级别下同名称的下标信息;用于排序
    [key: string]: any;
}

/**
 * 树节点数据结构
 */
export interface TreeData extends BaseNode {
    title?: ReactNode | string;
    isLeaf: boolean;
    showIcon?: boolean;
    switcherIcon?: boolean;
    [key: string]: any;
}

//  构建阶段的节点类型构建配置
export interface BuildConfig {
    //构建配置自定义id和parentId和name制定
    customFields?: {
        id?: string;
        parentId?: string;
        name?: string;
    };
}

export interface NodeDisplayConfig {
    // 车辆显示数量,默认200个
    number?: number;
    // 车辆显示数量是否显示，默认显示
    display: boolean;
}

/**
 * 树加载数据的配置
 */
export type Config = {
    nodeDisplayConfig?: NodeDisplayConfig;
    buildConfig?: BuildConfig;
    filterNodeCallbacks?: (key: string) => boolean; // 过滤车辆回调
    convertTreeData2Node: (data: BaseNode) => Promise<TreeData>; // 将treeData转换为TreeNode
    updateTreeDataNodeTitle: (treeData: TreeData, key: string) => TreeData; // 更新treeNode的title
};

export type AsyncStarryTreeUseDataState = {
    // 树组件ref
    treeRef: MutableRefObject<any> | null;
    // 初始化配置
    config: Config;
    // 展开的节点key
    expandedKeys: Key[];
    // 选中的节点key
    selectedKeys: Key[];
    //  已加载的key
    loadedKeys: Key[];
    // 树节点构建的基本数据
    treeNodeKeyMap: Record<string, BaseNode>;
    // 树节点数据
    treeNodes: TreeData[];
    // 父亲节点map结构
    fleetChildrenMap: FleetChildrenMap;
    // 节点显示数组
    nodeDisplayMap: Record<string, [number, number]>;
    // 节点父级key集合缓存 - 性能优化
    nodeParentKeysMap: Record<string, Set<string>>;
};


/**
 * 用于检查两个类型是否有重叠的键
 */
export type HasOverlap<T, U> = keyof T & keyof U;

/**
 * 确保扩展状态不包含基础状态的键
 */
export type EnsureNoOverlap<T, Base> = HasOverlap<T, Base> extends never 
  ? T 
  : never;

/**
 * 扩展状态类型，确保不与基础状态冲突
 */
export type ExtendedState<T> = EnsureNoOverlap<T, AsyncStarryTreeUseDataState>;

/**
 * 合并状态类型
 */
export type MergedState<T> = AsyncStarryTreeUseDataState & ExtendedState<T>;

/**
 * SetState 参数类型
 */
export type SetStateParam<T> = 
  | Partial<AsyncStarryTreeUseDataState>
  | Partial<ExtendedState<T>>
  | ((state: AsyncStarryTreeUseDataState & ExtendedState<T>) => 
		Partial<AsyncStarryTreeUseDataState> | Partial<ExtendedState<T>>);

/**
 * 列表数据转换map配置option
 */
export type TransformOption = {
    toMap?: boolean;
    mapKey?: string;
};


export interface BuildStaticTreeOptions<T> {
    customNodeBuild?: (
        type: NodeType,
        item: T & BaseNode,
    ) => T & BaseNode;
    afterBuild?: (
        result: FleetChildrenMap,
    ) => Promise<FleetChildrenMap>
}

/**
 * 车辆状态变化
 */
export type VehicleStateChangedItem = {
    vId: string;
    stateActive: StateActive;
};