import type { BaseNode} from "../../interface";
import { type TreeData } from "../../interface";
import FleetTaskController from './FleetTaskController';
import { defaultConvertTreeData2Node, getOperationTreeKey } from "../../utils";
import { TOP_FLEET_PARENT_ID, VEHICLE_DISPLAY_CONFIG_NUMBER } from "../../constants";
import type AsyncStarryTreeData from "../AsyncStarryTreeData";

export class LoadFleetsManager<T> {
    private readonly PREFIX_INDEX = -1;
    private readonly SUFFIX_INDEX = 1e8;
    private taskController = new FleetTaskController();
    private manager: AsyncStarryTreeData | null;
    private treeNodeCache: Record<string, TreeData> = {};
    filteredNodeListMap: Record<string, string[]> = {};
    constructor(manager: AsyncStarryTreeData) {
        this.manager = manager;
    }
    /**
     *  加载节点（支持车组和车辆节点）
     * @param keys 节点key数组，支持车组key和车辆key
     * @param isRefresh 是否刷新
     */
    async loadFleets(keys: string[] = [], isRefresh = false) {
        if ((!keys || keys.length === 0) && !isRefresh) return;
        const promiseTask = this.taskController.addTask(keys, isRefresh);
        this.startLoadFleetsTask();
        // eslint-disable-next-line consistent-return
        return await promiseTask;
    }

    private async startLoadFleetsTask() {
        const task = this.taskController.startNextTask();
        if (!task) return;
        const {keys, isRefresh } = task;
        try {
            await this.innerLoadFleets(keys, isRefresh);
        } catch (error) {
            this.loadFleetsTaskEnd(false);
            throw error;
        }
        this.loadFleetsTaskEnd(true);
    }

    private loadFleetsTaskEnd(success: boolean) {
        this.taskController.endTask(success);
        if (this.taskController.hasWaitingTasks()) {
            this.startLoadFleetsTask();
        }
    }

    /**
     *  内部加载节点方法（支持车组和车辆节点）- 适配新的统一children结构
     * @param keys 节点key数组，可以是车组key或车辆key
     * @param isRefresh 是否刷新
     */
    private async innerLoadFleets(keys: string[], isRefresh = false) {
        // 非刷新数据情况，判断是否已加载过，已加载过的直接返回
        const loadedKeys = this.manager?.getState()?.loadedKeys;
        const fleetChildrenMap = this.manager?.getState()?.fleetChildrenMap;

        //  优化：使用Set直接去重，避免中间数组和多次转换
        const nodeParentKeysMap = this.manager?.getState()?.nodeParentKeysMap || {};
        const newLoadedKeys = new Set<string>();

        //  先添加已加载的keys
        if (loadedKeys) {
            loadedKeys.forEach(key => newLoadedKeys.add(String(key)));
        }

        //  添加当前要加载的keys及其所有父级keys
        const allKeysToAdd = keys.flatMap(key => {
            const parentKeys = nodeParentKeysMap[key];
            return parentKeys ? [key, ...Array.from(parentKeys)] : [key];
        });

        // 一次性添加所有keys到Set，自动去重
        allKeysToAdd.forEach(key => newLoadedKeys.add(key));

        // 构建加载的节点数据
        const treeNodes: TreeData[] = [];

        //  新的处理逻辑：适配统一的children结构，不区分节点类型
        const handleNodes = async (parentTreeKey: string, parentPos?: string) => {
            const childrenData = fleetChildrenMap?.[parentTreeKey];

            if (!childrenData || !childrenData.children) {
                return;
            }

            //  处理统一的children数组 - 所有节点类型都使用相同逻辑
            const children = childrenData.children;

            //  统一处理所有类型的父节点（fleet、vehicle等）
            await this.parseUnifiedChildren(
                parentTreeKey,
                children,
                parentPos,
                treeNodes,
                newLoadedKeys,
                handleNodes
            );
        };

        // 从顶层开始处理
        await handleNodes(TOP_FLEET_PARENT_ID);

        if (!isRefresh) {
            this.manager?.updateLoadedKeys(Array.from(newLoadedKeys));
        }
        this.manager?.updateTreeNode(treeNodes);
    }

    /**
     *  新方法：统一处理children数组，实现可视区域展示（不区分节点类型）
     * @param parentKey 父节点key
     * @param children 统一的子节点key数组
     * @param parentPos 父节点位置
     * @param treeNodes 树节点数组
     * @param newLoadedKeys 需要加载的key集合
     * @param handleNodes 递归处理函数
     */
    private async parseUnifiedChildren(
        parentKey: string,
        children: string[],
        parentPos: string | undefined,
        treeNodes: TreeData[],
        newLoadedKeys: Set<string>,
        handleNodes: (parentKey: string, parentPos?: string) => Promise<void>
    ) {
        //  统一处理所有子节点，不区分类型
        let allChildren = children.slice();

        //  1. 过滤子节点- 统一过滤所有类型的节点
        if (typeof this.manager?.getState()?.config?.filterNodeCallbacks === 'function') {
            allChildren = allChildren.filter((childKey) => {
                const result = this.manager?.getState()?.config.filterNodeCallbacks?.(childKey);
                return result;
            });
        }

        const filteredLength = allChildren.length;
        //  缓存过滤后的子节点列表，方便后续搜索定位
        this.filteredNodeListMap[parentKey] = allChildren.slice();

        //  2.计算当前节点可视区域展示区间
        const nodeDisplayMap = this.manager?.getState()?.nodeDisplayMap;
        let showNodeIndexArray = nodeDisplayMap?.[parentKey] || [
            0,
            this.manager?.getState()?.config?.nodeDisplayConfig?.number ||
                VEHICLE_DISPLAY_CONFIG_NUMBER,
        ];

        //  如果当前子节点数 <= 展示区间长度，则缩小展示区间
        if (filteredLength <= showNodeIndexArray[1] - showNodeIndexArray[0]) {
            showNodeIndexArray = [0, filteredLength];
        }

        //  截取可视区域的子节点
        const visibleChildren = allChildren.slice(
            showNodeIndexArray[0],
            showNodeIndexArray[1]
        );

        //  添加前缀操作节点（如果需要）
        if (showNodeIndexArray[0] > 0) {
            const firstChildKey = visibleChildren[0];
            const treeNodeKeyMap = this.manager?.getState()?.treeNodeKeyMap || {};
            const firstChildNode = treeNodeKeyMap[firstChildKey] || ({} as BaseNode);

            const prefixKey = getOperationTreeKey(parentKey, 'prefix');
            const prefixNode = this.createOperationNode(prefixKey, firstChildNode, this.PREFIX_INDEX);
            this.updateTreeNodeKeyMap(prefixKey, prefixNode);
            visibleChildren.unshift(prefixKey);
        }

        //  添加后缀操作节点（如果需要）
        if (filteredLength > showNodeIndexArray[1]) {
            const lastChildKey = visibleChildren[visibleChildren.length - 1];
            const treeNodeKeyMap = this.manager?.getState()?.treeNodeKeyMap || {};
            const lastChildNode = treeNodeKeyMap[lastChildKey] || ({} as BaseNode);

            const suffixKey = getOperationTreeKey(parentKey, 'suffix');
            const suffixNode = this.createOperationNode(suffixKey, lastChildNode, this.SUFFIX_INDEX);
            this.updateTreeNodeKeyMap(suffixKey, suffixNode);
            visibleChildren.push(suffixKey);
        }

        //  统一生成TreeNode并处理递归
        for (let index = 0; index < visibleChildren.length; index++) {
            const childKey = visibleChildren[index];
            const childPos = parentPos ? `${parentPos}-${index}` : `${index}`;

            // 生成TreeNode
            const treeNode = await this.getNode(childKey, childPos);
            treeNodes.push(treeNode);

            //  如果此节点需要继续加载子节点，则递归处理
            if (newLoadedKeys.has(childKey)) {
                await handleNodes(childKey, childPos);
            }
        }
    }

    private async getNode(key: string, pos: string) {
        if (this.treeNodeCache[key]) {
            this.treeNodeCache[key].pos = pos;
            this.manager
                ?.getState()
                ?.config?.updateTreeDataNodeTitle?.(
                    this.treeNodeCache[key],
                    key,
                );
            return this.treeNodeCache[key];
        }
        const baseNode =
            this.manager?.getState()?.treeNodeKeyMap[key] || ({} as BaseNode);
         const mergeBaseNode = Object.assign(baseNode, { pos });
        const treeNode = (await this.manager
            ?.getState()
            ?.config?.convertTreeData2Node(mergeBaseNode)) as TreeData;
        if (
            this.manager?.getState()?.config.convertTreeData2Node !==
            defaultConvertTreeData2Node
        ) {
            this.treeNodeCache[key] = treeNode;
        }
        return treeNode;
    }


    /**
     * 拆分函数：创建「操作节点」（前缀/后缀）
     * @param id 节点 id
     * @param referNode 参照节点，用来读取 parentKey、parentId、path 等
     * @param nameIndex nameIndex 位置，用于排序或区分前后缀
     * @param name 具体操作文案，如 'prefix' 或 'suffix'
     * @returns 返回 TreeData 类型节点
     */
    private createOperationNode(
        key: string,
        baseNode: BaseNode,
        nameIndex: number,
    ): TreeData {
        return {
            ...baseNode,
            pos: '',
            key,
            isLeaf: true,
            nameIndex,
            disabled: true,
        };
    }

    /**
     * 将 key 和 node 的对应关系记录到 treeNodeKeyMap 中
     * @param key TreeData 的 key
     * @param node TreeData 对象
     */
    private updateTreeNodeKeyMap(key: string, node: BaseNode): void {
        const treeNodeKeyMap = this.manager?.getState()?.treeNodeKeyMap || {};
        treeNodeKeyMap[key] = node;
        this.manager?.setState({ treeNodeKeyMap });
    }

    // 更新是否显示控制；更新当前节点父亲展开区间
    public updateDisPlayArr = async (key: string): Promise<boolean> => {
         const parentKey = this.manager?.getState()?.treeNodeKeyMap[key]?.parentKey || TOP_FLEET_PARENT_ID;
        const nodes = this.filteredNodeListMap[parentKey] || [];
        const nodeIndex = nodes.findIndex(
            (nodeKey) => nodeKey === key
        );
        if (nodeIndex === -1) {
            return false;
        }

        //  获取当前显示区间
        let displayArr = this.manager?.getState()?.nodeDisplayMap[parentKey] || [
            0,
            this.manager?.getState()?.config?.nodeDisplayConfig?.number ||
                VEHICLE_DISPLAY_CONFIG_NUMBER,
        ];

        const step = displayArr[1] - displayArr[0];
        const half = Math.floor(step / 2);

        //  如果目标节点不在当前显示区间内，调整显示区间
        if (nodeIndex < displayArr[0] || nodeIndex >= displayArr[1]) {
            if (nodeIndex + half > nodes.length) {
                // 目标节点靠近末尾
                displayArr = [
                    Math.max(0, nodes.length - step),
                    nodes.length,
                ];
            } else if (nodeIndex - half < 0) {
                // 目标节点靠近开头
                displayArr = [0, step];
            } else {
                // 目标节点在中间，以其为中心调整显示区间
                displayArr = [nodeIndex - half, nodeIndex - half + step];
            }

            //  更新显示区间
            await this.manager?.updateNodeDisplayMap(
                parentKey,
                displayArr as [number, number],
            );
        }
        return true;
    };

    reset() {
        this.treeNodeCache = {};
        this.filteredNodeListMap = {};
    }
}
