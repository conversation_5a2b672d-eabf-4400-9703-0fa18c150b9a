@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.multi-video-device-list-wrap {
    display: flex;
    height: 60px;
    background: @grey-900;
    padding-left: 12px;
    .multi-video-device-list {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        border: none!important;
        /* 隐藏垂直滚动条 */
        .device-item {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            gap: 8px;
            width: 130px;
            height: 32px;
            margin-right: 12px;
            padding: 0 8px;
            color: rgba(255, 255, 255, 0.85);
            font-size: 12px;
            font-weight: 600;
            line-height: 32px;
            text-align: left;
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid hsla(0,0%,100%,.06);
            border-radius: 2px;
            cursor: pointer;
        }
        .device-item::abroad {
            border-radius: @border-radius-6;
        }
        .take-up-place {
            flex-shrink: 0;
            height: 20px;
        }
        
        .device-item-active {
            border: 1px solid @primary-color-light-border;
            background: @primary-color-lighter;
        }
        .device-item-check {
            border-color: @primary-color;
            background: @primary-color;
        }
    }
    .scroll-operate {
        display: flex;
        align-items: center;
        // width: 130px;
        padding: 0 8px;
        gap: 8px;
        margin-right: 16px;
        &-page-button {
            width: 32px;
            height: 32px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 32px;
            text-align: center;
            cursor: pointer;
        }
        &-page-button::abroad {
            font-size: 20px;
        }
        &-button {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.85);
            margin: 0 8px;
            cursor: pointer;
        }
        &-button::abroad {
              font-size: 20px;
        }
        &-button-add {
            margin-right: 0;
        }
       
        &-page-button-disabled {
            color: rgba(255, 255, 255, 0.45);
            cursor: not-allowed;
        }
    }
    .multi-video-device-list::-webkit-scrollbar {
        width: 0;
    }

    /* 隐藏水平滚动条 */
    .multi-video-device-list::-webkit-scrollbar {
        height: 0;
    }
    .primary-color-dom {
        color: @primary-color;
    }
    .empty-dom{
        color: @primary-color;
    }
}
