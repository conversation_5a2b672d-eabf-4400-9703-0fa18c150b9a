import BaseReuse from "@/class/BaseReuse";
import type { Store<PERSON><PERSON>, UseBoundStore } from "zustand";
import { create } from "zustand";
import { withSafeStore } from "@/modules/common/withSafeStore";
import { MultiChannelRealTimeVideoRef, PlayModeType, VehiclePlayItem } from "../components/MultiChannelRealTimeVideoV2";

interface GroupMonitoringPlayerState {
    groupMonitoringPlayerRef?: React.RefObject<MultiChannelRealTimeVideoRef> | null;
}

export default class GroupMonitoringPlayerData extends BaseReuse<Record<string, any>> {
    private readonly initialState: GroupMonitoringPlayerState = {
        groupMonitoringPlayerRef: null,
    };
    useDataStore: UseBoundStore<StoreApi<GroupMonitoringPlayerState>>;

    constructor() {
        super();
        this.useDataStore = create<GroupMonitoringPlayerState>()(
            withSafeStore(() => ({
                ...this.initialState,
            })),
        );
    }

    setReuse(reuse: Partial<Record<string, any>>): void {
        // Implementation
    }

    getReuse(): Record<string, any> {
        return {};
    }

    /**
     * Update the ref of the MultiChannelRealTimeVideo component.
     *
     * @param groupMonitoringPlayerRef Ref of the MultiChannelRealTimeVideo component.
     */
    updateGroupMonitoringPlayerRef(
        groupMonitoringPlayerRef: React.RefObject<MultiChannelRealTimeVideoRef>,
    ) {
        this.useDataStore.setState({ groupMonitoringPlayerRef });
    }

    /**
     * Play the video with given vehicle list and player mode.
     *
     * @param vehicleList The list of vehicles to be played.
     * @param playerPlayMode The player mode.
     */
    play(vehicleList: VehiclePlayItem[], playerPlayMode: PlayModeType) {
        this.useDataStore
            .getState()
            .groupMonitoringPlayerRef?.current?.playVideo(
                vehicleList,
                playerPlayMode,
            );
    }


    getOperateState() {
        return this.useDataStore.getState().groupMonitoringPlayerRef?.current?.getOperateState();
    }

    getPlayInfo() {
        return this.useDataStore
            .getState()
            .groupMonitoringPlayerRef?.current?.getPlayerInfo();
    }
}