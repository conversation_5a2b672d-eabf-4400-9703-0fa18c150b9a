import { asyncStarryTree } from '@streamax/material';
import FleetName from '../components/FleetName';
import FleetStatusCount from '../components/FleetStatusCount';

const KeyMonitoringTreeFleetNode = ({ treeKey }: { treeKey: string }) => {
    const { AsyncStarryTreeFleetNodeUIV1 } = asyncStarryTree;   
    const { AsyncStarryTreeFleetNodeUI } = AsyncStarryTreeFleetNodeUIV1;
    return (
        <AsyncStarryTreeFleetNodeUI
            treeKey={treeKey}
            rightContent={<FleetStatusCount treeKey={treeKey} />}
        >
            <FleetName treeKey={treeKey} />
        </AsyncStarryTreeFleetNodeUI>
    );
};

export default KeyMonitoringTreeFleetNode;
