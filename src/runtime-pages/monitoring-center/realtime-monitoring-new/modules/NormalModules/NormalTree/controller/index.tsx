import { Empty, Spin, StarryTree } from "@streamax/poppy";
import { useRef } from "react";
import type { TreeProps } from "@streamax/poppy/lib/starry-tree/antd-tree";
import { useMemoizedFn } from "ahooks";
import { LoadFailErrorTips } from "../components/LoadFailErrorTips";
import useNormalTreeInit from './hooks/useNormalTreeInit';
import rmPageConfig from "@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config";
import { FLEET_PREFIX, parseTreeKey, VEHICLE_PREFIX } from "../../../AsyncStarryTreeData";
import { useDebounceFn } from "@streamax/hooks";
import { useTreeContainerHeight } from "@/hooks/tree/useTreeContainerHeight";
import useNormalTreeState from "./hooks/useNormalTreeState";
import useNormalTreeCache from "./hooks/useNormalTreeCache";
import { performanceMeasureUtil } from '@base-app/runtime-lib';
import './index.scoped.less';
import { mapAndGroupMonitoringPlayOperate } from "../../utils";
import { TreeKeyHelper } from "../../../AsyncStarryTreeData/data/class/TreeKeyHelper";

export default () => {
    const { NormalTreeInstance } = rmPageConfig.data;

    //动态计算容器高度
    const treeContainerRef = useRef<HTMLDivElement>(null);
    const { height: containerHeight, isInitialized } =
        useTreeContainerHeight(treeContainerRef);

    /**
     * 缓存获取和存储
     * 缓存收集优先；在卸载数据
     */
    const { updateVirtualScroll } = useNormalTreeCache();

    /**
     * 初始化构建树阶段
     */
    useNormalTreeInit();

    /**
     * 状态统一获取
     */
    const {
        treeRef,
        stateLoadFailed,
        treeData,
        expandedKeys,
        loadedKeys,
        selectedKeys,
        fleetLoad,
    } = useNormalTreeState();

    const handleExpandedKeys: TreeProps['onExpand'] = useMemoizedFn((keys) => {
        NormalTreeInstance.updateExpandedKeys(keys);
    });

    const { run: handleSelectTreeNode } = useDebounceFn(
        (keys, info) => {
              performanceMeasureUtil.tree.start('normalTree.onSelect');
            // 只处理车组展开即可；会触发loadData去加载
            const { key } = info.node;
            if (key?.startsWith(FLEET_PREFIX) || key?.startsWith(VEHICLE_PREFIX)) {
                const expandedKeys =
                    NormalTreeInstance.getState()?.expandedKeys || [];
                const isExpanded = expandedKeys.includes(key);
                const newExpandedKeys = isExpanded
                    ? expandedKeys.filter((item) => item !== key)
                    : [...expandedKeys, key];
                NormalTreeInstance.updateExpandedKeys(newExpandedKeys);
            }
            NormalTreeInstance.updateSelectedKeys(keys);
            performanceMeasureUtil.tree.end('normalTree.onSelect');
            mapAndGroupMonitoringPlayOperate(keys);
        },
        { wait: 80 },
    );

    const handleLoadData: TreeProps['loadData'] = useMemoizedFn(
        async (node) => {
            return new Promise<void>((resolve) => {
                const { key } = node;
                const loadedKeys =
                    NormalTreeInstance.getState()?.loadedKeys || [];
                if (loadedKeys.includes(key)) {
                    resolve();
                    return;
                }
                NormalTreeInstance.loadFleets([key]).then(() => {
                    resolve();
                });
            });
        },
    );

    const handleVisibleChange = useMemoizedFn((treeNode) => {
        const vIds = treeNode
            .filter((i) =>TreeKeyHelper.parseTreeKey(i.key)?.[0] === 'vehicle')
            .map((i) => TreeKeyHelper.parseTreeKey(i.key)?.[2]);
        const dIds = treeNode
            .filter((i) =>TreeKeyHelper.parseTreeKey(i.key)?.[0] === 'device')
            .map((i) => TreeKeyHelper.parseTreeKey(i.key)?.[2]);
        rmPageConfig.data.RTDataInstance.updateVehicleSubscribe(vIds);
        rmPageConfig.useReuseStore
            .getState()
            .reuseData.onVisibleVehicleChange?.({
                vIds,
                dIds
            });
    });

    const renderTree =
        treeData?.length > 0 ? (
            <StarryTree
                hiddenLeafSwitcher
                motion={null}
                itemHeight={38}
                indentUnit="small"
                expandedKeys={expandedKeys}
                onExpand={handleExpandedKeys}
                onVisibleChange={handleVisibleChange}
                onVirtualScroll={updateVirtualScroll}
                loadData={handleLoadData}
                // @ts-ignore
                ref={treeRef}
                onSelect={handleSelectTreeNode}
                loadedKeys={loadedKeys}
                selectedKeys={selectedKeys}
                height={containerHeight || 0}
                treeData={treeData as StarryTree.TreeNode[]}
            />
        ) : (
            <Empty />
        );

    return (
        <div className="normal-tree-data-container">
            {stateLoadFailed ? (
                <div className="normal-tree-data-load-fail-tips">
                    <LoadFailErrorTips />
                </div>
            ) : null}
            <div className="normal-tree-data-content" ref={treeContainerRef}>
                {fleetLoad && isInitialized ? (
                    renderTree
                ) : (
                    <Spin style={{ width: '100%', marginTop: '20px' }} />
                )}
            </div>
        </div>
    );
};