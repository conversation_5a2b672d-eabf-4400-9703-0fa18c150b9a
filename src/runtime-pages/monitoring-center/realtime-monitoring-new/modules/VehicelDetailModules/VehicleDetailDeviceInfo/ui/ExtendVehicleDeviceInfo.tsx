import { JSXBase } from "@/types/pageReuse/pageReuseBase";
import { getCustomJsx, getCustomJsxAsync } from "@/utils/pageReuse";
import { useAsyncEffect } from "@streamax/hooks";
import { useState } from "react";

interface ExtendVehicleDeviceInfoProps {
	getVehicleDetailDeviceInfoExtendBlocks?: JSXBase;
	vehicleId: string;
}

export default (props: ExtendVehicleDeviceInfoProps) => {
	const { getVehicleDetailDeviceInfoExtendBlocks, vehicleId } = props;
	const [content, setContent] = useState<string>("");

    useAsyncEffect(async () => {
        const data = await getCustomJsxAsync(getVehicleDetailDeviceInfoExtendBlocks, [], {
            vehicleId,
		});
		
        setContent(data);
    }, [vehicleId]);

	return <>{ getVehicleDetailDeviceInfoExtendBlocks ? content : ""}</>;
};