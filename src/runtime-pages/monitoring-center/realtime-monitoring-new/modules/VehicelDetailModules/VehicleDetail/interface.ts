import { AlarmByPageItem } from '@/service/alarm';

export type DriverItem = {
    driverName: string; //司机名称
    // 驾驶员主键Id，字符串形式的雪花ID，存在以下几种情况
    // 1、负数雪花算法ID，表示未知司机，此时driverName和jobNumber无值；
    // 2、-2，表示无司机权限，此时driverName和jobNumber无值； 无权限 展示******
    // 3、正整数，表示正在驾驶的司机，此时driverName和jobNumber有值；
    driverId: string; //司机id
    jobNumber: string;
};

export const enum StateActive {
    ACTIVE = '1',
    INACTIVE = '0',
}
export interface DeviceItem {
    deviceId: string;
    stateList: {
        stateId: string;
        stateActive: StateActive;
    }[];
}

export interface Vehicle {
    fId: string;
    vId: string;
    vNo: string;
    vOnlineNumber: 1 | 0;
    vStates: string[];
    deviceList?: DeviceItem[];
    createTime: number;
    vType: null | number;
    driverList: DriverItem[];
    fleetList?: Record<string, any>[];
    gps: {
        lat: number;
        lng: number;
        angle?: number;
        speed?: number;
        gpsTime?: number;
    };
    driver: string;
    driverId: string;
    path: string;
    vehicleNumber: string;
    onlineState: number;
    devices?: DeviceItem[];
}

//ui props
export interface VehicleDetailProps {
    visible: boolean;
    title: string | React.ReactNode;
    onClose?: () => void;
    children: React.ReactNode;
}
export type VehicleDetailState = {
    vehicleId: string;
    vehicleInfo: Vehicle;
    alarmList: AlarmByPageItem[];
    visible: boolean;
    loading: boolean;
    fleetPathList: Record<string, any>[];
};
