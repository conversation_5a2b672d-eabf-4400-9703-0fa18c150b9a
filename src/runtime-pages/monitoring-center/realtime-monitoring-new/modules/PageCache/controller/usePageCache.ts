/**
 * 页面数据缓存的控制器层
 * 负责控制缓存的生命周期和缓存策略
 */
import { useEffect } from 'react';
import { useHistory } from '@base-app/runtime-lib/core';
import rmPageConfig from '@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config';

const POP_ACTION = 'POP';
const usePageDataCache = () => {
    const cacheInstance = rmPageConfig.data.PageCacheDataInstance;
    const history = useHistory();

    // 处理缓存初始化
    useEffect(() => {
        const initCache = async () => {
            await cacheInstance.loadFromStorage();
        };
        

        // 设置pathname并立即初始化缓存
        cacheInstance.useDataStore.setState({ 
            pathname: window.location.pathname
        });
        
        // 使用Promise.resolve确保在状态更新后执行initCache
        Promise.resolve().then(() => {
            // 只有history操作是POP，即回退或者刷新时，才使用缓存
            // 通过其他操作，如点击菜单、服务入口等操作渲染页面挂载组件，不使用缓存
            if (history.action === POP_ACTION) {
                initCache();
            } else {
                // 【135865】切换服务入口和菜单时，action为push，不使用缓存，手动清空缓存。
                // 因为pageConfig切换页面时不会销毁，当不需要使用状态时，需手动清除。
                cacheInstance.clearAll();
                // 当前操作是PUSH、REPLACE等，不使用缓存，直接设置状态为加载完毕
                cacheInstance.useDataStore.setState({
                    cacheLoaded: true,
                });
            }
        });
        // 暂时保留打印信息，用于定位action操作是否符合预期
        console.log('===[[[history location]]]===', history);

        return () => {
            // unlisten(); // 清理事件监听
            // 在组件卸载时保存到 sessionStorage
            cacheInstance.saveToStorage();
            cacheInstance.resetDataStore();
        };
    }, []);
};

export default usePageDataCache;
