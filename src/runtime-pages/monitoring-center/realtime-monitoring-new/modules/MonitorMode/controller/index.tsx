import { useMount } from 'ahooks';
import { useSafeStore } from '@/modules/common/withSafeStore';
import { confirmMonitorTalkAndRecordStatus } from '../utils/internal';
import type { MonitorModeUIProps } from '../interface';
import MonitorModeUI from '../ui/MonitorModeUI';
import rmPageConfig from '../../../page-config';

export default () => {
    const { MonitorModeInstance, MonitorTabsInstance, NormalTreeInstance } =
        rmPageConfig.data;
    const monitorMode = MonitorModeInstance.useDataStore?.(
        (state) => state.monitorMode,
    );

    useSafeStore(
        MonitorModeInstance.useDataStore,
        MonitorModeInstance.resetState,
    );

    useMount(() => MonitorModeInstance.load());

    MonitorModeInstance.onBeforeMonitorModeChange(async (event) => {
        const talkAndRecord = await confirmMonitorTalkAndRecordStatus();
        const result = [talkAndRecord].every(Boolean);
        if (result) {
            rmPageConfig.data.RealtimeVideoDataInstance.closeRealtimeVideo();
            // 模型切换清除所有缓存
            rmPageConfig.data.PageCacheDataInstance.clearAll();
            // 模型切换需要重置sort模块缓存
            rmPageConfig.data.NormalSortInstance.normalSortPlugin.reset();
        }
        return result;
    });

    MonitorModeInstance.onAfterMonitorModeChange(async (event) => {
        // 重置模式切换状态
        MonitorTabsInstance.restTabsActiveStatus();
        return true;
    });

    const handleMonitorModeChang: MonitorModeUIProps['onMonitorModeChange'] =
        async (mode) => {
            MonitorModeInstance.updateMonitorMode(mode);
        };

    return (
        <MonitorModeUI
            activeMonitorMode={monitorMode}
            onMonitorModeChange={handleMonitorModeChang}
        />
    );
};
