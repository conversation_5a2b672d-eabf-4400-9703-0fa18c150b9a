import React, { useState } from 'react';
import { useWebPlayer } from '../controller/useWebPlayer';
import { StarryPlayer } from '@/components/StarryPlayer';
import { StarryPlayer as StarryPlayerV2, utils } from '@base-app/runtime-lib';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import DataChangeListener from '../components/DataChangeListener';
import DataChangeListenerV2 from '../components/DataChangeListenerV2';
import { getAppGlobalData, useV2H5SDKAbility } from '@base-app/runtime-lib';
import { getBaseURL } from '@/utils/h5sdk';
import { getSDKWaterConfig } from '@/utils/getWaterMask';
import type { WebPlayerProps } from '../index';
import { useAsyncEffect } from '@streamax/hooks';
import IframeSDK from '@streamax/iframe-sdk';

const { videoAspectRatio } = utils;

// 使用V2.x版本的SDK
const LivevideoIns = async () => {
    const token = await window.localStorage.getItem('AUTH_TOKEN');
    return StarryPlayerV2.LivevideoPlayer.create({
    // 展示加载网速延时时间，单位毫秒， 2秒后显示加载网速
    showNetworkDelayTime:
        Number((window as any).APP_CONFIG['h5.player.start.loading.timeout']) *
        1000,
    // seek超时时间，单位秒
    seekTimeout: Number((window as any).APP_CONFIG['seek.timeout']) || 30,
    // 平台时区，单位秒
    platformTimeZone: Number(getAppGlobalData('PLATFORM_TIME_ZONE') || 0),
    // SDK请求前缀
    baseURL: getBaseURL(),
    // SDK headers
    defaultHeaders: {
        _tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
        _appId: getAppGlobalData('APP_ID'),
        _token: token,
    },
    // 加载H5SDK
    loadH5SDK: useV2H5SDKAbility.loadV2H5SDK,
})};

const WebPlayerUI = (props: WebPlayerProps) => {
    const {
        pageCode,
        showPlaceHolder,
        authWidgets,
        defaultPlayMode,
        moveInRefresh,
        disabledWidgets,
        hiddenWidgets,
        onScreenshot,
        onActiveChannelChange,
        getPlayerInitChannels,
        getPlayerInitLayout,
        onChannelLayoutChange,
        onSelectChannelChange,
        getRealtimeVideoRecordModal,
        onFullScreen = () => {},
        setRecordState,
        setPlayerStatus,
        getAutoCloseDuration,
        getAutoCloseAvaiable,
        renderRight,
        renderToolBar,
        handleBeforeAction,
        PlayerRef,
        v2H5SDKEnable,
        posterConfig,
        onRecordFinish,
        showFullScreen
    } = props;

    const [PlayerEle, setPlayerEle] = useState<any>('');


    // 渲染DataChangeListener
    const renderDataChangeListener = () => {
        const Listener = v2H5SDKEnable ? DataChangeListenerV2 : DataChangeListener;
        return (
            <Listener
                onRecordStateChange={setRecordState}
                onFullScreenChange={onFullScreen}
                onPlayerStatusChange={setPlayerStatus}
                onRecordFinish={onRecordFinish}
                getRealtimeVideoRecordModal={getRealtimeVideoRecordModal}
            />
        );
    };

    const handlePlayAspectRatioChange = (v: string) => {
        videoAspectRatio.setVideoAspectRatio(v as any);
    }

    // 渲染播放器
    const renderPlayer = () => {
        return (
            <DisableRightMouseClickImage>
                <PlayerEle
                    isLive
                    ref={PlayerRef}
                    pageCode={pageCode}
                    showPlaceHolder={showPlaceHolder}
                    authWidgets={authWidgets}
                    defaultPlayMode={defaultPlayMode}
                    autoClose={{
                        available: getAutoCloseAvaiable(),
                        resetAutoCloseTimeOnMove: moveInRefresh,
                        autoCloseDuration: getAutoCloseDuration(),
                    }}
                    customControlBar={{
                        renderRight: renderRight,
                    }}
                    customToolBar={{
                        render: renderToolBar,
                    }}
                    disabledWidgets={disabledWidgets}
                    hiddenWidgets={hiddenWidgets}
                    getSDKWaterConfig={getSDKWaterConfig}
                    onBeforeAction={handleBeforeAction}
                    onScreenshot={onScreenshot}
                    onActiveChannelChange={onActiveChannelChange}
                    getPlayerInitChannels={getPlayerInitChannels}
                    getPlayerInitLayout={getPlayerInitLayout}
                    onSelectChannelChange={onSelectChannelChange}
                    poster={posterConfig}
                    onPlayAspectRatioChange={handlePlayAspectRatioChange}
                    enableLoading={true}
                    onChannelLayoutChange={onChannelLayoutChange}
                    enableLoading={true}
                    showFullScreen={showFullScreen}
                />
            </DisableRightMouseClickImage>
        );
    };

    useAsyncEffect(async () => {
        const Player = v2H5SDKEnable ? await LivevideoIns() : StarryPlayer;
        setPlayerEle(Player);
    }, [v2H5SDKEnable]);

    return (
        <div
            style={{ height: '100%' }}
            className="starry-player-to-living-playing"
        >
            {renderDataChangeListener()}
            {PlayerEle && renderPlayer()}
        </div>
    );
};

export default WebPlayerUI;
