import React from 'react';
import { Form, Modal, Input, NewDatePicker } from '@streamax/poppy';
import { i18n, utils, StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import AllChekcbox from '@/components/AllCheckbox';
import { checkFieldSpace } from '@/utils/commonFun';

const FormItem = Form.Item;
const validatorVideoName = utils.validator.validateBaseVehicleNumber;

export interface RecordUIProps {
    visible: boolean;
    isFullScreen?: boolean;
    form: any;
    channelOptions: any[];
    submitLoading: boolean;
    onOk: () => void;
    onClose?: () => void;
    getDomContainer?: () => HTMLElement;
}

const RecordUI: React.FC<RecordUIProps> = ({
    visible,
    isFullScreen,
    form,
    channelOptions,
    submitLoading,
    onOk,
    onClose,
    getDomContainer
}) => {
    const defaultGetDomContainer = () => {
        return isFullScreen ? document.getElementById('starry-player') : document.body;
    };

    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '视频名称不能为空'));
    };

    return (
        <Modal
            // @ts-ignore
            getContainer={getDomContainer ? getDomContainer : defaultGetDomContainer}
            visible={visible}
            title={i18n.t('name', '录制视频')}
            maskClosable={false}
            width={440}
            okText={i18n.t('action', '确定')}
            cancelText={i18n.t('action', '取消')}
            onOk={onOk}
            onCancel={() => onClose?.()}
            okButtonProps={{
                loading: submitLoading
            }}
        >
            <Form layout="vertical" form={form}>
                <AFormItem name="vehicleName" label={i18n.t('name', '录制车辆')}>
                    <Input disabled placeholder={i18n.t('message', '请选择车辆')} />
                </AFormItem>
                <AFormItem name="videoTime" label={i18n.t('name', '录制时间')}>
                    {/* @ts-ignore */}
                    <NewDatePicker.NewRangePicker
                        showTime
                        disabled
                        placeholder={[i18n.t('message', '开始时间'), i18n.t('message', '结束时间')]}
                        style={{ width: '100%' }}
                    />
                </AFormItem>
                <AFormItem
                    name="evidenceName"
                    label={i18n.t('name', '视频名称')}
                    rules={[
                        {
                            required: true,
                            validator: checkSpace,
                        },
                        {
                            validator: validatorVideoName,
                        },
                    ]}
                >
                    <Input
                        allowClear
                        maxLength={50}
                        placeholder={i18n.t('message', '请输入视频名称')}
                    />
                </AFormItem>
                <FormItem
                    name="channelNos"
                    label={i18n.t('name', '下载通道')}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <AllChekcbox options={channelOptions} />
                </FormItem>
            </Form>
        </Modal>
    );
};

export default RecordUI;
