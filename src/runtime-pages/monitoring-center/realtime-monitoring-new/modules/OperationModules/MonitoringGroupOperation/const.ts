export const HANDLE_LISTEN = 'saas.search.alarm.handle.type.listen';
export const HANDLE_INTERCORM = 'saas.search.alarm.handle.type.intercorm';
export const HANDLE_VEHICLEESCORT = 'saas.search.alarm.handle.type.vehicleescort';
export const HANDLE_PLAYBACK = 'saas.search.alarm.handle.type.playback';
/**
 * 定义车辆相关的常量
 */
// 在线 stateId
export const ONLINE_STATE = '1461206870592231111';
// 离线 stateId
export const OFFLINE_STATE = '1461206870592232222';
// 休眠 stateId
export const DORMANCY_STATE = '1461206870592252222';
// 报警 stateId
export const ALARM_STATE = '1461206870592233333';
// 报警 stateId TODO
export const ALARM_STATE_CODE = '1003';

export const VEHICLE_STATES = {
    ONLINE_STATE,
    OFFLINE_STATE,
    DORMANCY_STATE,
};
// 未分组车辆的默认车组id
export const UN_GROUP_FLEET_ID = '0';
// 顶级车组父车组ID
export const TOP_FLEET_PARENT_ID = '';