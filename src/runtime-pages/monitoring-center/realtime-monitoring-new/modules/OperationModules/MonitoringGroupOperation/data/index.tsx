import { create } from 'zustand';
import { MonitoringGroupOperationState } from '../interface';
import SingleVehicleEscortOperation from '../../OperationEnum/SingleVehicleEscortOperation';
import DevicePlaybackOperation from '../../OperationEnum/DevicePlaybackOperation';
import ServePlaybackOperation from '../../OperationEnum/ServePlaybackOperation';
import MixPlaybackOperation from '../../OperationEnum/MixPlaybackOperation';
import TrackPlaybackOperation from '../../OperationEnum/TrackPlaybackOperation';
import MessageSendOperation from '../../OperationEnum/MessageSendOperation';
import { DORMANCY_STATE, OFFLINE_STATE, ONLINE_STATE } from '../const';
import RemoveMonitorGroupOperation from '../../OperationEnum/RemoveMonitorGroupOperation';
import BaseReuse from '@/class/BaseReuse';

const initialState: MonitoringGroupOperationState = {
    vehicleId: '',
    operationList: [
        {
            key: 'singleVehicleEscort',
            component: <SingleVehicleEscortOperation />,
            code: '@base:@page:realtime.monitoring@action:single.vehicle.escort',
            showState: [ONLINE_STATE],
        },
        {
            key: 'devicePlayback',
            component: <DevicePlaybackOperation />,
            code: '@base:@page:realtime.monitoring@action:device.playback',
            showState: [ONLINE_STATE],
        },
        {
            key: 'servePlayback',
            component: <ServePlaybackOperation />,
            code: '@base:@page:realtime.monitoring@action:serve.playback',
            showState: [OFFLINE_STATE, ONLINE_STATE],
        },
        {
            key: 'mixPlayback',
            component: <MixPlaybackOperation />,
            code: '@base:@page:realtime.monitoring@action:mix.playback',
            showState: [OFFLINE_STATE, ONLINE_STATE],
        },
        {
            key: 'trackPlayback',
            component: <TrackPlaybackOperation />,
            code: '@base:@page:realtime.monitoring@action:track.playback',
            showState: [OFFLINE_STATE, ONLINE_STATE, DORMANCY_STATE],
        },
        {
            key: 'messageSend',
            component: <MessageSendOperation />,
            closePopover: true,
            code: '@base:@page:realtime.monitoring@action:messagesend',
            showState: [ONLINE_STATE],
        },
        {
            key: 'removeMonitorGroup',
            component: <RemoveMonitorGroupOperation />,
            filterDeviceList: true,
            showState: [OFFLINE_STATE, ONLINE_STATE, DORMANCY_STATE],
            showDeviceList: false
        },
    ],
};

export default class MonitoringGroupOperationData extends BaseReuse<
    Record<string, any>
> {
    setReuse(reuse: Partial<Record<string, any>>): void {
        // Implementation
    }

    getReuse(): Record<string, any> {
        // Implementation
        return {};
    }
    useDataStore = create<MonitoringGroupOperationState>()((set, get) => ({
        ...initialState,
    }));
}