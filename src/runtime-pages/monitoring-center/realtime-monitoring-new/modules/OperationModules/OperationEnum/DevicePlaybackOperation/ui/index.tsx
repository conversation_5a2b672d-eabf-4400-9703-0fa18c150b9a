import { IconVideoTapeFill } from '@streamax/poppy-icons';
import BaseOperationItem from '../../../BaseOperationItem';
import { BaseOperationItemProps } from '../../../BaseOperationItem';
import { i18n } from '@base-app/runtime-lib';
import { VEHICLE_STATES } from '../../../BaseOperationItem/const';

const DevicePlaybackOperation = (props: Partial<BaseOperationItemProps>) => {
    const { ONLINE_STATE, OFFLINE_STATE, DORMANCY_STATE } = VEHICLE_STATES;
    const showState = [ONLINE_STATE];    
    return (
        // @ts-ignore
        <BaseOperationItem
            name={i18n.t('name', '设备回放')}
            operationKey="devicePlayback"
            showState={showState}
            showDeviceList
            code="@base:@page:realtime.monitoring@action:device.playback"
            icon={<IconVideoTapeFill />}
            {...props}
        />
    );
};

export default DevicePlaybackOperation;
