import { ReactNode } from "react";

export const enum StateActive {
    ACTIVE = 1,
    INACTIVE = 0,
}
export type DeviceInfo = {
    deviceId: string;
    stateList: {
        stateId: string;
        stateActive: StateActive;
    }[];
};
export type OperateComponentItem = {
    key: string;
    component?: ReactNode;
    name?: string;
    closePopover?: boolean;
    icon?: ReactNode;
    showState?: any[];
    showOnlineState?: any[];
    validateOffLine?: boolean;
    showDeviceList?: boolean;
    filterDeviceList?: boolean;
    code?: string;
    onClick?: (data: any) => void;
};
export type VehicleDetailOperationState = {
    vehicleId: string;
    operationList: OperateComponentItem[];
};

export type VehicleDetailOperationProps = {
    vehicleId: string;
    deviceId: string;
}
// 车辆状态统计查询
export const enum VehicleStateMenu {
    // 0 未激活
    NOT_ACTIVATED = 0,
    // 1启用
    ENABLE = 1,
    // 2停用
    DISABLE = 2,
}