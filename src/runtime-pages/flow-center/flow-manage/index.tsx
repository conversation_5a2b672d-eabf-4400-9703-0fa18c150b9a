import companyApi from '@/service/company';
import {
    getPageList,
    getMonthSimplePreview,
    getExcessDeviceCount,
    updateDeviceControl,
    isOpenOnMonth,
} from '@/service/flow-center';

import { getSimpleVehicleList } from '@/service/vehicle';
import { DatePicker, Form, ProTable, Select, Switch, Table, Tooltip, message } from '@streamax/poppy';
import { IconExport, IconRequestFill, IconSwitch02Fill } from '@streamax/poppy-icons';
import {
    getAppGlobalData,
    i18n,
    utils,
    Auth,
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
    useUrlSearchStore,
} from '@base-app/runtime-lib';
import cn from 'classnames';
import {
    PageBreadcrumbLayout,
    PageCardLayout,
    StarryModal,
} from '@base-app/runtime-lib';
import { Link, useLocation } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import type { GetDataSourceParams } from '@streamax/starry-components/lib/list-data-container';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { getTotalUnit, timeZone, transformTotal } from '../Common';
import GroupTree from './components/GoupTree';
import DeviceShow from '@/components/DeviceShow';
import type { DeviceData } from '@/components/DeviceShow';
import { IS_DST, disabledFutureMonths, getOffsetTimeByTimeStamp } from '@/utils/commonFun';
import './index.less';
import SearchInput from './components/SearchInput';
import StatusDot from './components/status-dot';
import { Month } from '@/utils/commonFun';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import type { Moment } from 'moment';
import { byteToGB, isOpenFlowControl } from '@/utils/flow';
import {
    flowStatusOptions,
    handleStatusOptions,
    handleMaps,
    handleStatusColors,
    controlColors,
} from '../constant';
import calcHeight from '@/utils/calcStickyHeight';
import { isNil } from 'lodash';
import InfoBack from '@/components/InfoBack';
import { exportExcel } from '@/service/import-export';
import { useDebounceFn } from 'ahooks';
import { useSystemComponentStyle } from '@base-app/runtime-lib';
import { RspDrawerTemplate } from '@streamax/responsive-layout';
import {
    combineDimensions,
    COMPONENT_ID,
    dimensionGroups,
    FLOW_TYPE
} from "@/runtime-pages/flow-center/dimensionPoolModule";
import { dateFormatWithGlobalTimeFormat } from '@/utils/time';
const { timestampToZeroTimeStamp, zeroTimeStampToFormatTime } = utils.formator;

const { MonthPicker } = DatePicker;

export interface Vehicle {
    fId: string;
    vId: string;
    vNo: string;
    vOnlineNumber: 1 | 0;
    vStates: string[];
    deviceList?: Record<string, any>[];
    driverList: Record<string, any>[];
    gps: {
        lat: number;
        lng: number;
        ang: number;
    };
    createTime: number;
}

interface ReqParams {
    fleetIds: string;
    vehicleIds: string;
    time: any;
    handleStatus?: string | number;
    flowStatus?: string | number;
}
const defaultOverviewData = {
    maxCount: 0,
    avgCount: 0,
    excessCount: 0,
};
const FlowManage = () => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const appId = getAppGlobalData('APP_ID') || '';
    const initSelectTime = moment().startOf('month');
    const [form] = Form.useForm();
    const [queryParams, setQueryParams] = useState<ReqParams>({
        fleetIds: '',
        vehicleIds: '',
        time: initSelectTime,
    });
    const [list, setList] = useState<any[]>([]);
    const [selectKey, setSelectKey] = useState<string>('');
    const [selectKeyName, setSelectKeyName] = useState<string>('选择车组');
    const setSelectKeyRef = useRef<string>('');
    const isReset = useRef(false);
    const [openControl, setOpenControl] = useState<boolean>(); // 是否开启流量管控
    const [month, setMonth] = useState(initSelectTime.month() + 1);
    const [overviewData, setOverviewData] = useState<any>(defaultOverviewData);
    const [currentOpen, setCurrentOpen] = useState<boolean>();
    const tableRef = useRef<any>();
    const groupTreeRef = useRef<any>();
    const app_user_config = getAppGlobalData('APP_USER_CONFIG');
    const {
        monthDate,
        fleetId,
        vehicleNumber,
    } = useLocation().query;
    const currentMonth = moment().get('month') + 1;
    const [hasReset, setHasReset] = useState(false); // 是否重置查询条件
    const [lastQueryParams, setLastQueryParams] = useState({});
    // 保存搜索条件字段列表
    const searchStore = useUrlSearchStore();
    const { flowStatus: urlFlowStatus } = searchStore.get();
    const functionalControlCode =
        '@base:@page:flow.manage@action:functional.control'; // 功能管控资源
    const PAGINATION_HEIGHT = 86;
    const { showSticky } = useHideTableStickyScroll({
        paginAtionHight: PAGINATION_HEIGHT,
        dataSource: list,
    });
    useEffect(() => {
        if (monthDate) {
            setMonth(moment(monthDate).startOf('month').month() + 1);
        }
        if (vehicleNumber) {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                multiSearch: {
                    searchValue: vehicleNumber,
                    searchType: 'vehicleNumber',
                },
            });
        }
        if (fleetId) {
            setSelectKey(fleetId);
            setSelectKeyRef.current = fleetId;
        }
        getOverview(
            monthDate
                ? moment(monthDate).startOf('month')
                : moment().startOf('month'),
            fleetId,
        );
        searchIsOpenControl();
        if (urlFlowStatus || urlFlowStatus == 0) {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                flowStatus: Number(urlFlowStatus),
            });
        }
    }, [monthDate, vehicleNumber, fleetId, urlFlowStatus]);

    // 获取是否开启流量管控
    const searchIsOpenControl = async () => {
        const limitInfo = await isOpenFlowControl(appId);
        setOpenControl(limitInfo?.limit as boolean);
    };

    useEffect(() => {
        isReset.current = true;
        const params = form.getFieldsValue();
        tableRef.current?.loadDataSource({ ...params, page: 1 });
    }, [selectKey]);

    const getOverview = async (time: Moment, fleetId?: string) => {
        const fleetData = await getFleetList();
        const [year, month] = time.format('YYYY-MM').split('-');
        const timeObj = Month.startAndEndDateStr(Number(year), Number(month));
        const params = {
            timeZoneOffset: IS_DST(time)
                ? Number(timeZone) + app_user_config?.summerTimeOffset * 60
                : Number(timeZone),
            startDate: timeObj.startTime,
            endDate: timeObj.endTime,
            fleetIds:
                fleetId || (selectKey == '' ? fleetData.join(',') : selectKey),
        };
        const flowData = await getMonthSimplePreview(params);
        let excessData: any;
        if (openControl || openControl === undefined) {
            // 第一次默认查询一次，后续openControl为true/false
            excessData = await getExcessDeviceCount(params);
        }
        setOverviewData({
            excessCount: excessData,
            ...flowData,
        });
    };

    const getFleetList = () => {
        return companyApi
            .getTree({
                appId,
                domainType: 1,
            })
            .then((data: any) => {
                const list: any[] = [];
                data.forEach((item: any) => {
                    list.push(item.key);
                });
                const unGroupData = [];

                return getSimpleVehicleList({
                    // 1启用 2停用
                    vehicleState: 1,
                }).then((ele) => {
                    // 一个车挂在多个车组得情况下  不能算在未分组里面
                    let vehicleTemp: Pick<
                        Vehicle,
                        'fId' | 'vId' | 'vNo' | 'createTime'
                    >[] = ele.map((item: any) => {
                        const [fId, vId, vNo, , createTime] = item.split(',');
                        const re = { fId, vId, vNo, createTime };
                        return re;
                    });
                    vehicleTemp = vehicleTemp.filter((item) => {
                        return !(
                            item.fId === '0' &&
                            vehicleTemp.some(
                                (i) => i.fId !== '0' && i.vId === item.vId,
                            )
                        );
                    });
                    vehicleTemp.map((item) => {
                        if (item.fId == '0') {
                            unGroupData.push(item);
                        }
                    });
                    if (unGroupData.length > 0) {
                        list.push(-1);
                    }
                    return list;
                });
            });
    };

    let columns: any = [
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            ellipsis: true,
            fixed: 'left',
            width: 200,
            render: (text: any, record: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Link
                            to={`/flow/flow-manage/detail?vehicleId=${
                                record.vehicleId
                            }&vehicleNumber=${encodeURIComponent(
                                record.vehicleNumber,
                            )}&deviceId=${
                                record.deviceId
                            }&selectTime=${queryParams.time?.format('YYYY-MM')}`}
                        >
                            {text}
                        </Link>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },

        {
            title: i18n.t('name', '设备编号'),
            dataIndex: 'deviceNo',
            key: 'deviceNo',
            ellipsis: { showTitle: true },
            width: 200,
            render: (text: string, record: DeviceData) => (
                <DeviceShow deviceInfo={record} />
            ),
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetList',
            key: 'fleetList',
            ellipsis: { showTitle: false },
            calcWidth: () => 200,
            width: 280,
            render: (text: any, record: any) => {
                const fleet = record?.fleetList || [{}];
                const name =
                    (fleet || [])
                        .map((item: any) => {
                            return (item || {}).fleetName;
                        })
                        .join(',') || '-';
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {name}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '流量消耗'),
            dataIndex: 'total',
            key: 'total',
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
            calcWidth: () => 160,
            width: 180,
            render: (text: number, record: any) => {
                if (openControl) {
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            <span
                                className={`${
                                    openControl &&
                                    !isNil(record.flowLimit) &&
                                    byteToGB(text) > byteToGB(record.flowLimit)
                                        ? 'warn-item'
                                        : ''
                                }`}
                            >
                                {transformTotal(text).toFixed(2) +
                                    getTotalUnit(text)}
                            </span>{' '}
                            <span>
                                {isNil(record.flowLimit)
                                    ? ''
                                    : `/${byteToGB(record.flowLimit)}GB`}
                            </span>
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                } else {
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            {transformTotal(text).toFixed(2) +
                                getTotalUnit(text)}
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                }
            },
        },
    ];
    if (openControl) {
        const otherItems = [
            {
                title: i18n.t('name', '流量状态'),
                dataIndex: 'flowStatus',
                key: 'flowStatus',
                ellipsis: true,
                calcWidth: () => 120,
                width: 180,
                render: (text: number, record: any) => {
                    const flag =
                        record.total >
                        (record?.flowLimit || 0)
                            ? true
                            : false;
                    return currentOpen && !isNil(record.flowLimit) ? (
                        <StatusDot
                            label={
                                flag
                                    ? i18n.t('name', '超额')
                                    : i18n.t('name', '正常')
                            }
                            color={flag ? '#FF4D4F' : '#52C41A'}
                        />
                    ) : (
                        '-'
                    );
                },
            },
            {
                title: i18n.t('name', '处理状态'),
                dataIndex: 'handleStatus',
                key: 'handleStatus',
                ellipsis: true,
                calcWidth: () => 120,
                width: 180,
                render: (text: number, record: any) => {
                    const isUpper =
                        record.total >
                        (record?.flowLimit || 0);
                    return currentOpen &&
                        !isNil(record.flowLimit) &&
                        isUpper ? (
                        <StatusDot
                            label={handleMaps[String(text)]}
                            color={handleStatusColors[String(text)]}
                        />
                    ) : (
                        '-'
                    );
                },
            },
            {
                title: i18n.t('name', '功能管控'),
                dataIndex: 'featureControl',
                key: 'featureControl',
                ellipsis: true,
                calcWidth: () => 120,
                width: 180,
                render: (text: number, record: any) => {
                    const isUpper =
                        record.total >
                        (record?.flowLimit || 0);
                    return currentOpen &&
                        !isNil(record.flowLimit) &&
                        isUpper ? (
                        <span className="config-control-item-container">
                            <StatusDot
                                label={
                                    text
                                        ? i18n.t('name', '开启')
                                        : i18n.t('name', '关闭')
                                }
                                color={controlColors[String(text)]}
                            />
                            <Auth code={functionalControlCode}>
                                <Switch
                                    className="switch-item"
                                    checked={text === 1}
                                    onChange={(checked) =>
                                        handleControl(checked, record)
                                    }
                                    disabled={currentMonth !== month}
                                    size="small"
                                />
                            </Auth>
                        </span>
                    ) : (
                        '-'
                    );
                },
            },
            {
                title: i18n.t('name', '操作人'),
                dataIndex: 'operUserName',
                key: 'operUserName',
                ellipsis: true,
                width: 180,
                render: (text: string, record: any) => {
                    const isUpper =
                        record.total >
                        (record?.flowLimit || 0);
                    return currentOpen &&
                        !isNil(record.flowLimit) &&
                        isUpper &&
                        text ? (
                        <StarryAbroadOverflowEllipsisContainer>
                            {text}
                        </StarryAbroadOverflowEllipsisContainer>
                    ) : (
                        '-'
                    );
                },
            },
            {
                title: i18n.t('name', '操作时间'),
                dataIndex: 'operTime',
                key: 'operTime',
                ellipsis: true,
                sorter: true,
                calcWidth: () => 165,
                width: 200,
                render: (text: number, record: any) => {
                    const isUpper =
                        (record.total) >
                        (record?.flowLimit || 0);
                    return currentOpen &&
                        !isNil(record.flowLimit) &&
                        text &&
                        isUpper ? (
                        <StarryAbroadOverflowEllipsisContainer>
                            {zeroTimeStampToFormatTime(text)}
                        </StarryAbroadOverflowEllipsisContainer>
                    ) : (
                        '-'
                    );
                },
            },
        ];
        columns = [...columns, ...otherItems];
    }

    const handleControl = (checked: boolean, record: any) => {
        const { deviceAlias, deviceNo, vehicleNumber, deviceCount } = record;
        const showDevice = '(' + (deviceAlias || deviceNo) + ')';
        const showVehicleNumber =
            vehicleNumber + (deviceCount > 1 ? showDevice : '');
        StarryModal.confirm({
            destroyOnClose: true,
            centered: true,
            title: i18n.t('message', '操作确认'),
            content: checked
                ? i18n.t(
                      'message',
                      '确认要对“{vehicleNumber}”开启功能管控吗？',
                      {
                          vehicleNumber: showVehicleNumber,
                      },
                  )
                : i18n.t(
                      'message',
                      '确认要对“{vehicleNumber}”关闭功能管控吗？',
                      {
                          vehicleNumber: showVehicleNumber,
                      },
                  ),

            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: async () => {
                const result = await updateDeviceControl({
                    deviceId: record.deviceId,
                    switchFlag: checked ? 1 : 0,
                });
                if (result) {
                    tableRef.current.loadDataSource(queryParams);
                    message.success(i18n.t('message', '操作成功'));
                }
            },
        });
    };
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(columns, {
            disabledKeys: ['vehicleNumber', 'deviceNo', 'total', 'countDate'],
            storageKey: '@base:@page:flow.center.manage',
        });

    const drawerRef = useRef(null);
    const onTreeSelect = async (key: string,treeNode: Record<string, string>) => {
        setSelectKey(key?.substring(key.indexOf('-') + 1) ?? '');
        setSelectKeyRef.current = key?.substring(key.indexOf('-') + 1) ?? '';
        setSelectKeyName(treeNode?.name || '');
        drawerRef?.current?.closeDrawer();
    };

    const onSelectMonthChange = (value: any) => {
        setQueryParams({ ...queryParams, time: value });
    };
    const disabledDate = (current: any) => {
        return ((current && current > moment().endOf('month')) ||
            current < moment().subtract(6, 'month')) as boolean;
    };

    const fetchList = async (params?: GetDataSourceParams) => {
        const month = params?.time.format('YYYY-MM').split('-').join('');
        const isOpen = await isOpenOnMonth({ month });
        setCurrentOpen(isOpen);
        setQueryParams(params);
        const fleetData = await getFleetList();
        const fleetIds =
            setSelectKeyRef.current == ''
                ? fleetData.join(',')
                : setSelectKeyRef.current;
        let reqParams: any = {
            page: !params ? 1 : (params?.page as number),
            pageSize: !params ? 20 : (params?.pageSize as number),
            fleetIds,
            timeZoneOffset: getOffsetTimeByTimeStamp(moment(params?.time)) || 0,
        };
        let flowStatus = params?.flowStatus;
        if (!params) {
            const [year, month] = queryParams.time.format('YYYY-MM').split('-');
            const time = Month.startAndEndDateStr(Number(year), Number(month));
            reqParams = {
                ...reqParams,
                startDate: time.startTime,
                endDate: time.endTime,
            };
            getOverview(queryParams.time);
        } else {
            const { time, handleStatus, multiSearch } = params;
            if (!flowStatus && !hasReset) {
                // 从其他页面跳转到流量管理时查询
                flowStatus = urlFlowStatus ?? flowStatus;
            }
            getOverview(params.time);
            const { multiSearch: formMultiSearch } = form.getFieldsValue();
            const [year, month] = time.format('YYYY-MM').split('-');
            const timeObj = Month.startAndEndDateStr(
                Number(year),
                Number(month),
            );

            reqParams = {
                ...reqParams,
                startDate: timeObj.startTime,
                endDate: timeObj.endTime,
                flowStatus,
                handleStatus,
                orderByField: params?.orderByField,
                orderByFlag: params?.orderByFlag,
            };
            if (multiSearch) {
                reqParams = {
                    ...reqParams,
                    [multiSearch?.searchType]:
                        multiSearch?.searchValue ||
                        formMultiSearch?.searchValue,
                };
            }
        }

        if (reqParams.fleetIds == '0') {
            reqParams.fleetIds = '-1';
        }
        const result = await getPageList(reqParams);
        setList(result.list);
        setLastQueryParams(reqParams);
        const overviewMonth = params ? params.time : queryParams.time;
        setMonth(moment(overviewMonth).get('month') + 1);
        setHasReset(true);
        searchStore.set({
            flowStatus: flowStatus,
            monthDate: moment(overviewMonth).startOf('month').format('YYYY-MM-DD')
        });
        return result;
    };

    let queryItems: any = [
        {
            label: i18n.t('name', '月份'),
            name: 'time',
            field: MonthPicker,
            itemProps: {
                initialValue: monthDate
                    ? moment(monthDate).startOf('month')
                    : moment().startOf('month'),
            },
            fieldProps: {
                allowClear: false,
                onChange: onSelectMonthChange,
                disabledDate: disabledFutureMonths
            },
        },
        {
            name: 'multiSearch',
            field: SearchInput,
            fieldProps: {
                allowClear: true,
            },
            itemProps: {
                initialValue: {
                    searchType: 'vehicleNumber',
                    searchValue: '',
                },
            },
        },
    ];

    if (currentOpen) {
        const otherItems = [
            {
                label: i18n.t('name', '流量状态'),
                name: 'flowStatus',
                field: Select,
                fieldProps: {
                    options: flowStatusOptions,
                    placeholder: i18n.t('message', '请选择流量状态'),
                    allowClear: true,
                },
            },
            {
                label: i18n.t('name', '处理状态'),
                name: 'handleStatus',
                field: Select,
                fieldProps: {
                    options: handleStatusOptions,
                    placeholder: i18n.t('message', '请选择处理状态'),
                    allowClear: true,
                },
            },
        ];
        queryItems = [...queryItems, ...otherItems];
    }
    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        const { field, order } = sorter;
        const orderByField = field === 'total' ? 0 : 1;
        const orderByFlag = order === 'ascend' ? 0 : 1;
        tableRef.current.loadDataSource({
            ...queryParams,
            orderByField,
            orderByFlag,
        });
    };
    const handleReset = () => {
        setMonth(initSelectTime.month() + 1);
        getOverview(initSelectTime);
        setHasReset(true);
        return true;
    };
    // 导出数据
    const _exportData = () => {
        let headersArr = [
            {
                columnName: 'vehicleNumber',
                title: i18n.t('name', '车牌号码'),
            },
            {
                columnName: 'deviceNo',
                title: i18n.t('name', '设备编号'),
            },
            {
                columnName: 'fleetList',
                title: i18n.t('name', '归属车组'),
            },
            {
                columnName: 'total',
                title: i18n.t('name', '消耗流量'),
            },
        ];
        if (openControl) {
            const otherItems = [
                {
                    columnName: 'flowStatus',
                    title: i18n.t('name', '流量状态'),
                },
                {
                    columnName: 'handleStatus',
                    title: i18n.t('name', '处理状态'),
                },
                {
                    columnName: 'featureControl',
                    title: i18n.t('name', '功能管控'),
                },
                {
                    columnName: 'operUserName',
                    title: i18n.t('name', '操作人'),
                },
                {
                    columnName: 'operTime',
                    title: i18n.t('name', '操作时间'),
                },
            ];
            headersArr = [...headersArr, ...otherItems];
        }

        const exportDimensionProps = {
            [FLOW_TYPE.LIVE]: {
                columnName: 'livingPlay',
            },
            [FLOW_TYPE.PLAYBACK]: {
                columnName: 'playBack',
            },
            [FLOW_TYPE.DOWNLOAD]: {
                columnName: 'videoDownload',
            },
            [FLOW_TYPE.INTERCOM]: {
                columnName: 'intercom',
            },
            [FLOW_TYPE.LISTEN]: {
                columnName: 'monitor',
            },
            [FLOW_TYPE.EVIDENCE]: {
                columnName: 'evidenceUpload',
            },
            [FLOW_TYPE.PICTURE]: {
                columnName: 'imageUpload',
            },
            [FLOW_TYPE.OTHER]: {
                columnName: 'otherFun',
            },
            // 其他维度的特有属性...
        };
        // 获取导出维度组
        const exportDimensions = dimensionGroups[COMPONENT_ID.LIST_EXPORT];

        // 组合共性和个性
        const enhancedDimensions = combineDimensions(exportDimensions,exportDimensionProps);

        headersArr = [...headersArr, ...enhancedDimensions.map(i=>{
            return {
                columnName: i.columnName,
                title: i.name,
            }
        })]
            .map((item,index)=>({...item,index}));
        const startMonth = dateFormatWithGlobalTimeFormat(lastQueryParams.startDate, 'YYYY-MM')?.replace("-",'');
        const sheetArr = [
            {
                sheetName: i18n.t('name', '设备流量统计') + `-${startMonth}`,
                excelHeaders: headersArr,
                queryParam: {
                    param: {
                        ...lastQueryParams,
                        pageSize: Number.MAX_SAFE_INTEGER,
                    },
                },
            },
        ];
        exportExcel({
            executorHandler: 'deviceFlowManagerExport',
            serviceName: 'base-flow-service',
            taskType: 1523,
            isAsync: true,
            excelType: 'XLSX',
            fileName:
                i18n.t('name', '设备流量统计') +
                `-${startMonth}-${moment().unix()}`,
            sheetQueryParams: sheetArr,
        }).then(() => {
            message.success(
                i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
            );
        });
    };
    const { run: exportData } = useDebounceFn(_exportData, {
        wait: 500,
    });

    return (
        <PageBreadcrumbLayout>
            <PageCardLayout>
                <div className="flow-data-manage">
                    <RspDrawerTemplate breakpoint='lg' gutter={[24,24]}>
                        <RspDrawerTemplate.Left
                        ref={drawerRef}
                        drawerTrigger={ <div>
                            <span className='rsp-drawer-title'>{(isNil(selectKeyName)||selectKeyName=='')?i18n.t("name", "选择车组"):selectKeyName}</span>
                        <StarryAbroadIcon> <IconSwitch02Fill /></StarryAbroadIcon>
                         </div>} drawerProps={{width:"400px",className:'flow-data-manage-drawer'}}>
                         <div className="left">
                        <GroupTree onTreeSelect={onTreeSelect} ref={groupTreeRef} />
                          </div>
                        </RspDrawerTemplate.Left>
                        <RspDrawerTemplate.Right>
                        <div className="right">
                        <ListDataContainer
                            className="data-container"
                            getDataSource={fetchList}
                            queryForm={{
                                items: queryItems,
                                form,
                                onReset: handleReset,
                            }}
                            ref={tableRef}
                            loadDataSourceOnMount={true}
                            toolbar={{
                                extraRight:[
                                    <Tooltip
                                        title={i18n.t('action', '导出')}
                                        placement="top"
                                        key="action-top"
                                    >
                                        <span>
                                            <StarryAbroadIcon>
                                                <IconExport
                                                    onClick={exportData}
                                                />
                                            </StarryAbroadIcon>
                                        </span>
                                    </Tooltip>
                                ],
                                extraIconBtns: [
                                    // @ts-ignore
                                    <TableColumnSetting
                                        key="setting"
                                        {...tableColumnSettingProps}
                                    />,
                                ],
                            }}
                            listRender={(data) => (
                                <>
                                    <InfoBack
                                        className="custom-tip-container"
                                        title={
                                            <>
                                                <a>{month}</a>{' '}
                                                <span>
                                                    {i18n.t(
                                                        'message',
                                                        '月流量使用最多的设备为 ',
                                                    )}
                                                </span>{' '}
                                                <a>
                                                    {byteToGB(
                                                        overviewData?.maxCount,
                                                    )}
                                                </a>
                                                {' GB, '}
                                                <span>
                                                    {i18n.t(
                                                        'message',
                                                        '平均流量使用 ',
                                                    )}
                                                </span>
                                                <a>
                                                    {' '}
                                                    {byteToGB(
                                                        overviewData?.avgCount,
                                                    )}
                                                </a>{' '}
                                                {' GB'}
                                                {openControl && (
                                                    <span>
                                                        {', '}
                                                        {i18n.t(
                                                            'message',
                                                            '流量使用超额设备 {count} 个',
                                                            {
                                                                count: (
                                                                    <a>
                                                                        {' '}
                                                                        {
                                                                            overviewData?.excessCount
                                                                        }{' '}
                                                                    </a>
                                                                ),
                                                            },
                                                        )}
                                                    </span>
                                                )}
                                            </>
                                        }
                                    />
                                    <Table
                                        className={cn(
                                            {
                                                'flow-data-list-table-scroll':
                                                    !showSticky,
                                            },
                                            'flow-data-table',
                                        )}
                                        scroll={{
                                            x: '100%',
                                            y: isAbroadStyle ? 380 : 480,
                                        }}
                                        columns={tableColumns}
                                        dataSource={data}
                                        onChange={handleTableChange}
                                        sticky={{
                                            offsetScroll: PAGINATION_HEIGHT,
                                            offsetHeader: calcHeight(), // 距离container顶部的高度
                                            getContainer: () => {
                                                return document.querySelector(
                                                    '#root',
                                                ) as HTMLElement;
                                            },
                                        }}
                                    />
                                </>
                            )}
                        />
                    </div>
                        </RspDrawerTemplate.Right>
                    </RspDrawerTemplate>
                </div>
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
};

export default FlowManage;
