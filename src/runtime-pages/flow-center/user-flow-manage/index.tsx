import { getUserFlowPage, getUserFlowStatistics } from '@/service/flow-center';

import { DatePicker, Form, Input, Table, Tooltip, message } from '@streamax/poppy';
import { IconExport } from '@streamax/poppy-icons';
import {
    getAppGlobalData,
    i18n,
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
    FleetTreeSelect,
    useUrlSearchStore,
    FlOW_BU_TYPE,
} from '@base-app/runtime-lib';
import { PageBreadcrumbLayout, PageCardLayout } from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import type { GetDataSourceParams } from '@streamax/starry-components/lib/list-data-container';
import moment from 'moment';
import { useRef, useState } from 'react';
import { getTotalUnit, transformTotal } from '../Common';
import { disabledFutureMonths } from '@/utils/commonFun';
import './index.scope.less';
import { byteToGB } from '@/utils/flow';
import { isNil, omit } from 'lodash';
import InfoBack from '@/components/InfoBack';
import { exportExcel } from '@/service/import-export';
import { useDebounceFn } from 'ahooks';
import { getUserInfoConfig } from '@/service/tenant';
import { useAsyncEffect, useGetState } from '@streamax/hooks';
import { userFlowManageExportConfig } from '../constant';
import { COMPONENT_ID, dimensionGroups } from '../dimensionPoolModule';
import { dateFormatWithGlobalTimeFormat } from '@/utils/time';

const { MonthPicker } = DatePicker;
export interface Vehicle {
    fId: string;
    vId: string;
    vNo: string;
    vOnlineNumber: 1 | 0;
    vStates: string[];
    deviceList?: Record<string, any>[];
    driverList: Record<string, any>[];
    gps: {
        lat: number;
        lng: number;
        ang: number;
    };
    createTime: number;
}

interface ReqParams {
    month: any;
    pageSize: number;
    page: number;
    fleetIds?: string;
    includeSubFleet?: string;
    complexSort?: string;
}

const UserFlowManage = () => {
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');
    const [belongToIsOpen, setBelongToIsOpen, getBelongToIsOpen] =
        useGetState<boolean>(false);
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const {
        pageSize,
        page,
        fleetIds,
        includeSubFleet,
        complexSort,
        month,
        ...formQuery
    } = searchStore.get();
    const [useFlowData, setUseFlowData] = useState({
        totalFlow: 0,
        averageFlow: 0,
    });
    const tableRef = useRef<any>();
    const [lastQueryParams, setLastQueryParams] = useState<ReqParams>({});
    // 获取列表维度组
    const listDimensions = dimensionGroups[COMPONENT_ID.USER_FLOW_LIST];

    useAsyncEffect(async () => {
        form.setFieldsValue({
            ...formQuery,
            pageSize,
            page,
            companyIds: {
                fleetIds,
                includeSubFleet: includeSubFleet ? Number(includeSubFleet) : 1,
            },
            complexSort,
            month: month
                ? moment(month).startOf('month')
                : moment().startOf('month'),
        });
        await getTenantPasswordConfig();
        tableRef.current.loadDataSource({complexSort});
    }, []);
    // 查询租户密码配置
    const getTenantPasswordConfig = async () => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            keys: 'tenant.user.fleet.config',
        };
        try {
            const userConfigInfo = await getUserInfoConfig(params); // 无值时返回 ：{}
            // 车组配置参数
            const { belongToIsOpen } = JSON.parse(
                userConfigInfo?.['tenant.user.fleet.config'] || '{}',
            );
            setBelongToIsOpen(!!belongToIsOpen);
        } catch (error) {
            console.error(error);
        }
    };
    const getDefaultSortOrder = (columnsKey: string) => {
        if (complexSort && complexSort.indexOf(columnsKey) > -1) {
            return complexSort.indexOf('desc') > -1 ? 'descend' : 'ascend';
        }
        return null;
    };
    const columns: any = [
        {
            title: i18n.t('name', '用户名称'),
            dataIndex: 'userName',
            ellipsis: true,
            fixed: 'left',
            width: 200,
            render: (text: any, record: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Link
                            to={`/flow/user-flow-manage/user-flow-detail?userId=${
                                record.userId
                            }&selectTime=${lastQueryParams.month}&detailTitle=${
                                record.userName || '-'
                            }`}
                        >
                            {record.userName || '-'}
                        </Link>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        belongToIsOpen && {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            render: (text: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '月消耗流量'),
            dataIndex: 'monthTotal',
            sorter: true,
            defaultSortOrder: getDefaultSortOrder('monthTotal'),
            showSorterTooltip: false,
            render: (text: number, record: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {transformTotal(text).toFixed(2) + ' ' +
                            getTotalUnit(text)}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '查看直通消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_LIVE,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_LIVE),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
        {
            title: i18n.t('name', '回放点播消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_PLAYBACK,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_PLAYBACK),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
        {
            title: i18n.t('name', '视频库点播消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_VIDEO,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_VIDEO),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
        {
            title: i18n.t('name', '证据点播消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_EVIDENCE,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_EVIDENCE),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
        {
            title: i18n.t('name', '对讲消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_INTERCOM,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_INTERCOM),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
        {
            title: i18n.t('name', '监听消耗流量'),
            dataIndex: FlOW_BU_TYPE.STARRY_MONITOR,
            defaultSortOrder: getDefaultSortOrder(FlOW_BU_TYPE.STARRY_MONITOR),
            ellipsis: true,
            sorter: true,
            showSorterTooltip: false,
        },
    ];
    const customColumns = columns.filter((item) => item);
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(customColumns, {
            disabledKeys: ['userName'],
            storageKey: '@base:@page:user.flow.center.manage',
        });

    const fetchList = async (params: GetDataSourceParams) => {
        const { month, companyIds, userName, ...rest } = params;
        const { includeSubFleet, fleetIds } = companyIds || {};
        const reqParams: any = rest;
        const searchMonth = month?.format('YYYY-MM');
        reqParams.month = searchMonth;
        reqParams.userName = userName?.trim();

        if (!isNil(includeSubFleet)) {
            reqParams.includeSubFleet = includeSubFleet;
        }
        searchStore.set({
            ...reqParams,
            includeSubFleet: companyIds?.includeSubFleet,
            fleetIds: companyIds?.fleetIds
        });
        if (fleetIds) {
            reqParams.fleetIdList = [fleetIds];
        }
        getTotalFlow(reqParams);
        setLastQueryParams(reqParams);
        const { list, ...listRest } = await getUserFlowPage(reqParams);
        // 格式化数据，直接展示
        const formateList = list.map((item) => {
            const flowTypeObject: any = {};
            item.detail.forEach((typeItem) => {
                flowTypeObject[typeItem.type] =
                    transformTotal(typeItem.flowTotal).toFixed(2) + ' ' +
                    getTotalUnit(typeItem.flowTotal);
            });
            // 填充没有数据类型的情况
            listDimensions.forEach((item) => {
                if (!flowTypeObject[item]) {
                    flowTypeObject[item] = '-';
                }
            });
            return {
                ...item,
                ...flowTypeObject,
            };
        });
        return {
            ...listRest,
            list: formateList,
        };
    };
    const getTotalFlow = async (params: GetDataSourceParams) => {
        const newParams = omit(params, ['page', 'pageSize', 'complexSort']);
        const res = await getUserFlowStatistics(newParams);
        setUseFlowData(res);
    };
    const queryItems: any = [
        belongToIsOpen && {
            label: i18n.t('name', '归属车组'),
            name: 'companyIds',
            field: FleetTreeSelect,
            fieldProps: {
                placeholder: i18n.t('message', '请选择归属车组'),
            },
        },
        {
            label: i18n.t('name', '月份'),
            name: 'month',
            field: MonthPicker,
            itemProps: {
                initialValue: moment().startOf('month'),
            },
            fieldProps: {
                allowClear: false,
                disabledDate: disabledFutureMonths,
            },
        },
        {
            label: i18n.t('name', '用户名称'),
            name: 'userName',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入用户名称'),
                maxLength: 50,
            },
        },
    ];
    const customQueryItems = queryItems.filter((item) => item);
    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        // @ts-ignore
        const { order, field } = sorter || {};
        let complexSort = undefined;
        if (order) {
            const sort = order.replace('end', '');
            complexSort = `${field} ${sort}`;
        }

        // todo 历史的这种交互都存在问题，得ListDataContainer出方案解决
        tableRef.current.loadDataSource({
            // ...lastQueryParams,
            // month: moment(lastQueryParams.month),
            complexSort,
        });
    };
    // 导出数据
    const _exportData = () => {
        let headersArr = [
            {
                columnName: 'userName',
                title: i18n.t('name', '用户名称'),
            },
            belongToIsOpen && {
                columnName: 'fleetName',
                title: i18n.t('name', '归属车组'),
            },
            {
                columnName: 'monthTotal',
                title: i18n.t('name', '月消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_LIVE,
                title: i18n.t('name', '查看直通消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_PLAYBACK,
                title: i18n.t('name', '回放点播消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_VIDEO,
                title: i18n.t('name', '视频库点播消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_EVIDENCE,
                title: i18n.t('name', '证据点播消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_INTERCOM,
                title: i18n.t('name', '对讲消耗流量'),
            },
            {
                columnName: FlOW_BU_TYPE.STARRY_MONITOR,
                title: i18n.t('name', '监听消耗流量'),
            },
        ];
        // @ts-ignore
        headersArr = headersArr
            .filter((item) => item)
            .map((item, index) => {
                return {
                    ...item,
                    index: index,
                };
            });
        const exportQueryParams = { ...lastQueryParams };
        delete exportQueryParams.page;
        delete exportQueryParams.pageSize;
        const currentMonth = dateFormatWithGlobalTimeFormat(exportQueryParams.month, 'YYYY-MM')?.replace("-",'');
        const sheetArr = [
            {
                sheetName: i18n.t('name', '用户流量统计') + '-' + currentMonth,
                excelHeaders: headersArr,
                queryParam: {
                    param: {
                        ...exportQueryParams,
                    },
                },
            },
        ];
        exportExcel({
            ...userFlowManageExportConfig.userManageList,
            isAsync: true,
            excelType: 'XLSX',
            fileName:
                i18n.t('name', '用户流量统计') +
                `-${currentMonth}-${moment().unix()}`,
            sheetQueryParams: sheetArr,
        }).then(() => {
            message.success(
                i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
            );
        });
    };
    const { run: exportData } = useDebounceFn(_exportData, {
        wait: 500,
    });

    return (
        <PageBreadcrumbLayout>
            <PageCardLayout>
                <div className="user-flow-data-manage">
                    <ListDataContainer
                        getDataSource={fetchList}
                        queryForm={{
                            items: customQueryItems,
                            form,
                        }}
                        ref={tableRef}
                        loadDataSourceOnMount={false}
                        pagination={{
                            defaultCurrent: Number(page) || 1,
                            defaultPageSize: Number(pageSize) || 20,
                        }}
                        toolbar={{
                            extraRight: [
                                <Tooltip
                                    title={i18n.t('action', '导出')}
                                    placement="top"
                                    key="action-top"
                                >
                                    <span className='export-button-icon'>
                                        <StarryAbroadIcon>
                                            <IconExport onClick={exportData} />
                                        </StarryAbroadIcon>
                                    </span>
                                </Tooltip>,
                            ],
                            // @ts-ignore
                            extraIconBtns: [
                                <TableColumnSetting
                                    key="setting"
                                    {...tableColumnSettingProps}
                                />,
                            ],
                        }}
                        listRender={(data) => (
                            <>
                                <InfoBack
                                    className="custom-tip-container"
                                    title={
                                        <>
                                            <div>
                                                {i18n.t(
                                                    'message',
                                                    '{month} 月用户使用总流量为 {total} {unit}，平均流量使用 {average} {unit}',
                                                    {
                                                        month: (
                                                            <a>
                                                                {Number(
                                                                    month?.split(
                                                                        '-',
                                                                    )[1],
                                                                )}
                                                            </a>
                                                        ),
                                                        total: (
                                                            <a>
                                                                {byteToGB(
                                                                    useFlowData.totalFlow,
                                                                )}
                                                            </a>
                                                        ),
                                                        average: (
                                                            <a>
                                                                {byteToGB(
                                                                    useFlowData.averageFlow,
                                                                )}
                                                            </a>
                                                        ),
                                                        unit: 'GB',
                                                    },
                                                )}
                                            </div>
                                        </>
                                    }
                                />
                                <Table
                                    scroll={{
                                        x: '100%',
                                    }}
                                    columns={tableColumns}
                                    dataSource={data}
                                    onChange={handleTableChange}
                                />
                            </>
                        )}
                    />
                </div>
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
};

export default UserFlowManage;
