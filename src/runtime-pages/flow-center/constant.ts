/*
 * @LastEditTime: 2025-04-11 14:15:20
 */
import { i18n } from "@base-app/runtime-lib";

export const flowStatusOptions = [
    {
        label: i18n.t('name', '超额'),
        value: 0,
    },
    {
        label: i18n.t('name', '正常'),
        value: 1,
    },
];
export const handleStatusOptions = [
    {
        label: i18n.t('name', '已处理'),
        value: 1,
    },
    {
        label: i18n.t('name', '未处理'),
        value: 0,
    },
];

export const handleMaps = {
    '0': i18n.t('name', '未处理'),
    '1': i18n.t('name', '已处理'),
};
export const handleStatusColors = {
    '0': '#FAAD14',
    '1': '#52C41A',
};
export const controlColors = {
    '0': '#00000040',
    '1': '#52C41A',
};
// 用户流量导出的配置
export const userFlowManageExportConfig = {
    userManageList: {
        taskType: 1528,
        serviceName: 'base-flow-service',
        executorHandler: 'userFlowListExcelExport',
    },
    userMangeDetail: {
        taskType: 1529,
        serviceName: 'base-flow-service',
        executorHandler: 'userFlowDetailExcelExport',
    }
};