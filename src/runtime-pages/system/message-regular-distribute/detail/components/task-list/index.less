@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.device-regular-upload-detail-list{
    display: flex;
    position: relative;
    .rsp-drawer-title{
        font-size: 20px;
        font-weight: 700;
        line-height: 28px;
        margin-right: 8px;
        color: @starry-text-color-primary;
    }
    .left-tab-pro {
        height: 100%;
        margin-left: -24px;
        .poppy-tabs {
            height: 100%;
        }
        .poppy-tabs-tab {
            padding: 4px 16px;
        }
        .poppy-tabs-tab-btn {
            max-width: 250px;
            overflow: hidden;
            font-size: 14px;
            white-space: nowrap;
            text-align: left;
            text-overflow: ellipsis;
            word-break: keep-all;
        }
    }
    .right-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        height: inherit;
        .poppy-tabs-nav-wrap{
            display: none;
        }
        .right-content-tabs{
            overflow: visible;
        }
    }
}
.device-regular-upload-detail-drawer{
    .poppy-tabs-content-holder{
        display: none;
    }
    .left-tab-pro{
        height: 100%;
        .poppy-tabs{
            height: 100%;
            .poppy-tabs-nav{
                border-left: 1px solid @starry-border-level-1-color;
                margin-bottom: 0px;
            }
        }
    }
}
