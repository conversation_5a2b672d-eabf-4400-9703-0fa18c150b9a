/*
 * @LastEditTime: 2023-03-10 16:34:11
 */
export enum TurnPage {
    prev = 'prev',
    next = 'next',
    not = '',
}
export type RecordItem = {
    recordId: string;
    driverName: string | undefined;
    hasPrivilege?: 0 | 1 | null; //是否有该司机权限1是有权限 0是无权限
    realDriverId: string; //
    updateUserName?: string; //处理人
    updateTime?: number; //
    handleTime?: number; //处理时间
    handleStatus?: 0 | 1; //是否已处理
};

/**
 * 图片校验成功编码
 */
export const PICTURE_SUCCESS_CODE = '3120000';

/**
 * 图片质量40
 */
export const PHOTO_QUALITY_SCORE = 40;
