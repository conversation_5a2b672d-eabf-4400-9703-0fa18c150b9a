@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.region-vehicle-find-car-icon {
    .vehicle-wrapper {
        position: relative;
        .vehicle-icon {
            position: relative;
            width: 28px;
            height: 68px;
            background-size: 100%;
            img {
                position: absolute;
                display: inline-block;
                transform-origin: 50% 50%;
            }
            .bg-border {
                top: 0;
                left: 0;
                display: inline-block;
                height: 72px;
                margin-top: -2px;
                margin-left: -2px;
                font-style: normal;
                line-height: 0;
                text-align: center;
                text-transform: none;
                vertical-align: -0.125em;
                transform-origin: 50% 50%;
                text-rendering: optimizeLegibility;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;

                .border-path {
                    width: 3px;
                    fill: #597ef7;
                    // fill: @primary-color;
                }
                .no-border-path {
                    display: none;
                }
            }
            .selected-bg-border {
                position: absolute;
                top: 50%;
                left: 50%;
                height: 90px;
                margin-top: unset;
                margin-left: unset;
            }
            .bg {
                position: absolute;
                top: 0;
                left: 0;
                display: inline-block;
                height: 68px;
                color: inherit;
                font-style: normal;
                line-height: 0;
                text-align: center;
                text-transform: none;
                vertical-align: -0.125em;
                transform-origin: 50% 50%;
                text-rendering: optimizeLegibility;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            .selected-bg {
                top: 50%;
                left: 50%;
                display: unset;
                height: 76px;
            }
            .car {
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                transform: translate(-50%, -50%);
            }
            .selected-car {
                width: 18px;
                height: 18px;
            }
            .angle-border {
                top: 50%;
                left: 50%;
                width: 28px;
            }
            .selected-angle-border {
                width: 30px;
            }
        }
        .vehicle-name {
            position: absolute;
            bottom: -20px;
            left: 50%;
            max-width: 80px;
            padding: 0 5px;
            overflow: hidden;
            color: #fff;
            font-size: 12px;
            line-height: 20px;
            white-space: nowrap;
            text-align: center;
            text-overflow: ellipsis;
            background: #0b2267;
            border-radius: 10px;
            box-shadow: 1px 1px 4px 1px rgba(0, 0, 0, 0.3);
            transform: translateX(-50%);
        }
        .selected-vehicle-name {
            // bottom: -33px;
            max-width: 120px;
            font-weight: 600;
            font-size: 12px;
            line-height: 20px;
            background: #597ef7;
            border-radius: 14px;
            box-shadow: 1px 1px 4px 1px rgba(0, 0, 0, 0.3);
        }
    }
}
#region-vehicle-find-map::abroad{
    border-radius: @border-radius-12;
}
.region-vehicle-find-map-no-mouse{
    cursor: none;
}
