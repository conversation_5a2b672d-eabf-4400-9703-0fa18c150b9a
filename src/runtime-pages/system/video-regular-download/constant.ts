import { fetchParameterDetail } from '@/service/parameter';
import { i18n } from '@base-app/runtime-lib';
export const ENABLE = 1;
export const DISABLE = 0;

// 获取视频剪辑最大时长租户参数
const getStreamLengthParams = async () => {
    try {
        const parameterData = await fetchParameterDetail(
            {
                parameterKey: 'VIDEO.CLIP.LENGTH',
            },
            false,
        );
        if (parameterData) {
            return parameterData.parameterValue;
        }
        return '';
    } catch (error) {
        // eslint-disable-next-line no-console
        console.log('不存在参数VIDEO.CLIP.LENGTH', error);
        return '';
    }
};

const getParams = async () => {
    const clipLength = await getStreamLengthParams();
    let clipLengthArr = clipLength.split(';');

    // 校验参数是否符合规范
    const isCurrent = () => {
        // 是否是三个参数
        if (clipLengthArr?.length != 3) {
            return false;
        }
        for (let key = 0; key < clipLengthArr.length; key++) {
            // 非正整数
            if (
                clipLengthArr[key] % 1 != 0 ||
                Number(clipLengthArr[key]) < 0 ||
                Number(clipLengthArr[key]) > 60
            ) {
                return false;
            }
            //三个参数不是递增的
            if (
                clipLengthArr?.[key] &&
                clipLengthArr?.[key + 1] &&
                Number(clipLengthArr?.[key]) > Number(clipLengthArr?.[key + 1])
            ) {
                return false;
            }
        }
        return true;
    };
    if (!isCurrent()) {
        clipLengthArr = ['10', '30', '60'];
    }
    return clipLengthArr;
};

export const STATUS_OPTIONS = [
    {
        label: i18n.t('name', '停用'),
        value: '0',
    },
    {
        label: i18n.t('name', '启用'),
        value: '1',
    },
];
export const StatusMaps = {
    0: i18n.t('name', '停用'),
    1: i18n.t('name', '启用'),
};
export const CycleType = {
    '1': i18n.t('name', '每日'),
    '2': i18n.t('name', '每周'),
    '3': i18n.t('name', '每月'),
};
export enum StreamTypeEnum {
    'MAJOR' = 1, // 主码流 - 高清
    'MINOR', // 子码流 - 标清
    'PICK_FRAME', // 抽帧
}
export const FILETYPE = {
    VIDEO: '1',
    BLANK: '2,3,4',
    list: [
        {
            value: '1',
            label: i18n.t('name', '录像'),
        },
        {
            value: '2,3,4',
            label: i18n.t('name', '黑匣子'),
        },
    ],
};
export const INTERNETTYPE = {
    list: [
        {
            value: 0,
            label: i18n.t('name', '有线网络'),
        },
        {
            value: 1,
            label: i18n.t('name', 'WIFI'),
        },
        {
            value: 2,
            label: i18n.t('name', '3G/4G/5G'),
        },
    ],
};

export const VELOCITYMODE = {
    LOW: 0,
    HIGH: 1,
    list: [
        {
            value: 0,
            label: i18n.t('name', '低速下载'),
            key: 'LOW', 
        },
        {
            value: 1,
            label: i18n.t('name', '高速下载'),
            key: 'HIGH', 
        },
    ],
};
export const VIDEO_TIME_COUNT = 5;

// 绘制颜色类型
export const DRAW_COLOR = {
    hasVideo: '#414B6B',
    uploaded: '#52C41A',
    alarm: '#FF4D4F',
    pickFrame: '#744DE0',
    major: '#F98742',
    minor: '#0D6EDE',
};

export const getStreamTypeMap = async (): Promise<Record<StreamTypeEnum, StreamTypeItem>> => {
    const clipLengthArr = await getParams();
    return {
        [StreamTypeEnum.MAJOR]: {
            label: i18n.t('name', '高清'),
            value: StreamTypeEnum.MAJOR,
            color: DRAW_COLOR.major,
            key: 'MAJOR',
            max: clipLengthArr?.[0] || 10,
        },
        [StreamTypeEnum.MINOR]: {
            label: i18n.t('name', '标清'),
            value: StreamTypeEnum.MINOR,
            color: DRAW_COLOR.minor,
            key: 'MINOR',
            max: clipLengthArr?.[1] || 30,
        },
        [StreamTypeEnum.PICK_FRAME]: {
            label: i18n.t('name', '延时录像'),
            value: StreamTypeEnum.PICK_FRAME,
            color: DRAW_COLOR.pickFrame,
            key: 'PICK_FRAME',
            max: clipLengthArr?.[2] || 60,
        },
    };
};

// 设备能力 对应字段能力支持与否枚举，对应字段为1-支持，为0或者null或者无该字段为不支持
export enum DeviceAbilityEnum {
    NoSupport,
    Support,
}
export const defaultStreamTypeMap = {
    [StreamTypeEnum.MAJOR]: {
        label: i18n.t('name', '高清'),
        value: StreamTypeEnum.MAJOR,
        color: DRAW_COLOR.major,
        key: 'MAJOR',
        max: 10,
    },
    [StreamTypeEnum.MINOR]: {
        label: i18n.t('name', '标清'),
        value: StreamTypeEnum.MINOR,
        color: DRAW_COLOR.minor,
        key: 'MINOR',
        max: 30,
    },
    [StreamTypeEnum.PICK_FRAME]: {
        label: i18n.t('name', '延时录像'),
        value: StreamTypeEnum.PICK_FRAME,
        color: DRAW_COLOR.pickFrame,
        key: 'PICK_FRAME',
        max: 60,
    },
};

export const getStreamTypeTimeNode = async () => {
    const clipLengthArr = await getParams();
    const typeA = [1, 2, 3, 5, 8, 10];
    const typeB = [1, 3, 5, 10, 20, 30];
    const typeC = [1, 3, 5, 10, 20, 30, 45, 60];
    const nodeArr = clipLengthArr.map((item: number) => {
        if (item <= 10) {
            return typeA.filter((defaultItem) => defaultItem < item).concat(item);
        } else if (item > 10 && item <= 30) {
            return typeB.filter((defaultItem) => defaultItem < item).concat(item);
        } else if (item > 30 && item <= 60) {
            return typeC.filter((defaultItem) => defaultItem < item).concat(item);
        } else {
            return typeC;
        }
    });

    return {
        MAJOR: nodeArr?.[0],
        MINOR: nodeArr?.[1],
        PICK_FRAME: nodeArr?.[2],
    };
};
export const defaultStreamTypeTimeNode = {
    //不同视频类型对应的时长快捷节点
    MAJOR: [1, 2, 3, 5, 8, 10],
    MINOR: [1, 3, 5, 10, 20, 30],
    PICK_FRAME: [1, 3, 5, 10, 20, 30, 45, 60],
};

export interface StreamTypeItem {
    label: string;
    value: StreamTypeEnum;
    color: string;
    key: string;
    max: number;
}
