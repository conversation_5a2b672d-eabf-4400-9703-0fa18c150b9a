import {
    Badge,
    Button,
    Form,
    Input,
    Select,
    Space,
    Switch,
    Table,
    Tooltip,
    message,
} from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import {
    IconAddFill,
    IconDeleteFill,
    IcListEditFill,
    IconRequestFill,
} from '@streamax/poppy-icons';
import {
    Auth,
    i18n,
    useUrlSearchStore,
    utils,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import {
    PageBreadcrumbLayout,
    PageCardLayout,
    StarryModal,
    Action,
} from '@base-app/runtime-lib';
import { Link, useHistory } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import type { Moment } from 'moment';
import React, { useRef, useState, useEffect } from 'react';
import { CycleType, ENABLE, STATUS_OPTIONS } from './constant';
import DateRange from '@/components/DateRange';
import {
    deleteCycleTask,
    changeCycleTaskStatus,
    getCycleTaskList,
} from '@/service/regular-task';
import type { ListBasePramsProps, CycleTaskItem } from '@/service/regular-task';
import { disabledAfterDate, getInitTimeRange, getPickerRanges,} from '@/utils/commonFun';
import './index.less';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import {
    Instances,
    ListPageQueryForm,
    ListPageTableBase,
} from '@/types/pageReuse/pageReuseBase';
import {
    getCustomItems,
    getCustomJsx,
    getTableIconBtns,
    runCustomFun,
} from '@/utils/pageReuse';
import { useUpdateEffect } from '@streamax/hooks';
import { TableProps } from '@streamax/poppy/lib/table';
interface TableListParams extends ListBasePramsProps {
    name: string;
    status: number;
    createTime: [Moment, Moment];
    complexSort?: string;
}
const {
    zeroTimeStampToFormatTime,
    timestampToZeroTimeStamp,
    getLocalMomentByZeroTimeStamp,
} = utils.formator;

/**列表页table定制复用 */
export type DeviceRegularUploadProps = ListPageTableBase &
    ListPageQueryForm &
    Instances;
/**end */

export default function DeviceRegularUpload(props: DeviceRegularUploadProps) {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        getColumnSetting,
    } = props;
    /**end */

    const [form] = Form.useForm();
    const tableRef = useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [sortOrder, setSortOrder] = useState<string>();
    const history = useHistory();
    const searchStore = useUrlSearchStore();

    useEffect(() => {
        const { page, pageSize, ...otherParams } = getSearchParams();

        form.setFieldsValue({
            ...otherParams,
        });
        tableRef.current.loadDataSource({
            page,
            pageSize,
            ...otherParams,
        });
    }, []);
    useEffect(() => {
        tableRef.current.loadDataSource({
            ...getSearchParams(),
            complexSort: sortOrder,
        });
    }, [sortOrder]);

    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedRowKeys);
    }, [selectedRowKeys]);

    runCustomFun(getInstances, {
        form,
        table: {
            ...tableRef.current,
            reload: tableRef.current?.loadDataSource,
        },
    });

    const getSearchParams = () => {
        const searchStoreData = searchStore.get();
        const {
            page = 1,
            pageSize = 20,
            createTimeStart,
            createTimeEnd,
            name,
        } = searchStoreData;
        let createTime;
        if (createTimeStart && createTimeEnd) {
            const momentStartTime =
                getLocalMomentByZeroTimeStamp(createTimeStart);
            const momentEndTime = getLocalMomentByZeroTimeStamp(createTimeEnd);
            createTime = [momentStartTime, momentEndTime];
        } else {
            createTime = undefined;
        }
        return {
            ...searchStoreData,
            page: Number(page),
            pageSize: Number(pageSize),
            name: name && decodeURIComponent(name),
            createTime,
        };
    };
    const getDataList = async (params: TableListParams) => {
        setSelectedRowKeys([]);
        const { name, createTime, complexSort } = params;
        const createTimeStart = createTime
            ? timestampToZeroTimeStamp(createTime?.[0])
            : null;
        const createTimeEnd = createTime
            ? timestampToZeroTimeStamp(createTime?.[1])
            : null;
        const reqParams: any = {
            ...params,
            name: name ? encodeURIComponent(name) : undefined,
            createTimeStart,
            createTimeEnd,
            cycleTaskType: 1, //任务类型
        };
        if (complexSort) {
            reqParams.complexSort = complexSort;
        }
        delete reqParams.createTime;
        searchStore.set(reqParams);

        if (injectSearchList) {
            const data = await injectSearchList(params);
            return data;
        }
        const res = await getCycleTaskList(reqParams);
        return res;
    };

    // 删除
    const handleDelete = (params?: CycleTaskItem) => {
        const content = params
            ? i18n.t('message', '确认要删除“{name}”视频定时下载任务吗？', { name: params.name })
            : i18n.t('message', '确认要删除已选任务名称的视频定时下载任务吗？');
        StarryModal.confirm({
            center: true,
            title: i18n.t('name', '确认删除'),
            content,
            icon: <IconRequestFill />,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: async () => {
                let taskIds: string[];
                if (params) {
                    taskIds = [params.id];
                } else {
                    taskIds = [...selectedRowKeys];
                }
                try {
                    const result = await deleteCycleTask({
                        ids: taskIds.join(),
                    });
                    if (result) {
                        tableRef.current?.loadDataSource();
                        setSelectedRowKeys([]);
                        message.success(i18n.t('message', '操作成功'));
                    } else {
                        message.error(i18n.t('message', '删除失败'));
                    }
                } catch (error) {
                    message.error(i18n.t('message', '删除失败'));
                }
            },
        });
    };
    // 停启用
    const handleAble = async (checked: boolean, record: CycleTaskItem) => {
        const title = checked
            ? i18n.t('name', '确认启用')
            : i18n.t('name', '确认停用');
        const content = checked
            ? i18n.t('message', '确认要启用“{taskName}”视频定时下载任务吗？', {
                  taskName: record.name,
              })
            : i18n.t('message', '确认要停用“{taskName}”视频定时下载任务吗？', {
                  taskName: record.name,
              });
        StarryModal.confirm({
            center: true,
            title,
            content,
            icon: <IconRequestFill />,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: async () => {
                const successMsg = checked
                    ? i18n.t('message', '启用成功')
                    : i18n.t('message', '停用成功');
                const errMsg = checked
                    ? i18n.t('message', '启用失败')
                    : i18n.t('message', '停用失败');
                const reqParams = {
                    id: record.id,
                    status: checked ? 1 : 0,
                };
                try {
                    const result = await changeCycleTaskStatus(reqParams);
                    if (result) {
                        tableRef.current?.loadDataSource();
                        message.success(successMsg);
                    } else {
                        message.error(errMsg);
                    }
                } catch (error) {
                    message.error(errMsg);
                }
            },
        });
    };
    // 创建时间升降序
    const handleTableChange = (
        _: any,
        filters: any,
        sorter: { field: string; order: string },
    ) => {
        let { order } = sorter;
        let sortStr: string;
        if (order) {
            order = (order ?? 'descend').substring(0, order.length - 3);
            sortStr = `orderBy createTime ${order}`;
        } else {
            sortStr = '';
        }
        setSortOrder(sortStr);
    };

    const queryItems: QueryFormProps['items'] | any[] = [
        {
            label: i18n.t('name', '任务名称'),
            name: 'name',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入任务名称'),
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '状态'),
            name: 'status',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择任务状态'),
                options: STATUS_OPTIONS,
            },
        },
        {
            label: i18n.t('name', '创建时间'),
            name: 'createTime',
            colSize: 2,
            field: DateRange,
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                    },
                    allowClear: true,
                    disabledDate: disabledAfterDate,
                        ranges: getPickerRangeTWM(),
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];

    const queryForm: QueryFormProps = {
        items: getCustomItems(getQueryForm, queryItems, undefined),
        form,
    };
    const columns: ColumnsType<any> = [
        {
            title: i18n.t('name', '任务名称'),
            dataIndex: 'name',
            ellipsis: { showTitle: false },
            render: (text: string, record: CycleTaskItem) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Action
                            code="@base:@page:system.video.regular.download@action:detail"
                            url="/system/video-regular-download/detail"
                            params={{
                                taskId: record.id,
                                taskName: record.name,
                                status: record.status,
                            }}
                            fellback={text}
                        >
                            {text}
                        </Action>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'status',
            render: (text: number, record: CycleTaskItem) => {
                const status = text === 1 ? 'success' : 'default';
                const showText =
                    text === 1
                        ? i18n.t('state', '启用')
                        : i18n.t('state', '停用');
                return (
                    <Space>
                        <Badge status={status} text={showText} />
                        <span style={{ display: 'flex' }}>
                            <Auth code="@base:@page:system.video.regular.download@action:able.enable">
                                <Switch
                                    onChange={(checked: boolean) =>
                                        handleAble(checked, record)
                                    }
                                    checked={text === ENABLE}
                                    size="small"
                                />
                            </Auth>
                        </span>
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '执行周期'),
            dataIndex: 'cycleType',
            render: (text: number) => {
                return text ? String(CycleType[String(text)]) : '-';
            },
        },
        {
            title: i18n.t('name', '创建人'),
            ellipsis: true,
            dataIndex: 'createUserName',
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            sorter: true,
            ellipsis: true,
            sortDirections: ['ascend', 'descend', null],
            defaultSortOrder: null,
            render: (text: number) => {
                return text ? zeroTimeStampToFormatTime(text) : '-';
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'action',
            width: 200,
            render: (_: any, record: CycleTaskItem) => {
                return (
                    <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Auth code="@base:@page:system.video.regular.download@action:edit">
                            <Link
                                to={`/system/video-regular-download/edit?taskId=${record.id}`}
                            >
                                <Tooltip title={i18n.t('action', '编辑')}>
                                    <IcListEditFill />
                                </Tooltip>
                            </Link>
                        </Auth>
                        {record.status !== ENABLE && (
                            <Auth code="@base:@page:system.video.regular.download@action:batch.del">
                                <span
                                    className="delete-item"
                                    onClick={() => handleDelete(record)}
                                >
                                    <Tooltip title={i18n.t('action', '删除')}>
                                        <IconDeleteFill />
                                    </Tooltip>
                                </span>
                            </Auth>
                        )}
                    </Space>
                );
            },
        },
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: (selectedKeys: any) => {
            setSelectedRowKeys(selectedKeys);
        },
        getCheckboxProps: (record: CycleTaskItem) => ({
            disabled: record.status == ENABLE,
        }),
    };

    const getRowSelection = () => {
        const rowSelection: TableProps['rowSelection'] = {
            selectedRowKeys,
            onChange: (selectedKeys: any) => {
                setSelectedRowKeys(selectedKeys);
            },
            getCheckboxProps: (record: CycleTaskItem) => ({
                disabled: record.status == ENABLE,
            }),
        };
        return rowSelection;
    };

    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(columns, {
            storageKey: 'video.regular.download',
            disabledKeys: ['name', 'action'],
            ...getColumnSetting?.(),
        });

    const toolBarLeftBtn = () => {
        return (
            <>
                <Action
                    code="@base:@page:system.video.regular.download@action:add"
                    url="/system/video-regular-download/add"
                    fellback={i18n.t('name', '添加')}
                >
                    <Button type="primary">
                        <IconAddFill />
                        {i18n.t('name', '添加')}
                    </Button>
                </Action>
                <Auth code="@base:@page:system.video.regular.download@action:batch.del">
                    <Button
                        onClick={() => handleDelete()}
                        disabled={!selectedRowKeys.length}
                    >
                        <IconDeleteFill />
                        {i18n.t('name', '批量删除')}
                    </Button>
                </Auth>
            </>
        );
    };

    return (
        <PageBreadcrumbLayout className="system-device-regular-upload">
            <PageCardLayout>
                <ListDataContainer
                    getDataSource={getDataList}
                    queryForm={queryForm}
                    loadDataSourceOnMount={false}
                    toolbar={{
                        extraLeft: (
                            <Space>
                                {getCustomJsx(getTableLeftRender, [
                                    toolBarLeftBtn(),
                                ])}
                            </Space>
                        ),
                        // @ts-ignore
                        extraIconBtns: getTableIconBtns(getIconBtns, [
                            <TableColumnSetting
                                key="setting"
                                {...tableColumnSettingProps}
                            />,
                        ]),
                    }}
                    ref={tableRef}
                    listRender={(data) => {
                        return (
                            <Table
                                columns={getCustomItems(
                                    getColumns,
                                    tableColumns,
                                )}
                                rowSelection={getRowSelection()}
                                dataSource={data}
                                // pagination={false}
                                rowKey="id"
                                bordered="around"
                                onChange={handleTableChange}
                            />
                        );
                    }}
                />
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
}
