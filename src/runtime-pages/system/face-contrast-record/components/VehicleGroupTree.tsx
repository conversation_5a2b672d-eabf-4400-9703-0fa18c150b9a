import React, { useState, useEffect, useRef } from 'react';
import { Tree, Spin, Empty } from '@streamax/poppy';
import { cloneDeep } from 'lodash';
import MultiSearch from '@/components/MultiSearch';
import type { SearchModuleNameType } from '@/components/MultiSearch';
import { i18n } from '@base-app/runtime-lib';
import type { Fleet, Vehicle } from '../useTreeData';
import type { DataNode } from 'antd/lib/tree';
import { ReactComponent as Nodata } from '@/assets/icons/black_state_nodata.svg';
import './VehicleGroupTree.less';
import { CaretDownOutlined } from '@ant-design/icons';

interface VehicleTreeProps {
    onSelect: (vehicleId: string | undefined) => void;
    treeData: DataNode[];
    loaded: boolean;
    fleetList: Fleet[];
    vehicleList: Vehicle[];
    loadFleets: (fIds: string[]) => Promise<any>;
    loadedKeys: string[];
    isExpandFirstGroup?: boolean; // 展开一级车组
    unGroupFleetId?: string; // 未分组车组id
}

interface SetSelectNodeProps {
    type?: 'fleet' | 'vehicle';
    fleetId?: number | string;
    vehicleId?: number | string;
}

interface OffsetInfoType {
    top: number;
    left: number;
    [params: string]: any;
}

// 车组前缀，避免车组车辆id冲突
export const fleetPrefix = 'fleet-';

function getOffsetInfo(element: HTMLElement): OffsetInfoType {
    const ret = { top: 0, left: 0, width: 0, height: 0 };
    if (!element) {
        return ret;
    }

    if (!element.getClientRects().length) {
        return ret;
    }

    const rect = element.getBoundingClientRect();

    if (rect.width || rect.height) {
        const doc = element.ownerDocument;
        const docElem = doc.documentElement;
        return {
            top: Math.round(rect.top - docElem.clientTop),
            left: Math.round(rect.left - docElem.clientLeft),
            width: Math.round(rect.width),
            height: Math.round(rect.height),
        };
    }

    return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: 0,
        height: 0,
    };
}

const VehicleTree = (props: VehicleTreeProps) => {
    const {
        onSelect = () => {},
        fleetList,
        loaded,
        vehicleList,
        isExpandFirstGroup = false,
        unGroupFleetId = '0',
        treeData,
        loadFleets,
        loadedKeys,
    } = props;

    const treeRef = useRef<any>(null);

    const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [containerHeight, setContainerHeight] = useState(0);
    const [isInitDataFinish, setIsInitDataFinish] = useState(false); // 初始化数据是否完成
    const [selectQueue, setSelectQueue] = useState<any[]>([]); // 待选择队列

    useEffect(() => {
        if (treeData.length > 0) {
            setIsInitDataFinish(true);
        }
    }, [treeData]);

    useEffect(() => {
        if (isExpandFirstGroup && isInitDataFinish) {
            // 展开一级车组
            setExpandedKeys(treeData.map((key: any) => `fleet-${key.fId}`));
        }
    }, [isExpandFirstGroup, isInitDataFinish]);

    useEffect(() => {
        if (isInitDataFinish && selectQueue.length > 0) {
            selectQueue.forEach((item: any) => {
                setSelectedNode(item);
            });
        }
    }, [isInitDataFinish]);

    useEffect(() => {
        const container = document.querySelector('.vehicle-group-tree-body');
        // @ts-ignore
        setContainerHeight(getOffsetInfo(container).height);
    }, []);

    // 设置选中节点
    function setSelectedNode(data: SetSelectNodeProps): void {
        if (!isInitDataFinish) {
            setSelectQueue((list: any[]) => {
                list.push(data);
                return list;
            });
            return;
        }
        const { type = 'vehicle', fleetId, vehicleId } = data;
        if (type === 'fleet') {
            selectFleetInTree({
                fleetId,
            });
        } else {
            selectVehicleInTree({
                vehicleId,
                fleetList: [{ fleetId }],
            });
        }
    }

    // 定位到指定车组或车辆
    function positionVehicle(key: string) {
        treeRef.current?.scrollTo({ key, offset: 30 });
    }

    // 加载车组下的车辆数据
    async function loadVehicleData(node: any) {
        return new Promise<void>((resolve) => {
            const { key, children } = node;
            const id = key.replace(fleetPrefix, '');
            if ((children && children.length > 0) || loadedKeys.includes(key)) {
                resolve();
                return;
            }
            loadFleets([id]).then(() => {
                resolve();
            });
        });
    }

    // 选择搜索结果中的车辆后，在树中进行选中操作
    async function selectVehicleInTree(data: any) {
        const { vehicleId, fleetList: dataFleetList = [] } = data;
        const fleetId = dataFleetList[0]?.fleetId || unGroupFleetId;
        const key = `vehicle-${fleetId}-${vehicleId}`;
        const fleet = fleetList.find((i: any) => i.fId === fleetId);
        // 未分组的车辆不需要加载车组、车辆数据
        fleet && (await loadFleets(fleet?.path));
        setExpandedKeys((expandedKeys: any[]) => {
            const newKeys = expandedKeys.concat(fleet?.path.map((fId: any) => `fleet-${fId}`));
            return [...new Set(newKeys)];
        });
        setSelectedKeys([key]);
        onSelect?.(vehicleId);
        positionVehicle(key);
    }

    // 设置选择车组
    async function selectFleetInTree(data: any) {
        const { fleetId } = data;
        const key = `fleet-${fleetId}`;
        const fleet = fleetList.find((i: any) => i.fId === fleetId);
        // 有可能查询出的车组是在页面进入后新增加的。此时  fleetList 里面没有此车组。此情况暂不处理
        if (fleet) {
            await loadFleets(fleet?.path);
            setExpandedKeys((expandedKeys: any[]) => {
                const newKeys = expandedKeys.concat(fleet?.path.map((fId: any) => `fleet-${fId}`));
                return [...new Set(newKeys)];
            });
            setSelectedKeys([key]);
            positionVehicle(key);
        }
    }

    const juageVehicleExit = (vehicleId: string): boolean => {
        let isExit = true;
        const tempVehicleData = (vehicleList || []).map((item: any) => item.vId);
        if (!tempVehicleData.includes(vehicleId)) {
            isExit = false;
        } else {
            isExit = true;
        }
        return isExit;
    };
    const lockTargetFromTree = (data: any) => {
        const isExit = juageVehicleExit(data?.vehicleId);
        if (!isExit) {
            // 筛选后不存在树中需要定位到父级上
            selectFleetInTree(data?.fleetList[0]);
        } else {
            selectVehicleInTree(data);
        }
    };
    // 选择搜索结果
    async function onSelectSearchResult(
        e: React.MouseEvent,
        module: SearchModuleNameType,
        data: any,
    ) {
        switch (module) {
            case 'fleet':
                selectFleetInTree(data);
                break;
            case 'vehicle':
                lockTargetFromTree(data);
                break;
            default:
                break;
        }
    }

    // 生成树结构
    const generateTreeNodes = (data: any[], level: number): any[] => {
        const childLevel = level + 1;
        return data.map((item: Record<string, any>) => {
            const { title, key, isLeaf } = item;
            return {
                title,
                key,
                isLeaf,
                showIcon: true,
                x: 1,
                switcherIcon: false,
                children: item.children?.length ? generateTreeNodes(item.children, childLevel) : [],
            };
        });
    };
    const removeExpandedKeys = (val: string, arr: string[]) => {
        const index = arr.indexOf(val);
        if (index > -1) {
            arr.splice(index, 1);
        }
        return arr;
    };
    // 选择树节点
    async function onSelectTreeNode(selectedKeys: string[], e: any) {
        // 如果选中的是车组，则需要展开此车组
        if (selectedKeys[0] && selectedKeys[0].startsWith(fleetPrefix)) {
            onSelect(undefined);
            await selectFleetInTree({ fleetId: selectedKeys[0].split('-')[1] });
        } else {
            setSelectedKeys(selectedKeys);
            const vehicleId = (selectedKeys[0] || '').split('-')[2];
            onSelect(vehicleId);
        }
        if (e.node.key && e.node.key.startsWith(fleetPrefix)) {
            // 取消选中，收起车组
            if (!e.selected || expandedKeys.includes(e.node.key)) {
                const tempData = [...new Set(cloneDeep(expandedKeys))];
                const newExpandedKeys = removeExpandedKeys(e.node.key, tempData);
                setExpandedKeys(newExpandedKeys);
            }
            // 选中展开车组
            if (!expandedKeys.includes(e.node.key)) {
                setExpandedKeys((expandedKeys: any[]) => {
                    return [...new Set(expandedKeys.concat([e.node.key]))];
                });
            }
        }
    }

    const getMultiSearchValue = (value: any) => {
        if (value && value.module === 'vehicle') {
            onSelect(value.data.vehicleId);
        }
        if (!value) {
            setSelectedKeys([]);
            onSelect(undefined);
        }
    };

    const renderElement = () => {
        if (treeData.length > 0) {
            return (
                <Tree
                    expandedKeys={expandedKeys}
                    onExpand={(keys) => setExpandedKeys(keys)}
                    loadData={loadVehicleData}
                    switcherIcon={<CaretDownOutlined />}
                    showLine={{ showLeafIcon: false }}
                    showIcon={false}
                    ref={treeRef}
                    // @ts-ignore
                    onSelect={onSelectTreeNode}
                    loadedKeys={loadedKeys}
                    selectedKeys={selectedKeys}
                    height={containerHeight}
                    treeData={generateTreeNodes(treeData, 0)}
                />
            );
        }
        return <Empty description={<span>{i18n.t('name', '暂无数据')}</span>} image={<Nodata />} />;
    };

    return (
        <div className="vehicle-group-tree">
            <div className="vehicle-group-tree-tab-wrapper">
                <div className="vehicle-group-tree-search">
                    <MultiSearch
                        placeholder={i18n.t('message', '请输入车组、车牌')}
                        mountedLoad={false}
                        // @ts-ignore
                        onSelect={onSelectSearchResult}
                        searchModule={['fleet', 'vehicle']}
                        simpleTree={false}
                        onChange={getMultiSearchValue}
                        cacheComponentName="realtimeMonitorTreeSearchInput"
                    />
                </div>
                <div className="vehicle-group-tree-body">
                    {loaded ? (
                        renderElement()
                    ) : (
                        <Spin style={{ width: '100%', marginTop: '20px' }} />
                    )}
                </div>
            </div>
        </div>
    );
};

export default VehicleTree;
