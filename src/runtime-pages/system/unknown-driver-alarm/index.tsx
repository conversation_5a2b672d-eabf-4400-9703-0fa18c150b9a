/*
 * @LastEditTime: 2025-07-07 13:26:55
 */
import React, { useState, useEffect, useRef, useContext } from 'react';
import {
    PageBreadcrumbLayout,
    PageCardLayout,
    StarryModal,
} from '@base-app/runtime-lib';
import { Button, Input, Tabs, Form, Modal, Checkbox, message } from '@streamax/poppy';
import { i18n, utils, useUrlSearchStore, Auth, StarryStorage } from '@base-app/runtime-lib';
import { ListDataContainer } from '@streamax/starry-components';
import DateRange from '@/components/DateRange';
import moment from 'moment';
import { CardTable, HandleType, PageType, PageName } from '../components/CardTable';
import BindDriver, { AbnornalFace } from './../components/BindDriver';
import { RefListDataContainerProps } from '@streamax/starry-components/lib/list-data-container';
import { IconDeleteFill, IconRequest } from '@streamax/poppy-icons';
import { postQueryDriverList } from '@/service/driver';
import { useHistory } from '@base-app/runtime-lib/core';
import PageCacheProvider from '@/hooks/usePageDataCache/PageCacheProvider';
import {
    AbnornalFaceAggregate,
    ComposeAbnornalFace,
    abnornalAlarmGet,
    abnornalAlarmDelete,
    abnornalAlarmHandle,
    fetchUnknownDriverAlarmQualified,
} from '@/service/system';
import { PHOTO_QUALITY_SCORE, type RecordItem } from './../constant';
import { getUserPage } from '@/service/user';
import { fetchFileUrlsPost } from '@/service/fms';
import uuid from '@/utils/uuid';
import { disabledAfterDate } from '@/utils/commonFun';
import usePhotoNumber from '@/hooks/usePhotoNumber';
import MultiSearch from '@/components/MultiSearch';
import './index.less';
import { orderBy } from 'lodash';
import useRefreshHistoryData from '../hooks/useRefreshHistoryData';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { getCustomItems, getCustomJsx } from '@/utils/pageReuse';
import { ListPageQueryForm, ListPageTableBase, PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import { useUpdateEffect } from 'ahooks';

export type UnknownDriverAlarmShareProps = ListPageQueryForm & ListPageTableBase & PageTabs;
const UnknownDriverAlarm = (props: UnknownDriverAlarmShareProps) => {
    /***定制***/
    const { getQueryForm, injectSearchData, getPageTabs } = props;
    /***定制***/

    const storage = StarryStorage();
    const searchStore = useUrlSearchStore();
    const formQuery = searchStore.get();
    const [activeKey, setActiveKey] = useState<HandleType>(
        formQuery.activeKey || HandleType.unhandled,
    );

    const [selectedItems, setSelectedItems] = useState<AbnornalFaceAggregate[]>([]);
    const [clickItem, setClickItem] = useState<{
        list?: any[];
        recordId?: string;
        allPictureIds?: string[];
    }>({});
    const [visible, setVisible] = useState(false);
    const listRef = useRef<RefListDataContainerProps>(null);
    const listDoneRef = useRef<RefListDataContainerProps>(null);
    const history = useHistory();
    const [listDataContainerForm] = Form.useForm();
    const [listDataContainerFormDone] = Form.useForm();
    const { getPhotoNumber } = usePhotoNumber();
    const {
        endTime,
        startTime,
        page: storePage,
        pageSize: storePageSize,
        driverName,
        vehicleId,
    } = formQuery;
    const initTimeRange = startTime
        ? [moment(Number(startTime)), moment(Number(endTime))]
        : [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')];
    const initTimeFormRange = [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')];
    const [dataSource, setDataSource] = useState<AbnornalFaceAggregate[]>([]); //
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]); //当前选中的卡片id
    const [checkAll, setCheckAll] = useState(false);
    const [recordItems, setRecordItems] = useState<RecordItem[]>([]); //当前数据列表记录id和司机名称，用于详情翻页使用
    const {checkRefreshData } = useRefreshHistoryData();
    useUpdateEffect(() => {
        if (activeKey === HandleType.unhandled) {
            listRef.current?.loadDataSource({ page: 1 });
        } else {
            listDoneRef.current?.loadDataSource({ page: 1 });
        }
    }, [activeKey]);

    useEffect(() => {
        initLoadData();
    }, []);
    const initLoadData = () => {
        if (Object.keys(formQuery).length) {
            listDataContainerForm.setFieldsValue({
                timeRange: initTimeRange,
                driverName,
                vehicleId,
            });
            listDataContainerFormDone.setFieldsValue({
                timeRange: initTimeRange,
                driverName,
            });
        }
        if (activeKey === HandleType.unhandled) {
            listRef.current?.loadDataSource({ page: 1 });
        } else {
            listDoneRef.current?.loadDataSource({ page: 1 });
        }
    };

    const items: any = [
        {
            label:
                activeKey === HandleType.unhandled
                    ? i18n.t('name', '报警时间')
                    : i18n.t('name', '处理时间范围'),
            name: 'timeRange',
            colSize: 2,
            field: DateRange,
            itemProps: {
                initialValue: initTimeFormRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    allowClear: true,
                    ranges: getPickerRangeTWM(),
                    disabledDate: disabledAfterDate,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
    if (activeKey === HandleType.handled) {
        items.unshift({
            label: i18n.t('name', '司机姓名或工号'),
            name: 'driverName',
            field: Input,
            fieldProps: {
                allowClear: true,
                maxLength: 50,
                placeholder: i18n.t('message', '请输入司机姓名或工号'),
            },
        });
    }
    if (activeKey === HandleType.unhandled) {
        items.unshift({
            label: i18n.t('name', '车辆'),
            name: 'searchData',
            field: MultiSearch,
            fieldProps: {
                searchModule: ['vehicle'],
                placeholder: i18n.t('message', '请选择车辆'),
                prefix: null,
                forceRefresh: true,
                cacheComponentName: 'unknown-driver-alarm-vehicle',
                showTitle: false,
            },
        });
    }
    const newItems: any = getCustomItems(getQueryForm, [...items], { activeKey });

    const reloadData = () => {
        if (activeKey === HandleType.unhandled) {
            listRef.current?.loadDataSource({ page: 1 });
        } else {
            listDoneRef.current?.loadDataSource({ page: 1 });
        }
    };

    const batchClear = (event: React.MouseEvent<HTMLButtonElement>) => {
        const button = event.currentTarget;
        StarryModal.confirm({
            title: i18n.t('name', '批量清理'),
            icon: <IconRequest />,
            content: i18n.t('message', '确认要清理选中的人脸信息吗?'),
            onOk: () => {
                //批量删除
                abnornalAlarmDelete({
                    recordIds: selectedItems.map((item) => item.recordId).join(','),
                }).then((res) => {
                    if (res) {
                        message.success(i18n.t('name', '操作成功'));
                        reloadData();
                    }
                });
            },
            afterClose:()=>{
                button.blur();
            }
        });
    };

    const bindToDriver = async (e: Event, item: AbnornalFaceAggregate) => {
        e.stopPropagation();
        const { unknownDriverId, recordId } = item || {};
        const maxNumber = await getPhotoNumber();
        const data = await fetchUnknownDriverAlarmQualified({
            unknownDriverId,
            qualifiedSize: maxNumber,
        });
        const isQualityPhoto = data?.some((item) => item.score > PHOTO_QUALITY_SCORE);
        if (!checkRefreshData(isQualityPhoto)) return;
        const fileUuidList = data.map((item: any) => item.fileId);
        const urls = fileUuidList.length > 0 ? await fetchFileUrlsPost({ fileUuidList }) : [];
        const list = data?.map((item: any) => {
            const { url, fileUuid } = urls.find((pic: any) => pic.fileUuid === item.fileId) || {};
            return {
                ...item,
                pictureUrl: url,
                pictureId: fileUuid || uuid(),
            };
        });
        const allPictureIds = (item.detailList || []).map((item) => item.pictureId);
        const result = {
            list: orderBy(list, ['score'], ['desc']),
            recordId,
            allPictureIds,
        };
        setClickItem(result);
        setVisible(true);
    };

    const unknownDriver = (event: React.MouseEvent<HTMLButtonElement>) => {
        const button = event.currentTarget;
        StarryModal.confirm({
            title: i18n.t('name', '标记为未知司机'),
            icon: <IconRequest />,
            content: i18n.t('message', '司机身份未知,请前往报警中心处理未知司机报警'),
            onOk: () => {
                //处理未知司机
                abnornalAlarmHandle({
                    handleType: 1,
                    recordIds: selectedItems.map((item) => item.recordId).join(','),
                }).then((res) => {
                    if (res) {
                        message.success(i18n.t('name', '操作成功'));
                        setCheckAll(false);
                        reloadData();
                    }
                });
            },
            afterClose:()=>{
                button.blur();
            }
        });
    };

    const gotoDriver = async (e: Event, item: AbnornalFaceAggregate) => {
        e.stopPropagation();
        const { unknownDriverId, recordId } = item || {};
        const maxNumber = await getPhotoNumber();
        const data = await fetchUnknownDriverAlarmQualified({
            unknownDriverId,
            qualifiedSize: maxNumber,
        });
        const isQualityPhoto = data?.some((item) => item.score > PHOTO_QUALITY_SCORE);
        if (!checkRefreshData(isQualityPhoto)) return;
        const fileUuidList = data.map((item: any) => item.fileId);
        const urls = fileUuidList.length > 0 ? await fetchFileUrlsPost({ fileUuidList }) : [];
        const list = data?.map((item: any) => {
            const { url, fileUuid } = urls.find((pic: any) => pic.fileUuid === item.fileId) || {};
            return {
                ...item,
                pictureUrl: url,
                pictureId: fileUuid || uuid(),
            };
        });
        const result = orderBy(list, ['score'], ['desc']);
        storage.setItem('faceAbnormalSelectedList', result);
        history.push({
            pathname: '/system/unknown-driver-alarm/add-driver',
            //@ts-ignore
            query: {
                pictureList: result.map((item) => item.pictureId).join(','),
                recordIdFormList: recordId,
                handleType: 1, //未知司机报警处理为新司机为1
            },
        });
    };

    const driverDetail = (item: AbnornalFaceAggregate) => {
        storage.setItem('unknownDriverAlarmListData', JSON.stringify(recordItems));
        history.push({
            pathname: '/system/unknown-driver-alarm/face-detail',
            //@ts-ignore
            query: {
                activeKey,
                recordId: item.recordId,
                realDriverId: item.realDriverId,
                unknownDriverId: item.unknownDriverId,
            },
        });
    };

    const handleCheckAll = (e: any) => {
        const { checked } = e.target;
        if (checked) {
            setCheckAll(true);
            setSelectedKeys(dataSource?.map((item) => item.recordId));
        } else {
            setCheckAll(false);
            setSelectedKeys([]);
        }
    };
    const toolbar = {
        hasReload: false,
        extraLeft: (
            <>
                {activeKey !== HandleType.unhandled && (
                    <Auth code="@base:@page:unknown.driver.alarm@action:batch.delete">
                        <Button onClick={batchClear} disabled={!selectedItems.length}>
                            <IconDeleteFill /> {i18n.t('action', '批量清理')}
                        </Button>
                    </Auth>
                )}
                {activeKey === HandleType.unhandled && (
                    <Auth code="@base:@page:unknown.driver.alarm@action:driver.unknow">
                        <Button onClick={unknownDriver} disabled={!selectedItems.length}>
                            {i18n.t('action', '标记为未知司机')}
                        </Button>
                    </Auth>
                )}
            </>
        ),
        extraRight: (
            <Checkbox onChange={handleCheckAll} checked={checkAll}>
                {i18n.t('name', '全选')}
            </Checkbox>
        ),
    };
    const fetchData = async (params: any) => {
        setCheckAll(false);
        setSelectedKeys([]);
        const { page, pageSize, timeRange, driverName, searchData, ...rest } = params;
        let vehicleId = '';
        if (searchData && searchData.module == 'vehicle') {
            vehicleId = searchData.data?.vehicleId;
        }
        let startTime, endTime;
        if (timeRange) {
            startTime = timeRange[0];
            endTime = timeRange[1];
        } else {
            startTime = moment(searchStore.get().startTime);
            endTime = moment(searchStore.get().endTime);
            if (activeKey == HandleType.unhandled) {
                listDataContainerForm.setFieldsValue({
                    ...listDataContainerForm.getFieldsValue(),
                    timeRange: [startTime, endTime],
                });
            } else {
                listDataContainerFormDone.setFieldsValue({
                    ...listDataContainerFormDone.getFieldsValue(),
                    timeRange: [startTime, endTime],
                });
            }
        }
        let param = {
            page,
            pageSize: pageSize || 20,
            handleStatus: activeKey == HandleType.unhandled ? 0 : 1,
            driverName,
            jobNumber: driverName,
            vehicleId,
            startTime: utils.formator.timestampToZeroTimeStamp(startTime),
            endTime: utils.formator.timestampToZeroTimeStamp(endTime),
            ...rest,
        };
        if (activeKey == HandleType.unhandled) {
            param.driverName = '';
        }
        if (activeKey == HandleType.handled) {
            param.vehicleId = '';
        }
        searchStore.set({
            activeKey,
            startTime: startTime.valueOf(),
            endTime: endTime.valueOf(),
            page,
            pageSize,
            driverName,
            vehicleId,
            ...rest,
        });
        const encodeName = param.driverName ? encodeURIComponent(param.driverName) : '';
        param.driverName = encodeName;
        param.jobNumber = encodeName;
        if (injectSearchData) {
            param = injectSearchData?.(param);
        }
        return abnornalAlarmGet({
            ...param,
        } as any).then(async (res) => {
            const { total, list } = res;
            // 查询所有图片
            const fileUuidList = list
                .map((item) => {
                    return item.detailList.map((detailItem) => detailItem.fileId);
                })
                .flat(2);
            const urls =
                fileUuidList.length > 0
                    ? await fetchFileUrlsPost({
                          fileUuidList: fileUuidList,
                      })
                    : [];
            //查询已处理所有司机名称
            const driverIds = list
                .map((item) => item.realDriverId)
                .filter((item) => item)
                .join(',');

            const { list: driverList = [] } =
                activeKey === HandleType.handled && total
                    ? await postQueryDriverList({
                          driverIds,
                          page: 1,
                          pageSize: 100, //分页查询最多100  故查询司机名称取最大值
                          needDriverPicture: false,
                          // domainType: 3
                      })
                    : {};
            // 查询所有已处理人名称
            const userIds = list.map((item) => item.updateUser).join(',');
            const { list: userList = [] } =
                activeKey === HandleType.handled && total
                    ? await getUserPage({
                          state: 1,
                          userIds,
                          page: 1,
                          pageSize: 100, //分页查询最多100  故查询名称取最大值
                      })
                    : {};
            const listTransform: AbnornalFaceAggregate[] = list.map((item) => {
                const diverNameObj = driverList.find((it) => it.driverId === item.realDriverId);
                const updateUserNameObj = userList.find((it) => it.userId === item.updateUser);
                item.detailList.forEach((detailItem) => {
                    const pictureItem = urls.find((it: any) => it.fileUuid === detailItem.fileId);
                    //@ts-ignore
                    detailItem.pictureUrl = pictureItem?.url;
                    detailItem.pictureId = pictureItem?.fileUuid || uuid();

                    // const vehicleNumber = vehicleNumbers.find((it) => it.vehicleId == item.vehicleId);
                    // detailItem.vehicleNumber = pictureItem?.fileUuid || uuid();
                });
                //@ts-ignore
                const newItem: AbnornalFaceAggregate = {
                    ...item,
                    driverName: diverNameObj?.driverName,
                    updateUserName: updateUserNameObj?.account,
                };
                return newItem;
            });
            setDataSource(listTransform);
            setRecordItems(
                listTransform.map((item) => {
                    return {
                        recordId: item.recordId,
                        driverName: item.driverName,
                        hasPrivilege: item.hasPrivilege, //是否有该司机权限
                        updateUserName: item.updateUserName, //处理人
                        handleTime: item.handleTime, //处理时间
                        realDriverId: item.realDriverId,
                        unknownDriverId: item.unknownDriverId,
                    };
                }),
            );
            return {
                ...res,
                list: listTransform,
            };
        });
    };
    const modalClose = () => {
        setVisible(false);
    };
    const onOk = () => {
        setVisible(false);
        reloadData();
    };
    const onCardSelect = (keys: any, selectedItems: any[]) => {
        setSelectedItems(selectedItems);
        if (selectedItems.length < dataSource.length) {
            setCheckAll(false);
        } else if (dataSource.length !== 0) {
            setCheckAll(true);
        }
    };
    const unHandle = (
        <Tabs.TabPane tab={i18n.t('name', '未处理')} key={HandleType.unhandled}>
            <ListDataContainer
                ref={listRef}
                queryForm={{
                    items: newItems,
                    form: listDataContainerForm,
                }}
                renderContainerStyle={{
                    toolbarAndListRender: false,
                }}
                listRender={(data) => {
                    return (
                        <CardTable
                            onSelected={onCardSelect}
                            addDriver={gotoDriver}
                            bindDriver={bindToDriver}
                            driverDetail={driverDetail}
                            list={data as ComposeAbnornalFace[]}
                            itemKey={'recordId'}
                            handleType={activeKey}
                            pageType={PageType.pageList}
                            selectedKeys={selectedKeys}
                            pageName={PageName.unknownDriver}
                        />
                    );
                }}
                toolbar={toolbar}
                getDataSource={fetchData}
            />
        </Tabs.TabPane>
    );
    const handled = (
        <Tabs.TabPane tab={i18n.t('name', '已处理')} key={HandleType.handled}>
            <ListDataContainer
                ref={listDoneRef}
                queryForm={{
                    items: newItems,
                    form: listDataContainerFormDone,
                }}
                renderContainerStyle={{
                    toolbarAndListRender: false,
                }}
                listRender={(data) => {
                    return (
                        <CardTable
                            onSelected={onCardSelect}
                            addDriver={gotoDriver}
                            bindDriver={bindToDriver}
                            driverDetail={driverDetail}
                            list={data as ComposeAbnornalFace[]}
                            itemKey={'recordId'}
                            handleType={activeKey}
                            pageType={PageType.pageList}
                            selectedKeys={selectedKeys}
                            pageName={PageName.unknownDriver}
                        />
                    );
                }}
                toolbar={toolbar}
                getDataSource={fetchData}
            />
        </Tabs.TabPane>
    );
    return (
        <PageCacheProvider>
            <PageBreadcrumbLayout>
                <PageCardLayout className={'unknown-driver-alarm-page'}>
                    <Tabs
                        onChange={(activeKey) => {
                            searchStore.set({
                                startTime: null,
                                endTime: null,
                                activeKey: activeKey as HandleType,
                            });
                            listDataContainerForm.setFieldsValue({
                                timeRange: [
                                    moment().subtract(6, 'days').startOf('day'),
                                    moment().endOf('day'),
                                ],
                            });
                            listDataContainerFormDone.setFieldsValue({
                                timeRange: [
                                    moment().subtract(6, 'days').startOf('day'),
                                    moment().endOf('day'),
                                ],
                            });
                            setCheckAll(false);
                            setActiveKey(activeKey as HandleType);
                        }}
                        destroyInactiveTabPane={true}
                        activeKey={activeKey}
                    >
                        {getCustomJsx(getPageTabs, [unHandle, handled], { activeKey })}
                    </Tabs>

                    <BindDriver
                        visible={visible}
                        selectedItems={clickItem?.list || []}
                        recordId={clickItem?.recordId}
                        allPictureIds={clickItem?.allPictureIds}
                        onOk={onOk}
                        onCancel={modalClose}
                        pageFrom="driverAlarm"
                    />
                </PageCardLayout>
            </PageBreadcrumbLayout>
        </PageCacheProvider>
    );
};
export default withShareRootHOC(UnknownDriverAlarm);
