import { useContext, useState } from 'react';
import { Context } from '../../../../H5Player/store';
import { useDebounceEffect, useLatest } from '@streamax/hooks';
import type { PlayerOption } from '../../../../H5Player/types';
import { PLAYER_STATUS, POLLING_STATE } from '../../../../H5Player/constant';
import { CONTROL_RANGE, isNeedControlBatch } from '@/utils/flow';

let interval: NodeJS.Timer | null = null;

const useDeviceChannelData = () => {
    const {
        layoutCount,
        currentPage,
        totalPage,
        pollingStatus,
        pollingTime,
        playerOptionList,
        deviceChannelFullScreen,
        loaded,
        dispatch,
    } = useContext(Context);

    const [deviceChannels, setDeviceChannels] = useState<PlayerOption[]>([]);
    const totalPageRef = useLatest(totalPage);
    const currentPageRef = useLatest(currentPage);


    useDebounceEffect(
        () => {
            if (loaded) {
                computeChannel();
            }
        },
        [loaded, currentPage, playerOptionList],
        { wait: 10 },
    );

    // 处理轮询
    useDebounceEffect(
        () => {
            if (pollingTime && pollingStatus === POLLING_STATE.open && !deviceChannelFullScreen) {
                interval = setInterval(updateCurrentPage, pollingTime);
            }
            return () => {
                // 轮询条件【存在轮询时间、轮询状态为开启状态】
                interval && clearInterval(interval);
            };
        },
        [pollingTime, pollingStatus, currentPage],
        { wait: 10 },
    );

    function updateCurrentPage() {
        const nextCurrentPage = currentPageRef.current + 1 > totalPageRef.current ? 1 : currentPageRef.current + 1;
        dispatch({
            type: 'setCurrentPage',
            payload: nextCurrentPage,
        });
    }

    const computeChannel = async () => {
        if (playerOptionList.length > 0) {
            
            const startIndex = (currentPage - 1) * layoutCount;
            let result = (playerOptionList.slice(startIndex, startIndex + layoutCount) || []).map(
                (item) => ({
                    ...item,
                    key: `${item.devId}-${item.channel}`,
                }),
            );
            // 要播放的设备，批量请求是否超流，超流设置对应通道状态
            // await isNeedControl(liveVideoOptions?.authId, CONTROL_RANGE.LIVE);
            const authIds = result.map(i => i.devId);
            const flowControls = await isNeedControlBatch(authIds, CONTROL_RANGE.LIVE);
            if (flowControls) {
                result = result.map(item => {
                    const controlItem = flowControls.find((i) => item.devId === i.authId);
                    if (controlItem?.switchFlag) {
                        item.status = PLAYER_STATUS.stopUse;
                    }
                    return item;
                });
            }
            if (result.length > 0) {
                // 设置选中音量通道
                dispatch({
                    type: 'setVolumeDeviceChannel',
                    payload: result.find((i) => i.status !== PLAYER_STATUS.stopUse)?.key,
                });
            }
            setDeviceChannels(result);
        } else {
            setDeviceChannels([]);
        }
    };

    return deviceChannels;
};

export default useDeviceChannelData;
