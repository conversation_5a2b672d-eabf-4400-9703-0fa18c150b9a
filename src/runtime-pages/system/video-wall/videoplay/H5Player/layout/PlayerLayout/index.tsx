import React, { useEffect, useRef } from 'react';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { COM_CLS_PREFIX_PLAYER_LAYOUT } from '../../constant';
import ToolBar from '../ToolBar';
import cn from 'classnames';
import './index.less';

export type ChannelBorderColorType = 'green' | 'primary' | 'none';

export interface PlayerLayoutProps {
    toolBarContent?: any;
    vehicleNumber: string;
    // 是否带边框
    borderColor?: ChannelBorderColorType;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
    onDoubleClick?: React.MouseEventHandler<HTMLDivElement>;
    onLoaded?: (dom: HTMLDivElement) => void;
}

const prefix = COM_CLS_PREFIX_PLAYER_LAYOUT;

const PlayerLayout: React.FC<PlayerLayoutProps> = (props) => {
    const { toolBarContent, borderColor, vehicleNumber, onDoubleClick, onClick, onLoaded } = props;
    const DomRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        onLoaded?.(DomRef.current as HTMLDivElement);
    }, []);

    return (
        <div
            className={cn(prefix, `border-${borderColor}`)}
            onClick={onClick}
            onDoubleClick={onDoubleClick}
            ref={DomRef}
        >
            <div className="vehicle-number">
                <OverflowEllipsisContainer maxWidth={200}>
                    {vehicleNumber}
                </OverflowEllipsisContainer>
            </div>
            <div className="tool-bar-wrapper">
                <ToolBar>{toolBarContent}</ToolBar>
            </div>
            <div className="player-wrapper">
                {/* 播放器 */}
                {props.children}
            </div>
        </div>
    );
};

export default PlayerLayout;
