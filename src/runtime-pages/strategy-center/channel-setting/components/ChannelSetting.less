
@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.channel-mode-setting {
    ul,
    li {
        margin: 0px;
        padding: 0px;
        list-style: none;
    }
    .alarm-select-list {
        flex: 1;
        max-height: calc(100% - 64px);
        //margin-top: 16px;
        padding: 16px 16px 0px 16px;
        overflow-y: auto;
        .alarm-type-group {
            margin-bottom: 12px;
            border: 1px solid @primary-color;
            border-radius: 2px;
            &::abroad {
                border-radius: @border-radius-8;
            }
        }
        .select-item:hover {
            background: @starry-bg-color-component-hover;
        }
        .select-item {
            height: 32px;
            padding: 0 16px;
            overflow: hidden;
            color: @starry-text-color-primary;
            font-weight: 400;
            font-size: 14px;
            line-height: 32px;
            letter-spacing: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
            &-active {
                color: @primary-color;
                background: @primary-color-lighter !important;
            }
            &::abroad {
                border-radius: @border-radius-8;
            }
        }
    }
    .channel-setting {
        position: relative;
        flex: 1;
        height: 100%;
        padding: 0 24px;
        overflow: auto;
        border-left: none;
        .dynamic-channel-table-form-header {
            margin-top: 24px;
        }
        .form-item {
            margin: 0;
            padding: 30px 0;
            border-bottom: 1px solid #eaeaea;
            label {
                font-weight: 600;
                font-size: 16px;
            }
            .poppy-form-item-control-input-content {
                display: flex;
                justify-content: flex-end;
            }
        }
        .form-item:last-child {
            border-bottom: none;
        }
    }
    .add-layout {
        display: flex;
        .setting-select {
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            width: 280px;
            height: 683px;
            background: @starry-bg-color-component-degraded;
            border: 1px solid @starry-border-level-1-color;
            .setting-title {
                display: flex;
                align-items: center;
                height: 46px;
                padding: 0 16px;
                color: rgba(0, 0, 0, 0.65);
                font-weight: 700;
                font-size: 14px;
            }
        }
    }
    .edit-layout {
        display: flex;
        flex-direction: column;
        height: 735px;
        border: 1px solid @starry-border-level-1-color;
        .setting-header {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: space-between;
            height: 46px;
            padding: 0 15px;
            background: @starry-bg-color-component-degraded;
        }
        .flex {
            display: flex;
            width: 100%;
            height: 688px;
            .left {
                border-right: 1px solid @starry-border-level-1-color;
                .search-wrapper {
                    padding: 16px 16px 0;
                }
            }
            .alarm-select-list {
                flex: none;
                width: 280px;
            }
            .alarm-type-setting {
                flex: 1;
                height: auto;
                border: none;
                border-bottom: 1px solid @starry-border-level-1-color;
            }
        }
    }
    .null-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        color: @starry-text-color-disabled;
    }
}
.edit-layout::abroad {
    border-radius: @border-radius-8;
}
