import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import Linkage from '@/modules/strategy/LinkageStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { Action } from '@base-app/runtime-lib';
import { useState } from 'react';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageBase } from '@/types/pageReuse/pageReuseBase';
import useOpenIsWhen from '@/hooks/useOpenIsWhen';
import { message } from '@streamax/poppy';
/**定制**/
export type DefaultLinkAlarmShareProps = PageBase;
const DefaultLinkAlarm = (props: DefaultLinkAlarmShareProps) => {
    /**定制**/
    const { showBreadCrumb } = props;
    /**定制**/
    const [appId, setAppId] = useState();
    const history = useHistory();

    const { when, openIsWhen } = useOpenIsWhen(false);
    
    const [uploading, setUploading] = useState(false);
    const goMore = () => {
        Action.openActionUrl({
            code: '@base:@page:setting.default.alarm.linkage@action:more',
            // @ts-ignore
            history,
            url: '/strategy/alarm-linkage',
            params: {
                choosedAppId: appId || '',
            },
        });
    };

    // 检测当前是否处于上传中状态，如果是上传中，提示后终止相应的操作
    const handleUploadStatusCheck = () => {
        if (uploading) {
            message.warn(i18n.t('message', '音频正在上传中，请稍等'));
            return false;
        }
        return true;
    };

    return (
        <StrategyDetailPage
            title={i18n.t('name', '报警通知设置')} // 和产品确认以原型为准，使用“报警通知”
            moreCode="@base:@page:setting.default.alarm.linkage@action:more"
            goMore={goMore}
            appIdChange={setAppId}
            type={STRATEGY_TYPE.ALARM_LINKAGE}
            showBreadCrumb={showBreadCrumb}
            when={when}
            openIsWhen={openIsWhen}
            subComponent={
                // @ts-ignore
                <Linkage
                    onUploadStatus={(status) => setUploading(status)}
                    title={i18n.t('name', '设置信息')}
                    authCode={{
                        editCode: '@base:@page:setting.default.alarm.linkage@action:edit',
                    }}
                    openIsWhen={openIsWhen}
                    msgSendFormAuthCode={{
                        displayLocationAuthCode: '@base:@page:setting.default.alarm.linkage@action:display.screen',
                        commonAudioAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.text',
                        commonTextAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.audio',
                        textMessageAuthCode: '@base:@page:setting.default.alarm.linkage@action:text.message',
                        audioMessageAuthCode: '@base:@page:setting.default.alarm.linkage@action:audio.message',
                        audioTransformAuthcode:'@base:@page:setting.default.alarm.linkage@action:text.to.speech'
                    }}
                />
            }
            onClickAppSelectItem={handleUploadStatusCheck}
        />
    );
};
export default withShareRootHOC(DefaultLinkAlarm);
