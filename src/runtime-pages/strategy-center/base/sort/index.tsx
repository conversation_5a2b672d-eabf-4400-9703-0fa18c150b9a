import { message } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { useHistory, useLocation, useParams } from '@base-app/runtime-lib/core';
import { useEffect, useState } from 'react';
import PageSort from '../../../../components/SortPage';
import { getStrategyListByPage, sortStrategy } from '../../../../service/strategy';
import { updateUserCountryConfig } from '../../user-policy/common';

const Sort = (props: {
    options?: {
        fetchSortPageList?: (params: any) => Promise<any>;
        sortStrategy?: (params: any) => Promise<any>;
    };
}) => {
    const {options } = props || {};
    const history = useHistory();
    const location = useLocation();
    const params: Record<string, string> = useParams();
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [configureType, setConfigureType] = useState<string>();
    const [loading, setLoading] = useState(false);
    const [initLoading, setInitLoading] = useState(false);
    const INTERNATIONAL_TYPE = {
        '1': 'evidenceReturnStrategy',
        '2': 'alarmLinkageStrategy',
        '3': 'emailSendingStrategy',
        '6': 'userStrategy',
        '7': 'dataCleanStrategy',
        '8': 'faceComparisonStrategy',
        '9': 'autoUploadStrategy',
        '10': 'alarmWebResponseStrategy',
        '11': 'pictureCaptureStrategy',
        '17': 'flowLimitConfig',
        '18': 'channelSettingStrategy',
        '19': 'dataCleanStrategy',
        '20': 'channelSettingStrategy',
    };
    useEffect(() => {
        getSortType(location.pathname);
    }, []);
    useEffect(() => {
        if (configureType) getDataList();
    }, [configureType]);
    function getSortType(str: string) {
        const typeList = [
            {
                key: 'evidence',
                value: 1,
            },
            {
                key: 'alarm-linkage',
                value: 2,
            },
            {
                key: 'email-sending',
                value: 3,
            },
            {
                key: 'group-alarm',
                value: 4,
            },
            {
                key: 'vehicle-alarm',
                value: 5,
            },
            {
                key: 'user',
                value: 6,
            },
            {
                key: 'data-cleaning',
                value: 7,
            },
            {
                key: 'face-contrast',
                value: 8,
            },
            {
                key: 'auto-upload',
                value: 9,
            },
            {
                key: 'auto-handle',
                value: 10,
            },
            {
                key: 'picture-capture',
                value: 11,
            },
            {
                key: 'flow-setting',
                value: 17,
            },
            {
                key: 'CH-set',
                value: 18,
            },
            {
                key: 'retention-period-setting',
                value: 19,
            },
        ];
        const configureType = typeList.find(
            (item) => str.indexOf(item.key) > -1,
        )?.value;
        setConfigureType(String(location.query?.configureType || configureType));
    }
    async function getDataList() {
        // 外层列表页是屏蔽了查询BP应用，故此处也屏蔽
        try {
            setInitLoading(true);
            if (params.appId == '0') {
                setDataSource([]);
                setInitLoading(false);
                return;
            }
            const reqParams = {
                configureType: configureType,
                appId: params.appId === 'undefined' ? '' : params.appId, //bug 57210，未复现，但是是因为appid传的undefined引起的，做下判断
                page: 1,
                pageSize: 1e6,
            };
            const res = await (options?.fetchSortPageList?.(reqParams) ?? getStrategyListByPage(reqParams));
            const data = (res.list || [])
                .sort((a: any, b: any) => {
                    return b.priority - a.priority;
                })
                .map((item: any, index: number) => {
                    return {
                        ...item,
                        index,
                    };
                });
            setDataSource(data);
        } catch {
            //
        } finally {
            setInitLoading(false);
        }
    }

    const columns = [
        {
            title: i18n.t('name', '设置名称'),
            dataIndex: 'configureName',
            ellipsis: true,
            render: (text: any, record: any) =>
                i18n.t(
                    `@i18n:@${INTERNATIONAL_TYPE[configureType || '1']}__${
                        record.configureId
                    }`,
                    record.configureName,
                ) || '-',
        },
    ];
    // 保存排序
    function onSave(resourceIds: any[]) {
        if (loading) return;
        setLoading(true);
        if (params.appId === 'undefined') {
            message.error(i18n.t('message', '操作失败,请返回重试'));
            return;
        }
        const reqParams = {
            ids: resourceIds.join(','),
            configureType: configureType,
            appId: params.appId,
        };
        const promiseSort =
            options?.sortStrategy?.(reqParams) ??
            sortStrategy(reqParams);
        promiseSort
            .then(() => {
                // 排序后改变优先级，可能有国家地区变化
                updateUserCountryConfig();
                history.goBack();
            })
            .catch(() => {
                message.error(i18n.t('message', '操作失败'));
            })
            .finally(() => setLoading(false));
    }

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <PageSort
                    data={dataSource}
                    loading={initLoading}
                    columns={columns}
                    onSave={onSave}
                />
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default Sort;
