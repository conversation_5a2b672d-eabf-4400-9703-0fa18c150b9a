/*
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description:发件邮箱 - 详细信息
 * @version: 0.0.0
 * @Date: 2022-06-22 17:48:17
 * @LastEditTime: 2022-10-10 17:24:05
 * @LastEditors: xiaoli<PERSON><PERSON>iao
 * @FilePath: \src\runtime-pages\strategy-center\email-sending\components\MailSettingInfo.tsx
 */
import EyeFilled from '@ant-design/icons/lib/icons/EyeFilled';
import EyeInvisibleFilled from '@ant-design/icons/lib/icons/EyeInvisibleFilled';
import { getSensitivePassword } from '@/service/strategy';
import { Col, Row } from '@streamax/poppy';
import { Auth, i18n, utils, useEmailUsernameShow,StarryAbroadIcon } from '@base-app/runtime-lib';
import { StarryInfoBlock } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { useContext, useState } from 'react';
import { Context } from '../../base/context';
import { CONFIGURE_TYPE_EMAIL_SEND } from '../base';
import { Descriptions } from '@streamax/starry-components';

export const MailSettingInfo = (props: any) => {
    const { editAuthCode, editPagePath } = props;
    const [visible, setVisible] = useState(false);
    const [sensitivePassword, setSensitivePassword] = useState('');
    const history: any = useHistory();
    const { strategyDetail }: any = useContext(Context) || {};
    const { showEmailUserName } = useEmailUsernameShow();
    const loginType = {
        0: 'SMTP'
    };
    const getParameter: any = (list: any[], name: string) => {
        const params: any = {};
        list?.forEach((item: any) => {
            const { paramName, paramValue } = item;
            if (paramName === 'encryptType') {
                const encryptTypeEnum = {
                    '0': i18n.t('state', '不加密'),
                    '1': 'SSL',
                    '2': 'TLS',
                };
                params[paramName] = encryptTypeEnum[paramValue];
            } else {
                params[paramName] = paramValue;
            }
        });
        return params[name];
    };
    const getPassword = () => {
        if (strategyDetail.paramList.length !== 0) {
            getSensitivePassword({
                configureId: props.configureId || strategyDetail.configureId,
                configureType: CONFIGURE_TYPE_EMAIL_SEND,
                word: 'password',
            }).then((rs) => {
                setVisible(true);
                setSensitivePassword(rs);
            });
        } else {
            setVisible(true);
        }
    };

    return (
        <>
            <StarryInfoBlock
                title={i18n.t('name', '邮箱配置')}
                operation={
                    <Auth code={editAuthCode}>
                        <a
                            style={{
                                opacity: strategyDetail?.configureId == undefined ? '.5' : '1',
                            }}
                            onClick={() =>
                                strategyDetail?.configureId != undefined &&
                                history.push(
                                    `${editPagePath}?configureId=${strategyDetail.configureId}&configureName=${strategyDetail.configureName}&appId=${strategyDetail.appId}&model=personal`,
                                )
                            }
                        >
                            {i18n.t('action', '编辑')}
                        </a>
                    </Auth>
                }
            >
                <div className="detail-baseinfo">
                    <Descriptions>
                        <Descriptions.Item label={i18n.t('name', '登录方式')}>
                            {
                                loginType[getParameter(strategyDetail.paramList, 'emailType')] || 'SMTP'
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '发件邮箱')}>
                            {
                                getParameter(strategyDetail.paramList, 'senderEmail') || '-'
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '发件人')}>
                            {
                                getParameter(strategyDetail.paramList, 'sender') || '-'
                            }
                        </Descriptions.Item>
                        {showEmailUserName === 1 && (
                            <Descriptions.Item label={i18n.t('name', '用户名')}>
                                {
                                    getParameter(strategyDetail.paramList, 'userName') || '-'
                                }
                            </Descriptions.Item>
                        )}
                        <Descriptions.Item label={i18n.t('name', '密码')}>
                            {visible ? (
                                <span className='mail-send-eye-icon-box'>
                                    <span style={{ marginRight: 10, wordBreak: 'break-all' }}>
                                        {utils.aes.AesDecrypt(sensitivePassword || '-')}
                                    </span>
                                    <StarryAbroadIcon>
                                    <a>
                                        <EyeInvisibleFilled
                                            // @ts-ignore
                                            onClick={() => {
                                                setVisible(false);
                                            }}
                                        />
                                    </a>
                                    </StarryAbroadIcon>
                                </span>
                            ) : (
                                <span className='mail-send-eye-icon-box'>
                                    <span style={{ marginRight: 10 }}>
                                        {Array.from({ length: 16 }).fill('*')}
                                    </span>
                                    <StarryAbroadIcon>
                                        <a>
                                        <EyeFilled
                                            // @ts-ignore
                                            onClick={getPassword}
                                        />
                                    </a>
                                    </StarryAbroadIcon>
                                </span>
                            )}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', 'SMTP服务器')}>
                            {
                                getParameter(strategyDetail.paramList, 'smtpAddress') || '-'
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', 'SMTP端口')}>
                            {
                                getParameter(strategyDetail.paramList, 'port') || '-'
                            }
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '加密类型')}>
                            {
                                getParameter(strategyDetail.paramList, 'encryptType') || '-'
                            }
                        </Descriptions.Item>
                    </Descriptions>
                </div>
            </StarryInfoBlock>
        </>
    );
};
