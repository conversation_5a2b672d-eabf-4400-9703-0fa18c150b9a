/*
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description:发件邮箱 - 添加复制
 * @version: 0.0.0
 * @Date: 2022-06-22 17:02:06
 * @LastEditTime: 2022-10-10 17:21:32
 * @LastEditors: xiaoli<PERSON><PERSON>iao
 * @FilePath: \src\runtime-pages\strategy-center\email-sending\add\index.tsx
 */
import { getSensitivePassword } from '@/service/strategy';
import { i18n } from '@base-app/runtime-lib';
import DefaultAuthorizeScope from '../../base/components/DefaultAuthorizeScope';
import DefaultBasicInfo from '../../base/components/DefaultBasicInfo';
import {
    AUTH_TYPE_FLEET,
    AUTH_TYPE_VEHICLE,
    SCOPE_TYPE_WITHOUT_CHILD,
    SCOPE_TYPE_WITH_CHILD,
} from '../../base/constant';
import createStrategyAddFactory from '../../base/createStrategyAddFactory';
import type { StepData } from '../../base/types';
import { CONFIGURE_TYPE_EMAIL_SEND, INTERNATIONAL_TYPE } from '../base';
import MailConfiguration from '../components/MailConfiguration';
import type { ParamListItem } from '../type';
import { paramListTransfer } from '../util';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems } from '@/utils/pageReuse';
import { useLocation } from '@base-app/runtime-lib/core';
import {getVehiclerListByPage} from "@/service/vehicle";

export type EmailSendingAddShareProps = PageTabs;

const EmailSendingAdd = (props: EmailSendingAddShareProps) => {
    /**定制***/
    const { getPageAddTabConfigItems } = props;
    /**定制***/

    // @ts-ignore
    const { query } = useLocation();
    const configureId = query.configureId;

    const customVehicleAddTransferData = async (params: any)=>{
        return await getVehiclerListByPage({...params, vehicleState: undefined, vehicleStates: '0,1'}, true);
    };

    const stepList: StepData[] = [
        {
            key: 'basicConfig',
            stepName: i18n.t('name', '基本配置'),
            segmentList: [
                {
                    key: 'basicInfo',
                    segmentName: i18n.t('name', '基本信息'),
                    segmentContent: (
                        <DefaultBasicInfo
                            type="add"
                            internationalType={INTERNATIONAL_TYPE}
                        />
                    ),
                    bottomDivider: true,
                },
                {
                    key: 'captureConditions',
                    segmentName: i18n.t('name', '邮件配置'),
                    segmentContent: <MailConfiguration />,
                },
            ],
        },
        {
            key: 'authorizeScope',
            stepName: i18n.t('name', '授权范围'),
            segmentList: [
                {
                    key: 'authorizeScope',
                    segmentName: i18n.t('name', '授权范围'),
                    segmentContent: (
                        // @ts-ignore
                        // createStrategyAddFactory内部会报警处理appId
                        <DefaultAuthorizeScope
                            modules={['fleet', 'vehicle']}
                            batchSave
                            type="add"
                            customVehicleAddTransferData={customVehicleAddTransferData}
                        />
                    ),
                },
            ],
        },
    ];

    const formatStep1ParamData = (params: any) => {
        return paramListTransfer
            .toSubmit(Array.isArray(params) ? params[1] : params)
            .map((item) => {
                return item.paramName === 'smtpAddress' ||
                    item.paramName === 'sender'||item.paramName==='userName'
                    ? {
                          ...item,
                          paramValue: item.paramValue?.trim(),
                      }
                    : item;
            });
    };

    const formatStep2ReqData = (params: any) => {
        const { scopeFleet = [], scopeVehicle = [] } = params || {};
        const authList = [];
        const includeChild = scopeFleet.filter(
            (p: { scope: number }) => p.scope === SCOPE_TYPE_WITH_CHILD,
        );
        const excludeChild = scopeFleet.filter(
            (p: { scope: number }) => p.scope === SCOPE_TYPE_WITHOUT_CHILD,
        );
        includeChild.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_FLEET,
                authIds: includeChild
                    .map((p: { fleetId: number }) => p.fleetId)
                    .join(','),
                isFull: true,
                scope: SCOPE_TYPE_WITH_CHILD,
            });
        excludeChild.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_FLEET,
                authIds: excludeChild
                    .map((p: { fleetId: number }) => p.fleetId)
                    .join(','),
                isFull: true,
                scope: SCOPE_TYPE_WITHOUT_CHILD,
            });
        scopeVehicle.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_VEHICLE,
                authIds: scopeVehicle
                    .map((p: { vehicleId: number }) => p.vehicleId)
                    .join(','),
                isFull: true,
            });
        return authList;
    };

    const resToStep1FormData = async ({
        description,
        appId,
        paramList = [],
    }: any) => {
        // 复制时，设置名称和优先级不复制(需求如此)
        const basicInfo = {
            // configureName,
            description,
            // priority,
            appId,
        };
        // 获取密码
        const password = await getSensitivePassword({
            configureId: configureId,
            configureType: CONFIGURE_TYPE_EMAIL_SEND,
            word: 'password',
        });
        // 组装密码回填
        const newParamList = paramList.map(
            (item: Pick<ParamListItem, 'paramName' | 'paramValue'>) => {
                if (item.paramName === 'password') {
                    return {
                        paramName: item.paramName,
                        paramValue: password,
                    };
                }
                return item;
            },
        );
        const paramListInfo = paramListTransfer.toRender(newParamList || []);
        return [basicInfo, paramListInfo];
    };

    const newStepList = getCustomItems(getPageAddTabConfigItems, stepList);

    return createStrategyAddFactory(newStepList, CONFIGURE_TYPE_EMAIL_SEND, {
        formatStep1ParamData,
        formatStep2ReqData,
        resToStep1FormData,
    });
};

export default withShareRootHOC(EmailSendingAdd);
