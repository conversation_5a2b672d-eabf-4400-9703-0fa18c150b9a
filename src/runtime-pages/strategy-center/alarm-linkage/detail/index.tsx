/*
 * @LastEditTime: 2025-03-13 14:05:42
 */
import { i18n } from '@base-app/runtime-lib';
import createStrategyDetailFactory from '../../base/createStrategyDetailFactory';
import DefaultBasicInfoDetail from '../../base/components/DefaultBasicInfoDetail';
import DefaultAuthorizeScopeDetail from '../../base/components/DefaultAuthorizeScopeDetail';
import { TabItem } from '../../base/types';
import { INTERNATIONAL_TYPE, Actions } from '../base';
import LinkageSetting from '../components/step1/LinkageSetting';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems } from '@/utils/pageReuse';
export type AlarmLinkageDetailShareProps = PageTabs;

const { detailPage } = Actions;
const AlarmLinkageDetail = (props: AlarmLinkageDetailShareProps) => {
    /**定制***/ 
    const { getPageTabsConfigItems } = props;
    /**定制***/ 
    const tabs: TabItem[] = [
        {
            key: 'strategyInfo',
            tabName: i18n.t('name', '设置信息'),
            tabContent: (
                <DefaultBasicInfoDetail
                    editAuthCode={detailPage.editBasicInfo.authCode}
                    editPagePath={detailPage.editBasicInfo.path}
                    internationalType={INTERNATIONAL_TYPE}
                />
            ),
        },
        {
            key: 'alarmLinkConfig',
            tabName: i18n.t('name', '个性化配置'),
            tabContent: <LinkageSetting inDetailPage={true} />,
        },
        {
            key: 'scope',
            tabName: i18n.t('name', '适用范围'),
            tabContent: (
                <DefaultAuthorizeScopeDetail
                    modules={['fleet', 'vehicle']}
                    addOrDeleteCode={detailPage.addOrDeleteCode}
                />
            ),
        },
    ];
    const newTab = getCustomItems(getPageTabsConfigItems, tabs);
    // @ts-ignore
    return createStrategyDetailFactory(newTab, {
        internationalType: INTERNATIONAL_TYPE,
        deleteAuthCode: detailPage.delete.authCode,
    });
};

export default withShareRootHOC(AlarmLinkageDetail);
