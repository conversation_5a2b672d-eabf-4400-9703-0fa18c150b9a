import { message } from '@streamax/poppy';
import { IconRequest } from '@streamax/poppy-icons';
import {
    getAppGlobalData,
    i18n,
    ThemeStyleRadio,
    changeGlobalThemeStyle,
    g_emmiter,
    utils,
    Auth,
} from '@base-app/runtime-lib';
import { StarryModal } from '@base-app/runtime-lib';
import type { ThemeStyleObj, ThemeStyleRadioValue } from '@base-app/runtime-lib';
import { useEffect, useState } from 'react';
import { updateThemeApply, fetchThemeApplyDetail } from '@/service/theme';
import { ApplyTypeEnum, ThemeTypeEnum } from '@/types/theme';
import './ThemeStyle.less';


const ThemeStyle = () => {
    const [themeValue, setThemeValue] = useState<ThemeStyleRadioValue>('mix');

    useEffect(() => {
        fetchThemeApplyDetail({
            //  0 租户
            applyType: ApplyTypeEnum.TENANT_TYPE,
            applyId: getAppGlobalData('APP_USER_INFO')?.tenantId,
            /** 主题样式 */
            themeType: ThemeTypeEnum.STYLE,
        }).then((res: any) => {
            setThemeValue(res.themeValue || "mix");
        });
    }, []);

    const confirmChange = (obj: ThemeStyleObj) => {
        setThemeValue(obj.themeValue);
        updateThemeApply({
            applyType: 0,
            applyId: getAppGlobalData('APP_USER_INFO')?.tenantId,
            /** 主题样式 */
            themeType: 1,
            themeId: obj.id,
        })
            .then(() => {
                message.success(i18n.t('common.base.operation.success', '操作成功'));
                changeGlobalThemeStyle(obj.themeValue);
                // 更新logo
                g_emmiter.emit('@starry:@event:global.app.logo.change');
                utils.general.updateSystemInitConfig();
            })
            .catch(() => {
                message.error(i18n.t('common.base.operation.fail', '操作失败'));
            });
    };

    const changeTheme = (value: ThemeStyleRadioValue, obj: ThemeStyleObj) => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '更换确认'),
            content: i18n.t('message', '确认将主题样式变更为“{styleText}”？', {
                styleText: i18n.t(`@i18n:@themeStyle__${obj.id}`, obj.themeName),
            }),
            icon: <IconRequest />,
            onOk: () => {
                confirmChange(obj);
            },
        });
    };

    return (
        <div className="theme-style-container">
            <div className="theme-style-container-text">{i18n.t('name', '设置默认主题样式')}</div>
            <ThemeStyleRadio value={themeValue} onChange={changeTheme} />
        </div>
    );
};

export default ThemeStyle;
