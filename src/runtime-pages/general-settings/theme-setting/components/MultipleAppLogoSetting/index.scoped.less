@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.multiple-logo-container {
	display: flex;
	flex-direction: column;
	gap: 24px;
	.logo-title {
		font-weight: 600;
        color: @starry-text-color-primary;
	}
	::global {
		.tip-icon {
            color: @starry-text-color-secondary;
			&:hover {
				color: @primary-color;
				cursor: pointer;
			}
		}
		.hidden-image-text {
			vertical-align: middle;
			border-radius: 2px;
			border: 1px dashed @starry-border-level-2-color;
			.poppy-image-img {
				object-fit: contain;
				max-width: 38px;
				max-height: 38px;
			}
			.poppy-image-mask {
				.poppy-image-mask-info {
					.anticon {
						margin-right: 32px;
						margin-left: 8px;
					}
				}
			}
		}
	}
}

.image-wrapper {
	width: 100%;
	height: 100%;
	.delete-icon-wrapper {
		display: none;
		position: absolute;
		align-items: center;
		justify-content: center;
		width: 102px;
		height: 102px;
		top: 1px;
		left: 1px;
		background: rgba(0, 0, 0, 0.45);
		color: #fff;
	}
	&:hover {
		.delete-icon-wrapper {
			display: flex;
		}
	}
}

.image-tips {
	color: @starry-text-color-secondary;
}