import { But<PERSON>, Form, Space, Spin, Tooltip, Upload, message } from "@streamax/poppy";
import { IconDelete, IconInformationFill } from "@streamax/poppy-icons";
import type { RcFile, UploadChangeParam } from "@streamax/poppy/lib/upload";
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Auth,
    RouterPrompt,
    g_emmiter,
    getAppGlobalData,
    i18n,
    utils,
    useConfigParameter,
} from '@base-app/runtime-lib';
import { useSetState, useSubmitFn, useToggle } from "@streamax/hooks";
import { useRef, useState } from "react";
import { fetchFileDownloadUrl, fileUploadStream } from "@/service/gss";
import { useAsyncEffect } from "ahooks";
import type { ThemeItem} from "@/types/theme";
import { ThemeTypeEnum } from "@/types/theme";
import type { ThemeStyleExtendParams} from "@/service/theme";
import { fetchThemeList, fetchThemeStyleExtend, updateThemeStyleExtend } from "@/service/theme";
import './index.scoped.less';

const { encryptPassword: encrypToken } = utils.general;

/**图片大小限制 200k*/
const IMAGE_SIZE_LIMIT = 200 * 1024;

type OperateType = 'add' | 'edit';

/**logo类型 */
type LogoType = 'logoSemiDark' | 'logoLight' | 'logoDark';

export default () => {
    const [uploadLoading, setUploading] = useSetState<{
        logoSemiDarkLoading?: false;
        logoLightLoading?: false;
        logoDarkLoading?: false;
    }>({});
	const [logoUrlInfo, setLogoUrlInfo] = useSetState<{
        logoSemiDarkUrl?: string;
        logoLightUrl?: string;
        logoDarkUrl?: string;
    }>({});
	const [when, setWhen] = useState<boolean>(false);
	const [initLoading, setInitLoading] = useState<boolean>(false);
    const appId = getAppGlobalData('APP_ID') as string;
    const { data: isShowDark } = useConfigParameter({ key: 'THEME.STYLE.DARK' });

	const [operateType, { toggle }] = useToggle<OperateType, OperateType>('edit', 'add');
	const [form] = Form.useForm();

	const currentAppInfoRef = useRef<{ mixId: string; lightId: string; darkId: string }>({});

	const calcThemeStyle = (list: ThemeItem[]) => {
		const result = list
			.map((item) => {
				if (item.themeValue === 'mix') {
                    return {
                        mixId: item.id,
                    };
                } else if (item.themeValue === 'light') {
                    return {
                        lightId: item.id,
                    };
                } else if (item.themeValue === 'dark') {
                    return {
                        darkId: item.id,
                    };
                }
			})
			.reduce((pre, cur) => Object.assign(pre, cur), {});
		return result as {
            mixId: string;
            lightId: string;
            darkId: string;
        };
	};

	useAsyncEffect(async () => {
		try {
			setInitLoading(true);
			await loadData();
		} catch {
			// ignore
		} finally {
			setInitLoading(false);
		}
	}, []);

	const loadData = async() => {
		const { list: themeLIst } = await fetchThemeList({
			page: 1,
			pageSize: 200,
			themeType: ThemeTypeEnum.STYLE,
		});
		const { mixId, lightId, darkId } = calcThemeStyle(themeLIst) || {};
        currentAppInfoRef.current = { mixId, lightId, darkId };
        const initialPromiseArr = [
            getInitThemeLogo('logoSemiDark', mixId),
            getInitThemeLogo('logoLight', lightId)];
       isShowDark && initialPromiseArr.push(getInitThemeLogo('logoDark', darkId));
		await Promise.all(initialPromiseArr);
	};
	
	const getInitThemeLogo = (type: LogoType, themeId: string) => {
		fetchThemeStyleExtend({ extendType: 'appLogo', themeId, appId }).then((res) => {
			const { appLogo: fileId } = JSON.parse(res?.[0]?.extendData || '{}');
			fileId &&
                fetchFileDownloadUrl(
                    {
                        fileIdList: fileId.toString(),
                        validTime: 365,
                    },
                    {
                        _appId: 0,
                        _tenantId: 0,
                    },
                ).then((data) => {
                    if (data && data[0]) {
                        setLogoUrlInfo({
                            [`${type}Url`]: data[0].fileUrl,
                        });
                        form.setFieldsValue({
                            [type]: fileId,
                        });
                    }
                });
		});
	};


	const [handleSave, subLoading] = useSubmitFn(async () => {
		const {
            logoSemiDark = null,
            logoLight = null,
            logoDark = null,
        } = await form.validateFields();
		const { mixId, lightId, darkId } = currentAppInfoRef.current || {};
		const reqParams: Omit<ThemeStyleExtendParams, 'extendType'>[] = [
            {
                appId,
                themeId: mixId,
                extendData: JSON.stringify({
                    appLogo: logoSemiDark,
                }),
            },
            {
                appId,
                themeId: lightId,
                extendData: JSON.stringify({
                    appLogo: logoLight,
                }),
            }
        ];
        // dark资源存在才展示和传传参
        isShowDark &&
            reqParams.push({
                appId,
                themeId: darkId,
                extendData: JSON.stringify({
                    appLogo: logoDark,
                }),
            });
		await updateThemeStyleExtend(reqParams);
		message.success(i18n.t('message', '操作成功'));
		g_emmiter.emit('@starry:@event:global.app.logo.change');
		handleCancel();
	});

	const handleCancel = () => {
		if (subLoading) return;
		toggle();
		setWhen(false);
		loadData();
	};

	const customRequest = (type: LogoType) => {
		return function ({ file, onSuccess, onError }: any) {
			const formData = new FormData();
			formData.append('file', file);
			fileUploadStream(formData, {
                _abs: encrypToken(window.localStorage.getItem('AUTH_TOKEN') as string),
            })
                .then((res: string) => {
                    form.setFieldsValue({
                        [type]: res,
                    });
                    getImgUrl(res, type, onSuccess, onError);
                })
                .catch(() => {
                    onError();
                });
		};
	};

	const getImgUrl = (fileId: string | number, type: LogoType, onSuccess?: any, onError?: any) => {
		fetchFileDownloadUrl(
            {
                fileIdList: fileId.toString(),
                validTime: 365,
            },
            {
                _appId: 0,
                _tenantId: 0,
            },
        )
            .then((data) => {
                if (data && data[0]) {
                    setLogoUrlInfo({
                        [`${type}Url`]: data[0].fileUrl,
                    });
                    onSuccess?.();
                }
            })
            .catch(() => {
                onError?.();
            });
	};
	
	const handleChange = (info: UploadChangeParam, type: LogoType) => {
		if (info.file.status === 'uploading') {
			  setUploading({
                  [`${type}Loading`]: true,
              });
			return;
		}
		if (info.file.status === 'done' || info.file.status === 'error') {
			 setUploading({
                 [`${type}Loading`]: false,
             });
		}
	};

	const ImageUploadBtn = (type: LogoType) => {
        const show = uploadLoading?.[`${type}Loading`];
		return (
			<Space direction="vertical" align="center" size={12}>
				{show ? (
					<LoadingOutlined spin style={{ fontSize: 24 }} />
				) : (
					<PlusOutlined style={{ fontSize: 24 }} />
				)}
				<div>{i18n.t('name', '上传图片')}</div>
			</Space>
		);
	};

	const handleBeforeUpload = async (file: RcFile) => {
		if (file.type !== 'image/png') {
			message.error(i18n.t('message', '仅支持png格式，且小于200k'));
			return Upload.LIST_IGNORE;
		}
		if (file.size > IMAGE_SIZE_LIMIT) {
			message.error(i18n.t('message', '仅支持png格式，且小于200k'));
			return Upload.LIST_IGNORE;
		}
		return true;
	};

	const handleEdit = () => {
		toggle();
		setWhen(true);
	};

	return (
        <Spin spinning={initLoading}>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <div className="single-app-logo-container">
                <div className="single-app-logo-nav-wrapper">
                    <Space size={8}>
                        <div className="logo-title">{i18n.t('name', '设置应用Logo')}</div>
                        <Tooltip
                            placement="right"
                            title={i18n.t('message', '根据设置的主题样式展示对应的Logo')}
                        >
                            <IconInformationFill className="tip-icon" />
                        </Tooltip>
                    </Space>
                    <Space>
                        {operateType === 'add' ? (
                            <Space
                                size={24}
                                style={{ pointerEvents: subLoading ? 'none' : 'auto' }}
                            >
                                <Button style={{ padding: 0 }} type="link" onClick={handleCancel}>
                                    {i18n.t('action', '取消')}
                                </Button>
                                <Button
                                    style={{ padding: 0 }}
                                    loading={subLoading}
                                    type="link"
                                    onClick={handleSave}
                                >
                                    {i18n.t('action', '保存')}
                                </Button>
                            </Space>
                        ) : (
                            <Button style={{ padding: 0 }} type="link" onClick={handleEdit}>
                                {i18n.t('action', '编辑')}
                            </Button>
                        )}
                    </Space>
                </div>
                <Form layout="vertical" form={form} style={{ maxWidth: 1058 }}>
                    <Form.ItemGroup gridProps={{ row: { gutter: 40 }, col: { span: 8 } }}>
                        <Form.Item name="logoSemiDark" label={i18n.t('name', 'Logo-混合风格')}>
                            <Upload
                                disabled={operateType === 'edit'}
                                name="logoSemiDark"
                                listType="picture-card"
                                accept="image/png"
                                showUploadList={false}
                                beforeUpload={(file) => handleBeforeUpload(file)}
                                customRequest={customRequest('logoSemiDark')}
                                onChange={(file) => handleChange(file, 'logoSemiDark')}
                            >
                                {logoUrlInfo?.logoSemiDarkUrl ? (
                                    <div className="image-wrapper">
                                        <img
                                            src={logoUrlInfo.logoSemiDarkUrl}
                                            alt="logo"
                                            style={{ width: '100%', height: '100%' }}
                                        />
                                        {operateType === 'add' ? (
                                            <div className="delete-icon-wrapper">
                                                <span
                                                    className="delete-icon"
                                                    onClick={(e: React.MouseEvent) => {
                                                        e.stopPropagation();
                                                        setLogoUrlInfo({
                                                            logoSemiDarkUrl: undefined,
                                                        });
                                                        form.setFieldsValue({
                                                            logoSemiDark: undefined,
                                                        });
                                                    }}
                                                >
                                                    <IconDelete />
                                                </span>
                                            </div>
                                        ) : null}
                                    </div>
                                ) : (
                                    ImageUploadBtn('logoSemiDark')
                                )}
                            </Upload>
                            <div className="image-tips">
                                {i18n.t('message', '只支持png格式，小于200k')}
                            </div>
                        </Form.Item>
                        <Form.Item name="logoLight" label={i18n.t('name', 'Logo-浅色风格')}>
                            <Upload
                                name="logoLight"
                                listType="picture-card"
                                accept="image/png"
                                disabled={operateType === 'edit'}
                                showUploadList={false}
                                beforeUpload={(file) => handleBeforeUpload(file)}
                                customRequest={customRequest('logoLight')}
                                onChange={(file) => handleChange(file, 'logoLight')}
                            >
                                {logoUrlInfo?.logoLightUrl ? (
                                    <div className="image-wrapper">
                                        <img
                                            src={logoUrlInfo.logoLightUrl}
                                            alt="logo"
                                            style={{ width: '100%', height: '100%' }}
                                        />
                                        {operateType === 'add' ? (
                                            <div className="delete-icon-wrapper">
                                                <span
                                                    className="delete-icon"
                                                    onClick={(e: React.MouseEvent) => {
                                                        e.stopPropagation();
                                                        setLogoUrlInfo({
                                                            logoLightUrl: undefined,
                                                        });
                                                        form.setFieldsValue({
                                                            logoLight: undefined,
                                                        });
                                                    }}
                                                >
                                                    <IconDelete />
                                                </span>
                                            </div>
                                        ) : null}
                                    </div>
                                ) : (
                                    ImageUploadBtn('logoLight')
                                )}
                            </Upload>
                            <div className="image-tips">
                                {i18n.t('message', '只支持png格式，小于200k')}
                            </div>
                        </Form.Item>
                        {isShowDark ? (
                            <Form.Item name="logoDark" label={i18n.t('name', 'Logo-深色风格')}>
                                <Upload
                                    name="logoDark"
                                    listType="picture-card"
                                    accept="image/png"
                                    disabled={operateType === 'edit'}
                                    showUploadList={false}
                                    beforeUpload={(file) => handleBeforeUpload(file)}
                                    customRequest={customRequest('logoDark')}
                                    onChange={(file) => handleChange(file, 'logoDark')}
                                >
                                    {logoUrlInfo?.logoDarkUrl ? (
                                        <div className="image-wrapper">
                                            <img
                                                src={logoUrlInfo.logoDarkUrl}
                                                alt="logo"
                                                style={{ width: '100%', height: '100%' }}
                                            />
                                            {operateType === 'add' ? (
                                                <div className="delete-icon-wrapper">
                                                    <span
                                                        className="delete-icon"
                                                        onClick={(e: React.MouseEvent) => {
                                                            e.stopPropagation();
                                                            setLogoUrlInfo({
                                                                logoDarkUrl: undefined,
                                                            });
                                                            form.setFieldsValue({
                                                                logoDark: undefined,
                                                            });
                                                        }}
                                                    >
                                                        <IconDelete />
                                                    </span>
                                                </div>
                                            ) : null}
                                        </div>
                                    ) : (
                                        ImageUploadBtn('logoDark')
                                    )}
                                </Upload>
                                <div className="image-tips">
                                    {i18n.t('message', '只支持png格式，小于200k')}
                                </div>
                            </Form.Item>
                        ) : null}
                    </Form.ItemGroup>
                </Form>
            </div>
        </Spin>
    );
};