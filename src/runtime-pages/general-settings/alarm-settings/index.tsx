/*
 * @LastEditTime: 2024-12-06 14:09:34
 */
import { useEffect, useState } from 'react';
import { Modal, Tabs } from '@streamax/poppy';
import { StarryCard, StarryBreadcrumb, StarryModal } from '@base-app/runtime-lib'; // 使用公共组件
import { i18n, useUrlSearchStore, Auth, RouterPrompt } from '@base-app/runtime-lib';
import LevelSetting from './components/LevelSetting';
import TypeSetting from './components/TypeSetting';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageTabs, PageBase } from '@/types/pageReuse/pageReuseBase';
import { getCustomJsx, isShowBreadCrumb } from '@/utils/pageReuse';
import { useAppList } from '@/hooks';
import { setDefaultTabKey } from '@/utils/commonFun';
import './index.less';
// 定制项
export type AlarmSettingShareProps = PageTabs & PageBase;

const { TabPane } = Tabs;
export const tabKeys = {
    levelSetting: 'levelSetting',
    typeSetting: 'typeSetting',
};

const AlarmSetting = (props: AlarmSettingShareProps) => {
    /***定制项** */
    const { getPageTabs, children, showBreadCrumb } = props;
    // 过滤bp和运维应用
    const { inSaaS, appList, loaded } = useAppList({}, [0, 66666]);
    const [tabActiveKey, setTabActiveKey] = useState<string>(tabKeys.levelSetting);
    const searchStore = useUrlSearchStore();
    const [when, setWhen] = useState(false);
    const openIsWhen = (flag: boolean) => {
        setWhen(flag);
    };
    useEffect(() => {
        setDefaultTabKey([alarmLevelSetting, alarmTypeSetting], setTabActiveKey);
        const { tabVal } = searchStore.get();
        tabVal && setTabActiveKey(tabVal);
    }, []);
    const tabsChange = (val: string) => {
        if (when && (tabActiveKey == tabKeys.levelSetting || tabActiveKey == tabKeys.typeSetting)) {
            setWhen(false);
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setTabActiveKey(val);
                    searchStore.set({
                        tabVal: val,
                    });
                },
                onCancel() {
                    setWhen(true);
                    setTabActiveKey(tabActiveKey);
                },
            });
        } else {
            setWhen(false);
            setTabActiveKey(val);
            searchStore.set({
                tabVal: val,
            });
        }
    };
    const alarmLevelSetting = Auth.check(
        '@base:@page:alarm.setting@action:tab.alarm.level',
    ) && (
        <TabPane tab={i18n.t('name', '报警等级')} key={tabKeys.levelSetting}>
            <LevelSetting
                activeTab={tabActiveKey}
                openIsWhen={openIsWhen}
                appList={appList}
                inSaaS={inSaaS}
                loaded={loaded}
            />
        </TabPane>
    );
    const alarmTypeSetting = Auth.check(
        '@base:@page:alarm.setting@action:tab.alarm.category',
    ) && (
        <TabPane tab={i18n.t('name', '报警分类')} key={tabKeys.typeSetting}>
            <TypeSetting
                activeTab={tabActiveKey}
                openIsWhen={openIsWhen}
                appList={appList}
                inSaaS={inSaaS}
                loaded={loaded}
            />
        </TabPane>
    );
    const pageCard = (
        <>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <div className="alarm-settings-container">
                    <Tabs activeKey={tabActiveKey} onChange={tabsChange}>
                        {getCustomJsx(getPageTabs, [alarmLevelSetting, alarmTypeSetting], {
                            tabActiveKey,
                        })}
                    </Tabs>
                    {children}
                </div>
            </StarryCard>
        </>
    );

    return isShowBreadCrumb(showBreadCrumb) ? (
        <StarryBreadcrumb>{pageCard}</StarryBreadcrumb>
    ) : (
        pageCard
    );
};
export default withShareRootHOC(AlarmSetting);
