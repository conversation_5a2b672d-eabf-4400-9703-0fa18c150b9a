import {
    Button,
    Form,
    Input,
    message,
    Select,
    Space,
    Spin,
    Table,
    Tooltip,
} from '@streamax/poppy';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import {
    ListDataContainerProps,
    RefListDataContainerProps,
} from '@streamax/starry-components/lib/list-data-container';
import {
    Action,
    Auth,
    getAppGlobalData,
    i18n,
    StarryBreadcrumb,
    StarryCard,
    StarryModal,
    useAppList,
    utils,
    useUrlSearchStore,
    StarryAbroadFormItem,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import {
    IcListEditFill,
    IconAdd,
    IconDeleteFill,
    IconReplaceFill,
    IconRequestFill,
} from '@streamax/poppy-icons';
import { useEffect, useRef, useState } from 'react';
import { useUpdateEffect } from '@streamax/hooks';
import { TableProps } from '@streamax/poppy/lib/table';
import {
    DictionaryTypeEnum,
    EnumDetailItem,
    fetchEnumPage,
    removeEnum,
    replaceEnum,
    VehicleBusinessTypeEnum,
} from '@/service/vehicle';
import VehicleTypeImage from '@/components/VehicleTypeImage';
import { useHistory } from '@base-app/runtime-lib/core';
import { getImgUrls } from '@/utils/commonFun';
const defaultCar = require('@/assets/images/icon_map_car_current_dark.png');
import './index.scoped.less';
import { Instances, ListPageQueryForm, ListPageTableBase } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems, getCustomJsx, getTableIconBtns, runCustomFun } from '@/utils/pageReuse';
import { withShareRootHOC } from '@streamax/page-sharing-core';
/**列表页table定制复用 */
export type VehicleTypeManageShareProps = ListPageTableBase & ListPageQueryForm & Instances;
const VehicleTypeManage = (props: VehicleTypeManageShareProps) => {
    /** 定制项 */
    const {
        getColumnSetting,
        getColumns,
        getTableLeftRender,
        getIconBtns,
        injectSearchList,
        getQueryForm,
        getInstances
    } = props;
    /***定制结束***/
    const [form] = Form.useForm();
    const history = useHistory();
    const searchStore = useUrlSearchStore();
    const listDataContainerRef = useRef<RefListDataContainerProps>(null);
    const [visible, setVisible] = useState(false);
    const [spinning, setSpinning] = useState(true);
    const { inSaaS, appList, loaded } = useAppList({}, [0, 66666]);
    const selectedAppRef = useRef<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    const [vehicleTypeOptions, setVehicleTypeOptions] = useState([]);
    const [replaceItem, setReplaceItem] = useState<EnumDetailItem>({});
    const {
        pageSize = 20,
        page = 1,
        appId,
        complexSort,
        ...formQuery
    } = searchStore.get();
    useUpdateEffect(() => {
        if (loaded) {
            const searchList = {
                ...formQuery,
                page: Number(page),
                pageSize: Number(pageSize),
                appId: appId
                    ? Number(appId)
                    : appList?.[0]?.value ?? getAppGlobalData('APP_ID'),
            };
            form.setFieldsValue(searchList);
            selectedAppRef.current = appId
                ? Number(appId)
                : appList?.[0]?.value;
            listDataContainerRef.current?.loadDataSource({
                complexSort,
                page: Number(page),
                pageSize: Number(pageSize),
            });
            setSpinning(false);
            fetchVehicleType();
        }
    }, [loaded]);
    const vehicleManage = (record) => {
        history.push(`/vehicle-manage?vehicleType=${record.enumCode}`);
    };
    const getDefaultSortOrder = (columnsKey: string) => {
        if (complexSort && complexSort.indexOf(columnsKey) > -1) {
            return complexSort.indexOf('desc') > -1 ? 'descend' : 'ascend';
        }
        return null;
    };
    const fetchVehicleType = async () => {
        const { list } = await fetchEnumPage({
            appId: appId
                ? Number(appId)
                : appList?.[0]?.value ?? getAppGlobalData('APP_ID'),
            enumType: 'vehicleType',
            pageSize: 1e8,
        });
        setVehicleTypeOptions(
            (list || []).map((item) => {
                return {
                    value: Number(item.enumCode),
                    label: i18n.t(
                        `@i18n:@vehicleTypeEnum__${item.enumCode}`,
                        item.enumName,
                    ),
                };
            }),
        );
    };
    const queryForm: ListDataContainerProps['queryForm'] = {
        // @ts-ignore
        items: getCustomItems(getQueryForm, [
            inSaaS && {
                label: i18n.t('name', '归属应用'),
                name: 'appId',
                field: Select,
                fieldProps: {
                    placeholder: i18n.t('message', '请选择归属应用'),
                    options: appList,
                    showSearch: true,
                    filterOption: (inputValue: string, option: any) => {
                        return (
                            (option.label || '')
                                .toLowerCase()
                                .indexOf(
                                    (inputValue as string).toLowerCase(),
                                ) !== -1
                        );
                    },
                },
                itemProps: {
                    initialValue: appList?.[0]?.value,
                    onchange: (value: number) => {
                        selectedAppRef.current = value;
                    },
                },
            },
            {
                label: i18n.t('name', '车辆类型'),
                name: 'nameCode',
                field: Input,
                fieldProps: {
                    placeholder: i18n.t('message', '请输入车辆类型名称'),
                    maxLength: 50,
                    allowClear: true,
                },
            },
        ].filter(Boolean)),
        form,
    };

    const columns = [
        {
            title: i18n.t('name', '车辆类型名称'),
            dataIndex: 'enumName',
            ellipsis: true,
            render: (text: string, record: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {i18n.t(
                            `@i18n:@vehicleTypeEnum__${record.enumCode}`,
                            text,
                        ) || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        inSaaS && {
            title: i18n.t('name', '归属应用'),
            dataIndex: 'appId',
            ellipsis: true,
            render: (text: number) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {i18n.t(`@i18n:@app__${text}`) || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '车辆类型图标'),
            dataIndex: 'fileUrl',
            ellipsis: true,
            render: (text) => {
                return <VehicleTypeImage url={text} />;
            },
        },
        {
            title: i18n.t('name', '类型'),
            dataIndex: 'businessType',
            ellipsis: true,
            render: (text) => {
                if (text === VehicleBusinessTypeEnum.custom) {
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            {i18n.t('name', '自定义类型')}
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                }
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {i18n.t('name', '默认类型')}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '关联车辆数'),
            dataIndex: 'extendList',
            ellipsis: true,
            render: (text, record) => {
                // 扩展字段中的key是vehicleCount
                const dataItem = text?.find((item) => {
                    return item.key === 'vehicleCount';
                });
                return dataItem ? (
                    <a onClick={() => vehicleManage(record)}>
                        {dataItem?.value}
                    </a>
                ) : (
                    '-'
                );
            },
        },
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'createUserName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            ellipsis: true,
            sorter: true,
            defaultSortOrder: getDefaultSortOrder('createTime'),
            sortDirections: ['ascend', 'descend', undefined],
            render: (text: string) => {
                if (Number.isNaN(Number(text))) {
                    return '-';
                } else {
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            {utils.formator.zeroTimeStampToFormatTime(
                                Number(text),
                            ) || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                }
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'action',
            fixed: 'right',
            ellipsis: true,
            render: (text: any, record: EnumDetailItem) =>
                getOperateIcon(record),
        },
    ].filter(Boolean);
    const newColumns = getCustomItems(getColumns, columns);
    //@ts-ignore
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(newColumns, {
            storageKey: '@base:@page:vehicle.type.manage',
            disabledKeys: ['enumName', 'operate'],
            ...getColumnSetting?.(),
        });
    
    const addVehicleType = (
        <Action
            code="@base:@page:vehicle.type.manage@action:add"
            url="/vehicle-type-manage/add"
            key='addVehicleType'
        >
            <Button icon={<IconAdd />} type="primary">
                {i18n.t('name', '添加')}
            </Button>
        </Action>
    );
    const toolbar: ListDataContainerProps['toolbar'] = {
        extraLeft: (
            <Space>
                {getCustomJsx(getTableLeftRender, [addVehicleType], {})}
            </Space>
        ),
        // @ts-ignore
        extraIconBtns: getTableIconBtns(getIconBtns, [
            <TableColumnSetting key="setting" {...tableColumnSettingProps} />,
        ]),
    };

    const handleTableChange: TableProps['onChange'] = (
        pagination,
        filters,
        sorter,
    ) => {
        // @ts-ignore
        const { order, field } = sorter || {};
        let complexSort = undefined;
        if (order) {
            const sort = order.replace('end', '');
            complexSort = `${field} ${sort}`;
        }
        // todo 历史的这种交互都存在问题，得ListDataContainer出方案解决
        listDataContainerRef.current?.loadDataSource({
            complexSort,
        });
    };

    const fetchData = async (params: any) => {
        const searchStoreParams = {
            ...params,
            nameCode: params?.nameCode?.trim() ?? '',
            appId: inSaaS ? params?.appId : getAppGlobalData('APP_ID'),
        };
        searchStore.set(searchStoreParams);
        const { complexSort, ...restSearchStoreParams } = searchStoreParams;
        // 重新获取列表数据时，恢复默认都展示星号
        if (complexSort) {
            const orderList = complexSort.split(' ');
            restSearchStoreParams.orderBy = orderList?.[0];
            restSearchStoreParams.sort = orderList?.[1];
        }
        if (injectSearchList) {
            return injectSearchList({
                ...restSearchStoreParams,
                enumType: DictionaryTypeEnum.vehicleType
            });
        }
        const data = await fetchEnumPage({
            ...restSearchStoreParams,
            enumType: DictionaryTypeEnum.vehicleType,
        });
        // 拿到图片id
        const fileIds = (data.list || []).map((item) => item.fileId);
        const imageData: Record<string, string> = {};
        if (fileIds) {
            const urlData = await getImgUrls(fileIds, {
                _appId: 0,
                _tenantId: 0,
            });
            urlData.forEach((item) => {
                imageData[item.fileId] = item.fileUrl;
            });
        }
        (data.list || []).forEach((item) => {
            item.fileUrl = imageData?.[item.fileId] || defaultCar;
        });
        return data;
    };

    const handleDelete = (record: EnumDetailItem) => {
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确认要删除车辆类型“{name}”吗？', {
                name: i18n.t(
                    `@i18n:@vehicleTypeEnum__${record.enumCode}`,
                    record.enumName,
                ),
            }),
            icon: <IconRequestFill />,
            onOk: () => {
                removeEnum({
                    ids: String(record.id) ?? '',
                })
                    .then(() => {
                        message.success(i18n.t('message', '操作成功'));
                    })
                    .catch(() => {})
                    .finally(() => {
                        listDataContainerRef.current?.loadDataSource();
                        fetchVehicleType();
                        modal.destroy();
                    });
            },
        });
    };

    const getOperateIcon = (record: EnumDetailItem) => {
        const dataItem =
            (record.extendList || []).find((item) => {
                return item.key === 'vehicleCount';
            }) || {};
        const bindNumber = Number(dataItem?.value || 0);
        return (
            <Space size={20}>
                <Action
                    code="@base:@page:vehicle.type.manage@action:edit"
                    url="/vehicle-type-manage/edit"
                    params={{
                        appId: record.appId,
                        vehicleTypeId: record.id,
                        vehicleTypeCode: record.enumCode,
                    }}
                >
                    <Tooltip title={i18n.t('action', '编辑')}>
                        <a>
                            <IcListEditFill />
                        </a>
                    </Tooltip>
                </Action>
                {bindNumber ? (
                    <Auth code="@base:@page:vehicle.type.manage@action:replace">
                        <Tooltip title={i18n.t('action', '替换')}>
                            <a
                                onClick={() => {
                                    setReplaceItem(record);
                                    setVisible(true);
                                    form.setFieldsValue({
                                        oldEnumCode: record.enumCode,
                                        newEnumCode: null,
                                    });
                                }}
                            >
                                <IconReplaceFill />
                            </a>
                        </Tooltip>
                    </Auth>
                ) : (
                    <Auth code="@base:@page:vehicle.type.manage@action:delete">
                        <Tooltip title={i18n.t('action', '删除')}>
                            <a
                                onClick={() => {
                                    handleDelete(record);
                                }}
                            >
                                <IconDeleteFill />
                            </a>
                        </Tooltip>
                    </Auth>
                )}
            </Space>
        );
    };
    const handleSubmit = () => {
        form.validateFields().then((values) => {
            setLoading(true);
            replaceEnum({
                appId: values.appId,
                enumType: DictionaryTypeEnum.vehicleType,
                enumCode: values.oldEnumCode,
                toEnumCode: values.newEnumCode,
            })
                .then(() => {
                    message.success(i18n.t('message', '操作成功'));
                })
                .finally(() => {
                    listDataContainerRef.current?.loadDataSource();
                    fetchVehicleType();
                    setLoading(false);
                    setVisible(false);
                });
        });
    };
    runCustomFun(getInstances, {
        form,
        table: {
            loadDataSource: listDataContainerRef.current?.loadDataSource,
        },
    });
    return (
        <StarryBreadcrumb>
            <StarryCard>
                <Spin spinning={spinning}>
                    <ListDataContainer
                        loadDataSourceOnMount={false}
                        ref={listDataContainerRef}
                        queryForm={queryForm}
                        getDataSource={fetchData}
                        toolbar={toolbar}
                        listRender={(data) => {
                            return (
                                <Table
                                    //@ts-ignore
                                    columns={tableColumns}
                                    dataSource={data}
                                    pagination={false}
                                    rowKey="id"
                                    onChange={handleTableChange}
                                />
                            );
                        }}
                    />
                </Spin>
            </StarryCard>
            <StarryModal
                centered
                destroyOnClose
                title={i18n.t('message', '替换车辆类型')}
                visible={visible}
                confirmLoading={loading}
                maskClosable={false}
                onCancel={() => setVisible(false)}
                onOk={handleSubmit}
            >
                <Form layout="vertical" form={form}>
                    <StarryAbroadFormItem
                        label={i18n.t('name', '车辆类型名称')}
                        name="oldEnumCode"
                        required
                        rules={[{ required: true }]}
                    >
                        <Select
                            disabled
                            options={[
                                {
                                    label: i18n.t(
                                        `@i18n:@vehicleTypeEnum__${replaceItem.enumCode}`,
                                        replaceItem.enumName,
                                    ),
                                    value: replaceItem.enumCode,
                                },
                            ]}
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        label={i18n.t('name', '替换车辆类型')}
                        name="newEnumCode"
                        className="vehicle-type-replace-modal-enumCode-form"
                        required
                        rules={[{ required: true }]}
                    >
                        <Select
                            filterOption={(
                                inputValue: string,
                                option,
                            ) => {
                                return (
                                    (option?.label || '')
                                        .toLowerCase()
                                        .indexOf(
                                            (
                                                inputValue as string
                                            ).toLowerCase(),
                                        ) !== -1
                                );
                            }}
                            showSearch
                            allowClear
                            options={vehicleTypeOptions.filter(
                                (item) =>
                                    item.value !== Number(replaceItem.enumCode),
                            )}
                            placeholder={i18n.t('message', '请选择')}
                        />
                    </StarryAbroadFormItem>
                </Form>
            </StarryModal>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(VehicleTypeManage);
