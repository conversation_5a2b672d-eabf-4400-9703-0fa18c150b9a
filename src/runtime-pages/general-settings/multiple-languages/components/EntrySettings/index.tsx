import { useAppList } from '@/hooks';
import { useSubmitFn } from '@streamax/hooks';
import { Form, Input, message, Modal, Select, Space, Spin, Tooltip } from '@streamax/poppy';
import { IcListEditFill, IconImport, IconExport } from '@streamax/poppy-icons';
import { getAppGlobalData, i18n, reLoadLanguage } from '@base-app/runtime-lib';
import { StarryTable } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import moment from 'moment';
import { StarryAbroadIcon } from '@base-app/runtime-lib';
import { useEffect, useRef, useState } from 'react';
import { exportExcel } from '@/service/import-export';
import {
    editLanguageEntry,
    fetchAuthorityLanguageList,
    fetchLanguageEntryPage,
} from '@/service/language';
import { languageLabelList, validatorI18n } from '@/utils/commonFun';
import './index.less';
import {
    StarryAbroadFormItem,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import useUrlState from "@ahooksjs/use-url-state";

const { Option } = Select;

const EntryType = [
    i18n.t('name', '系统'),
    'ocx',
    'webSdk',
    'diskTool',
    'mediaClientX',
    'miniPlayer',
    i18n.t('name', '国际化词条'),
];

const storageKey = '@base:@<EMAIL>';

interface RecordType {
    id: number;
    langCode: string;
    entryType: string;
    languageList: Record<string, any>[];
}
interface ParamsType {
    appId?: number;
    page: number;
    pageSize: number;
    search?: string;
    langKey?: string;
}


interface QueryState {
    entrySettingsPage: number;
    entrySettingsPageSize: number;
    entrySettingsSearchValue?: string;
    entrySettingsAppId?: number;
}
export default () => {
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const [entryId, setEntryId] = useState<number | null>(null);
    const table = useRef<any>();
    const [form] = Form.useForm();
    const history: any = useHistory();
    const [spinState, setSpinState] = useState(false);
    const [columns, setColumns] = useState<any[]>([]);
    const [langs, setLangs] = useState([]);
    const { inSaaS, appList, loaded } = useAppList();
    const [searchForm] = Form.useForm();

    const [searchParams, setSearchParams] = useUrlState<QueryState>({
        entrySettingsPage: 1,
        entrySettingsPageSize: 20,
    },
        {
            parseOptions: {
                parseNumbers: true, // 自动转换数字
            },
        });

    useEffect(() => {
        buildColumns();
    }, []);

    useEffect(() => {
        if (!loaded) return;
        if (!columns.length) return;
        let appId;
        const {entrySettingsPageSize, entrySettingsPage, entrySettingsAppId,entrySettingsSearchValue} = searchParams;
        if (inSaaS && appList.length > 0) {
            appId= entrySettingsAppId || appList?.[0]?.value
        }
        const text = entrySettingsSearchValue ? decodeURIComponent(entrySettingsSearchValue) : undefined
        const param = {
            appId,
            page: entrySettingsPage || 1,
            pageSize: entrySettingsPageSize || 20,
            langKey: text,
        }
        searchForm.setFieldsValue(param);
        setSearchValue(text || '');
        table.current?.loadDataSource(param)
    }, [loaded,columns]);

    const edit = (data: RecordType): void => {
        setEntryId(data.id);
        setModalVisible(true);
        data.languageList.forEach((element) => {
            data[element.langType] = element.translation;
        });
        form.setFieldsValue(data);
    };

    async function buildColumns() {
        let langList = [];
        try {
            langList = await fetchAuthorityLanguageList({
                tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
            });
        } catch (error) {
            langList = [];
        }
        setLangs(langList);
        const result = [
            {
                title: i18n.t('name', '词条编码'),
                dataIndex: 'langKey',
                fixed: 'left',
                width: 200,
                render: (text: string) => {
                    return (
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {text}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            },
            {
                title: i18n.t('name', '词条名称'),
                dataIndex: 'languageName',
                width: 180,
                ellipsis: { showTitle: false },
                render: (text: string) => {
                    return (
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {text}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            },
            {
                title: i18n.t('name', '释义'),
                dataIndex: 'description',
                width: 180,
                ellipsis: { showTitle: false },
                render: (text: string) => {
                    return (
                            <StarryAbroadOverflowEllipsisContainer>
                                {text || '-'}
                            </StarryAbroadOverflowEllipsisContainer>
                    );
                },
            },
            ...(langList || []).map((item: any) => ({
                title: languageLabelList[item] || item,
                dataIndex: item,
                key: item,
                width: 180,
                ellipsis: { showTitle: false },
                render: (text: string, record: RecordType) => {
                    const index = record.languageList.findIndex(
                        (lang: any) => lang.langType === item,
                    );
                    const language = index > -1 ? record.languageList[index].translation : '';
                    // return <span title={language}>{language || '-'}</span>;
                    return (
                        <Tooltip title={language}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {language || '-'}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            })),
            {
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                fixed: 'right',
                width: 180,
                render: (text: string, record: RecordType) => {
                    return (
                        <Tooltip placement="top" title={i18n.t('action', '编辑')}>
                            <a onClick={() => edit(record)}>
                                <IcListEditFill />
                            </a>
                        </Tooltip>
                    );
                },
            },
        ];
        setColumns(result);
    }
    const fetchEntryList = async (params: ParamsType) => {
        const {appId,page,pageSize} = params;
        setSearchParams({
            entrySettingsAppId:appId,
            entrySettingsSearchValue: searchValue ? encodeURIComponent(searchValue) : undefined,
            entrySettingsPage: page,
            entrySettingsPageSize: pageSize,
        })
        const { list, total } = await fetchLanguageEntryPage({
            ...params,
            langKey: searchValue ? encodeURI(`${searchValue}`) : null,
            languageName: searchValue ? encodeURI(`${searchValue}`) : null,
        });
        return Promise.resolve({
            list,
            total,
        });
    };
    const onPressEnter = (e: any): void => {
        setSearchValue(e.target.value);
        if (e.target.value) {
            table.current.reset();
        } else {
            setImmediate(() => {
                table.current.reload();
            });
        }
    };
    const toolbar = {
        leftRender: () => {
            return (
               <>
                {!inSaaS && (
                    <Input
                        style={{ width: 280,  marginRight: '12px' }}
                        prefix
                        placeholder={i18n.t('message', '请输入词条编码或词条名称')}
                        allowClear
                        maxLength={50}
                        onPressEnter={onPressEnter}
                        onKeyPress={(e: any) => {
                            if (e.code === 'Enter') {
                                // table.current.reset();
                                // table.current.reload();
                            }
                        }}
                    />
                )}
               </>
            );
        },
        rightRender: () => (
            <>
                <Space size={16}>
                    <Tooltip title={i18n.t('name', '导入')} placement="top">
                        <span className="operator-btn" onClick={importData}>
                            <StarryAbroadIcon>
                                <IconImport />
                            </StarryAbroadIcon>
                        </span>
                    </Tooltip>
                    <Tooltip title={i18n.t('name', '导出')} placement="top">
                        <span className="operator-btn" onClick={exportData}>
                            <StarryAbroadIcon>
                                <IconExport />
                            </StarryAbroadIcon>
                        </span>
                    </Tooltip>
                </Space>
            </>
        ),
        columnSetting: {
            disabledKeys: ['operate', 'langKey'],
            storageKey,
        },
    };

    const formItems = [
        inSaaS && {
            label: i18n.t('name', '归属应用'),
            name: 'appId',
            field: Select,
            itemProps: {
                initialValue: appList?.[0]?.value,
            },
            fieldProps: {
                placeholder: i18n.t('message', '请选择归属应用'),
                options: appList,
            },
        },
        {
            label: i18n.t('name', '词条'),
            name: 'langKey',
            field: Input,
            fieldProps: {
                placeholder: i18n.t('message', '请输入词条编码或词条名称'),
                allowClear: true,
                maxLength: 50,
                onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                    setSearchValue(e.target.value),
            },
        },
    ].filter((i) => i);

    // 导入数据
    function importData() {
        history.push({
            pathname: '/multiple-languages/entry-setting-import',
        });
    }

    // 导出数据
    function exportData() {
        const headersArr = [
            {
                columnName: 'languageName',
                title: i18n.t('name', '词条名称'),
                index: 1,
            },
            {
                columnName: 'langKey',
                title: i18n.t('name', '词条编码'),
                index: 0,
            },

            {
                columnName: 'type',
                title: i18n.t('name', '词条类型'),
                index: 2,
            },
            {
                columnName: 'description',
                title: i18n.t('name', '释义'),
                index: 3,
            },
        ];

        let langColumns = [];
        const defaultLangColumns = [...(langs || [])];
        try {
            const columnSetting = JSON.parse(
                localStorage.getItem('POPPY_TABLE_COL_SETTING_KEYS') || '{}',
            )[storageKey];
            if (columnSetting) {
                // @ts-ignore
                langColumns = columnSetting.filter((key: any) => defaultLangColumns.includes(key));
            } else {
                langColumns = defaultLangColumns;
            }
        } catch (error) {
            langColumns = defaultLangColumns;
        }

        const startIndex = headersArr.length;
        langColumns.forEach((item: string, index: number) => {
            headersArr.push({
                columnName: item.replace('_', ''),
                title: languageLabelList[item],
                index: index + startIndex,
            });
        });

        const getSheetArr = () => {
            const appId = searchForm.getFieldValue('appId') || getAppGlobalData('APP_ID');
            const config = {
                excelHeaders: headersArr,
                queryParam: {
                    param: {
                        langKey: searchValue ? encodeURI(`${searchValue}`) : undefined,
                        languageName: searchValue ? encodeURI(`${searchValue}`) : undefined,
                        appId,
                    },
                },
            };
            return [
                {
                    sheetName: `Web_${appId}`,
                    ...config,
                },
                {
                    sheetName: `Entry_${appId}`,
                    ...config,
                },
                {
                    sheetName: `System_${appId}`,
                    ...config,
                },
            ];
        };

        setSpinState(true);
        const fileName = i18n.t('name', '词条');
        exportExcel({
            serviceCode: '579da25d6bff46b98cc128db19ca0c47',
            isAsync: true,
            excelType: 'XLSX',
            fileName: `${fileName}_${moment().unix()}`,
            sheetQueryParams: getSheetArr(),
        })
            .then(() => {
                message.success(i18n.t('message', '导出成功，请到个人中心中查看导出详情'));
            })
            .finally(() => setSpinState(false));
    }

    const handleCancel = () => {
        form.resetFields();
        setModalVisible(false);
    };
    const [handleOk] = useSubmitFn(async () => {
        const values = await form.validateFields();
        const languagelist: any = [];
        Object.keys(values).forEach((element) => {
            const obj = {
                langType: element,
                translation: values[element] || '',
            };
            if (element !== 'langKey' && element !== 'languageName' && element !== 'description') {
                languagelist.push(obj);
            }
        });
        const rs = await editLanguageEntry({
            id: entryId,
            languagelist,
            langKey: values.langKey,
            languageName: values.languageName,
            appId: inSaaS ? searchForm.getFieldValue('appId') : undefined,
        });
        if (rs) {
            handleCancel();
            message.success(i18n.t('message', '操作成功'));
            table.current.reload();
            await reLoadLanguage(true, true);
        } else {
            message.error(i18n.t('message', '操作失败'));
        }
    });
    const renderItem = (data: any[]) => {
        const disabledArr = ['langKey', 'entryType','description'];
        return (data || [])
            .slice(0, data.length - 1)
            .map(({ dataIndex, title }: { dataIndex: string; title: string }) => {
                if (disabledArr.includes(dataIndex)) {
                    if (dataIndex === 'entryType') {
                        return (
                            <StarryAbroadFormItem
                                key={dataIndex}
                                label={title}
                                name={dataIndex}
                                className="entry-settings-form-item"
                            >
                                <Select disabled>
                                    {EntryType.map((item, index) => {
                                        return <Option value={index}>{item}</Option>;
                                    })}
                                </Select>
                            </StarryAbroadFormItem>
                        );
                    }
                    return (
                        <StarryAbroadFormItem
                            key={dataIndex}
                            label={title}
                            name={dataIndex}
                            className="entry-settings-form-item"
                        >
                            <Input disabled />
                        </StarryAbroadFormItem>
                    );
                }
                return (
                    <StarryAbroadFormItem
                        key={dataIndex}
                        className="entry-settings-form-item"
                        label={title}
                        name={dataIndex}
                        rules={[
                            {
                                required: dataIndex === 'languageName',
                            },
                            {
                                validator: validatorI18n,
                            },
                        ]}
                    >
                        <Input allowClear />
                    </StarryAbroadFormItem>
                );
            });
    };
    return (
        <div>
            <Spin size="large" spinning={spinState}>
                <div className="entry-settings-container">
                    {columns.length > 0 && (
                        <StarryTable
                            fetchDataAfterMount={false}
                            fetchDataFunc={fetchEntryList}
                            aroundBordered
                            scroll={{ x: '100%' }}
                            ref={table}
                            rowKey="id"
                            toolbar={toolbar}
                            columns={columns}
                            {...(inSaaS
                                ? {
                                      queryProps: {
                                          items: formItems,
                                          form: searchForm,
                                          onReset: () => {
                                              setSearchValue('');
                                              return true;
                                          },
                                      },
                                  }
                                : {})}
                        />
                    )}
                    <Modal
                        centered
                        title={i18n.t('name', '编辑词条')}
                        visible={modalVisible}
                        onOk={handleOk}
                        onCancel={handleCancel}
                    >
                        <Form form={form} layout="vertical">
                            {renderItem(columns)}
                        </Form>
                    </Modal>
                </div>
            </Spin>
        </div>
    );
};
