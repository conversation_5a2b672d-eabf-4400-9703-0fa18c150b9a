import { useRef, useState } from 'react';
import { i18n, utils } from '@base-app/runtime-lib';
import { Badge, message, Upload } from '@streamax/poppy';
import { StarryBreadcrumb, StarryCard, StarryTable } from '@base-app/runtime-lib';
import { IconFileImport } from '@streamax/poppy-icons';
import { fileUploadStream } from '@/service/gss';
import { importExcel } from '@/service/import-export';
import { mainTaskByPage } from '@/service/task';
import './index.less';
import { isNull } from 'lodash';

const { zeroTimeStampToFormatTime } = utils.formator;
const { Dragger } = Upload;

export default (props: any) => {
    const { INserviceCode, taskTypes, importParams } = props.location.query;
    const tableRef = useRef<any>();
    const [uploading, setUploading] = useState(false);

    const renderBadge = (state: number) => {
        switch (state) {
            case 50:
                return <Badge status="error" text={i18n.t('state', '导入失败')} />;
            case 100:
                return <Badge status="success" text={i18n.t('state', '导入成功')} />;
            case 1:
                return <Badge status="processing" text={i18n.t('state', '导入中')} />;
            case 0:
                return <Badge status="default" text={i18n.t('state', '排队等待')} />;
            case 75:
                return <Badge status="warning" text={i18n.t('state', '重试处理中')} />;
            default:
                return <Badge status="error" text={i18n.t('state', '导入失败')} />;
        }
    };

    // const download = (record: any) => {
    //     fetchFileDownloadUrl({
    //         fileIdList: ''
    //     }).then((rs) => window.open(rs[0].fileUrl));
    // };

    // const handleDelete = (record: any) => {
    //     const modal = StarryModal.confirm({
    //         centered: true,
    //         title: i18n.t('name', '删除确认'),
    //         content: i18n.t('message', '确定要删除“{name}”吗？', { name: record.taskName }),
    //         okText: i18n.t('action', '确定'),
    //         cancelText: i18n.t('action', '取消'),
    //         icon: <IconRequest />,
    //         onOk: () => {
    //             deleteTask({
    //                 mainTaskId: record.taskId
    //             }).then(() => {
    //                 message.success(i18n.t('messgae', '操作成功'));
    //                 modal.destroy();
    //                 tableRef.current.reload({ reset: true });
    //             });
    //         },
    //     });
    // };
    const beforeUpload = (file: any) => {
        if (file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            message.error(i18n.t('message', '请上传xls或xlsx类型文件'));
            return Upload.LIST_IGNORE;
        }
        return true;
    };
    const columns: any[] = [
        {
            title: i18n.t('name', '导入文件名称'),
            dataIndex: 'fileName',
            ellipsis: true,
            render: (text: any) => text || '-',
        },
        {
            title: i18n.t('name', '导入状态'),
            dataIndex: 'taskStatus',
            ellipsis: true,
            render: (text: any) => renderBadge(text),
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'createUserName',
            ellipsis: true,
            render: (text: any) => text || '-',
        },
        {
            title: i18n.t('name', '操作时间'),
            dataIndex: 'createTime',
            ellipsis: true,
            sorter: true,
            render: (text: number) => (text && zeroTimeStampToFormatTime(text)) || '-',
        },
        {
            title: i18n.t('name', '失败原因'),
            dataIndex: 'failCode',
            key: 'failCode',
            render: (text: any) =>
                text ? (
                    <span title={i18n.t(`@base:@return__${text}`)}>
                        {i18n.t(`@base:@return__${text}`)}
                    </span>
                ) : (
                    '-'
                ),
        },
        // {
        //     title: i18n.t('name', '操作'),
        //     dataIndex: 'operate',
        //     ellipsis: true,
        //     render: (text: unknown, record: any) => {
        //         return (
        //             <Space size={20}>
        //                 {
        //                     record.taskStatus === 100 && (
        //                         <Tooltip title={i18n.t('action', '下载')}>
        //                             <IconDownload
        //                                 className="opertate-icon"
        //                                 // @ts-ignore
        //                                 onClick={() => download(record)}
        //                             />
        //                         </Tooltip>
        //                     )
        //                 }
        //                 <Tooltip title={i18n.t('action', '删除')}>
        //                     <IconDelete
        //                         className="opertate-icon"
        //                         // @ts-ignore
        //                         onClick={() => handleDelete(record)}
        //                     />
        //                 </Tooltip>
        //             </Space>
        //         )
        //     }
        // },
    ];

    const fetchList = async (params: any) => {
        return mainTaskByPage({
            ...params,
            // taskParam:sheetName,
            taskTypes: taskTypes || '6',
        }).then((rs: any) => {
            return {
                // 导入任务只有一个子任务,直接把子任务参数取出来
                list: rs.list.map((item: any) => {
                    let fileName: string;
                    try {
                        fileName = JSON.parse(item.taskParam)?.fileName;
                    } catch (error) {
                        fileName = '-';
                    }
                    return {
                        ...item,
                        fileName,
                    };
                }),
                total: rs.total,
            };
        });
    };

    const customRequest = ({ file, onSuccess, onProgress }: any) => {
        const formData = new FormData();
        formData.append('file', file);
        setUploading(true);
        fileUploadStream(formData)
            .then((res: any) => {
                onProgress({ percent: 50 });
                const importExcelParams: any = {
                    excelType:
                        file.type ===
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            ? 'XLSX'
                            : 'XLS',
                    fileId: res,
                    fileName: file.name,
                };
                if (INserviceCode) {
                    importExcelParams.serviceCode =
                        INserviceCode || '2a225713375e484ca70c130b7d4506ef'; //填 query 的参数
                } else {
                    try {
                        const { serviceName, executorHandler, taskType } = JSON.parse(importParams);
                        importExcelParams.serviceName = serviceName;
                        importExcelParams.executorHandler = executorHandler;
                        importExcelParams.taskType = taskType;
                    } catch (error) {
                        // eslint-disable-next-line no-console
                        console.log('importParams error');
                    }
                }
                return importExcel(importExcelParams);
            })
            .then((res: any) => {
                setTimeout(() => {
                    onProgress({ percent: 100 });
                    onSuccess(res, file);
                    tableRef.current.reload();
                    setUploading(false);
                }, 500);
            }).catch(error => {
                onProgress({ percent: 100 });
                onSuccess(undefined, file);
                setUploading(false);
            });
    };

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="import-container">
                    <div className="upload-box">
                        <Dragger
                            name="file"
                            withCredentials
                            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            customRequest={customRequest}
                            beforeUpload={beforeUpload}
                            maxCount={1}
                            disabled={uploading}
                        >
                            <p className="upload-drag-icon">
                                <IconFileImport />
                            </p>
                            <p className="upload-text">
                                {i18n.t('message', '点击或将文件拖拽到这里上传')}
                            </p>
                            <p className="upload-tips">
                                {i18n.t('message', '支持扩展名：.xls .xlsx ')}
                            </p>
                        </Dragger>
                    </div>
                    <p className="title">{i18n.t('name', '导入记录')}</p>
                    <StarryTable
                        fetchDataAfterMount
                        fetchDataFunc={fetchList}
                        ref={tableRef}
                        toolbar={false}
                        columns={columns}
                        rowKey="taskId"
                    />
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
