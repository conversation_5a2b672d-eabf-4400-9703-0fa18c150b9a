//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';

.ant-pagination-total-text {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
}
.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
    min-width: 24px;
    height: 24px;
    margin: 0;
    line-height: 24px;
    background: transparent;
    border-color: transparent;
}
.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
    background: transparent;
    border-color: transparent;
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 24px;
    line-height: 24px;
}
.ant-select-single .ant-select-selector .ant-select-selection-item,
.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: 24px;
}
.ant-pagination-options-quick-jumper {
    height: 24px;
    line-height: 24px;
    input {
        width: 44px;
        padding: 0 7px;
    }
}

.operation-icon {
    color: @primary-color;
    cursor: pointer;
}

.modal-scroll-show-right {
    margin-right: -16px;
    padding-right: 16px;
}
.data-index:last-child {
    margin-bottom: 0;
}
