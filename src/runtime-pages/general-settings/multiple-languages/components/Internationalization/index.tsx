import { useState, useEffect, createContext, useRef } from 'react';
import { Container, Input, Select, Tabs } from '@streamax/poppy';
import RightTable from './rightTable';
import {
    i18n,
    getAppGlobalData,
    useUrlSearchStore,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import { fetchApplicationPageList } from '@/service/application';
import { fetchAuthorityLanguageList } from '@/service/language';
import {
    fetchResourcePageList,
    fetchServicePageList,
} from '@/service/resource';
import {
    fetchAlarmLevelPage,
    fetchAlarmCategoryPage,
    getAlarmTypeAuthorityPage,
    getAlarmTypeAuthorityPageSort,
} from '@/service/alarm';
import {
    getStrategyListByPage,
    getDefaultStrategyListByPage,
    getUserStrategyListByPage,
} from '@/service/strategy';
import { fetchRoleInternationalPage } from '@/service/role';
import { getLabeIinterList } from '@/service/label';
import { useAppList } from '@/hooks';
import { MEAL } from '@/utils/constant';
import { getTenantSetMealPage } from '@/service/tenant';
import { getMealPage } from '@/service/meal';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { getCustomItems } from '@/utils/pageReuse';
import { PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getTenantTemplate } from '@/service/tenant-template';
import './index.less';
import { ChannelTypeEnum, fetchChannelTypePageList, getCurrentChannelMode } from '@/service/channel';
import { omit } from 'lodash';
import { RspDrawerTemplate } from '@streamax/responsive-layout';
import { IconSwitch02Fill } from '@streamax/poppy-icons';
import { channelMode } from '@/modules/strategy/ChannelStrategy/util';
import { DictionaryTypeEnum, fetchEnumPage } from '@/service/vehicle';
/**定制**/
export type InternationalizationShareProps = PageTabs;
const { TabPane } = Tabs;
enum TabKey {
    App = 0,
    Menu = 1,
    Page = 2,
    Operation = 3,
    Role = 4,
    AlarmLevel = 5,
    AlarmCategory = 6,
    UserStrategy = 7,
    AlarmLinkageStrategy = 10,
    EvidenceReturnStrategy = 11,
    EmailSendingStrategy = 12,
    FaceCompareStrategy = 13,
    DataCleanStrategy = 14,

    AlarmWebResponseStrategy = 16,
    PictureCaptureStrategy = 17,
    Service = 18,
    AlarmType = 19,
    Label = 20,
    Sets = 21,
    DefaultStrategy = 22,
    StoreMeal = 23,

    SubTenantTemplate = 24,
    flowSetting = 25,

    ChannelSetting = 26,
    ChannelType = 27,
    retentionPeriodSetting = 28,
    VehicleType = 29
}
/**导出导入任务id***/ 
enum InternationalEnum {
    vehicleTypeLanguageExcelExport = 1532,
    vehicleTypeLanguageExcelImport = 1531
}

export const TabsContext = createContext<Record<number, string>>({});

const Internationalization = (props: InternationalizationShareProps & {
    entrySettingsQueryKey: string[]
}) => {
    /*****定制****/
    const { getPageTabsConfigItems } = props;
    /*****定制****/
    // 切换tab时，url需要保留entrySettingsQueryKey中的参数
    const {entrySettingsQueryKey} = props

    const { get: getTab, set: setTab } = useUrlSearchStore();
    const { inSaaS, appList } = useAppList({}, [66666]);
    const [activeKey, setActiveKey] = useState(Number(getTab('tab') || 0));
    const [menus, setMenus] = useState<any[]>([]);
    const [initialObj, setInitialObj] = useState<Record<number, string>>({}); // 不同tab的默认应用id
    const drawerRef = useRef(null);
    const onMenuChange = (key: string) => {
        setActiveKey(parseInt(key));
        const cacheQuery = {};
        const allQuery = getTab()
        entrySettingsQueryKey.forEach(i=>{
            cacheQuery[i] = allQuery[i]
        })
        setTab({
            values: {
                ...cacheQuery,
                tab: key,
            },
            rewrite: true,
        });
        drawerRef?.current?.closeDrawer();
    };
    useEffect(() => {
        if (inSaaS && !appList.length) return;
        buildMenus();
    }, [appList]);
    const formValidate = (rule: any, value: string) => {
        return Promise.resolve();
    };
    const tabs = (
        <Tabs
            onTabClick={(key: string) => onMenuChange(key)}
            tabPosition={'right'}
            activeKey={activeKey.toString()}
        >
            {menus.map(({ key, title }) => {
                return (
                    <TabPane
                        key={key}
                        tab={
                            <div className="tab-pane-custom" title={title}>
                                {title}
                            </div>
                        }
                    />
                );
            })}
        </Tabs>
    );
    const selectApplication = (key: number, filterBP = false) => {
        const options = appList.filter((item) => !filterBP || item.value);
        const initialValue = appList.filter(
            (item) => !filterBP || item.value,
        )[0]?.value;
        setInitialObj((prev: Object) => ({
            ...prev,
            [key]: initialValue,
        }));
        return {
            label: i18n.t('name', '归属应用'),
            name: 'appId',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('message', '请选择归属应用'),
                options,
                filterOption: (inputValue: any, option: any) => {
                    return (
                        option.label
                            ?.toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                    );
                },
                showSearch: true,
            },
            itemProps: {
                initialValue,
            },
        };
    };

    async function buildMenus() {
        const languageType = await fetchAuthorityLanguageList({
            tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
        });
        const list = [
            {
                title: i18n.t('name', '应用'),
                key: TabKey.App,
                render: (
                    <RightTable
                        moduleName="app"
                        moduleTitle={i18n.t('message', '应用国际化')}
                        fetchData={fetchApplicationPageList}
                        entryField="applicationName"
                        langField="applicationId"
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURI(
                                      `${search} in:applicationName[app],id[app]`,
                                  )
                                : '';
                            return params;
                        }}
                        languageType={languageType}
                        itemColumns={[
                            {
                                title: i18n.t('name', '应用编码'),
                                dataIndex: 'applicationId',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '应用名称'),
                                dataIndex: 'applicationName',
                                ellipsis: true,
                                render: (text: any, record: any) => {
                                    return record.applicationName;
                                },
                            },
                        ]}
                        searchParams={[
                            {
                                label: i18n.t('name', '应用'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入应用编码或应用名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ]}
                        fileName={i18n.t('name', '应用')}
                        searchFormNumberKeys={[]}
                        disabledItems={['applicationId', 'applicationName']}
                        sheetName="Application"
                        INserviceCode="be8566a8c7ed422ab3ec9da116c511f2"
                        EXserviceCode="6434767c38aa4d4c96945f3a993fd0b5"
                        taskTypes={13}
                        EXColumns={[
                            {
                                title: i18n.t('name', '应用编码（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '应用名称'),
                                dataIndex: 'application-name',
                            },
                        ]}
                    />
                ),
            },
            {
                title: i18n.t('name', '租户模板'),
                key: TabKey.SubTenantTemplate,
                render: (
                    <RightTable
                        sheetName="tenantTemplate"
                        fileName={i18n.t('name', '租户模板')}
                        importParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'tenantTemplateImport',
                            taskType: 1515,
                        }}
                        exportParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'tenantTemplateExport',
                            taskType: 1514,
                        }}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '模板名称'),
                                dataIndex: 'templateName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        taskTypes={1515}
                        moduleName="tenantTemplate"
                        moduleTitle={i18n.t('message', '子租户模板国际化')}
                        fetchData={getTenantTemplate}
                        entryField="templateName"
                        langField="id"
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search ? search : '';
                            return params;
                        }}
                        appList={appList}
                        languageType={languageType}
                        itemColumns={[
                            {
                                title: i18n.t('name', '模板名称'),
                                dataIndex: 'templateName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName', // applicationName
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.SubTenantTemplate,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '模板'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入模板名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['templateName', 'appName']}
                        needSpliceAppId={true}
                    />
                ),
            },
            {
                title: i18n.t('name', '菜单'),
                key: TabKey.Menu,
                render: (
                    <RightTable
                        moduleName="menu"
                        moduleTitle={i18n.t('message', '菜单国际化')}
                        fetchData={fetchResourcePageList}
                        entryField="resourceName"
                        langField="resourceCode"
                        languageType={languageType}
                        defaultParams={{
                            resourceType: 1,
                        }}
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURIComponent(
                                      `${search} in:resourceName[tr],resourceCode[tr]`,
                                  )
                                : '';
                            return params;
                        }}
                        sheetName="Menu"
                        fileName={i18n.t('name', '菜单')}
                        INserviceCode="a7b6241c720e45ce839dcbf57e3a7a0e"
                        EXserviceCode="a958d32c5d1f48a59c33d27c38bb539b"
                        taskTypes={11}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '菜单编码（勿动）'),
                                dataIndex: 'resourceCode',
                            },
                            {
                                title: i18n.t('name', '菜单名称'),
                                dataIndex: 'resourceName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '菜单编码'),
                                dataIndex: 'resourceCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '菜单名称'),
                                dataIndex: 'resourceName',
                                render: (text: any, record: any) => {
                                    return record.resourceName;
                                },
                            },
                        ]}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Menu),
                            {
                                label: i18n.t('name', '菜单'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入菜单编码或菜单名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['resourceCode', 'resourceName']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '页面'),
                key: TabKey.Page,
                render: (
                    <RightTable
                        sheetName="Page"
                        fileName={i18n.t('name', '页面')}
                        INserviceCode="27f7648d89534a66ba8124f78a631bb2"
                        EXserviceCode="fb8dc5f8b57f42e08e8631cc00a1a023"
                        taskTypes={122}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '页面编码（勿动）'),
                                dataIndex: 'resourceCode',
                            },
                            {
                                title: i18n.t('name', '页面名称'),
                                dataIndex: 'resourceName',
                            },
                        ]}
                        moduleName="page"
                        moduleTitle={i18n.t('message', '页面国际化')}
                        fetchData={fetchResourcePageList}
                        entryField="resourceName"
                        langField="resourceCode"
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURIComponent(
                                      `${search} in:resourceName[tr],resourceCode[tr]`,
                                  )
                                : '';
                            return params;
                        }}
                        languageType={languageType}
                        defaultParams={{
                            resourceType: 2,
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '页面编码'),
                                dataIndex: 'resourceCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '页面名称'),
                                dataIndex: 'resourceName',
                            },
                        ]}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Page),
                            {
                                label: i18n.t('name', '页面'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入页面编码或页面名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['resourceCode', 'resourceName']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '操作'),
                key: TabKey.Operation,
                render: (
                    <RightTable
                        sheetName="Operation"
                        fileName={i18n.t('name', '操作')}
                        INserviceCode="7a9b2334ee2a4abcb53b55b4456585e7"
                        EXserviceCode="f295d16d62c84bf89b8dd645983ef989"
                        taskTypes={124}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '操作编码（勿动）'),
                                dataIndex: 'resourceCode',
                            },
                            {
                                title: i18n.t('name', '操作名称'),
                                dataIndex: 'resourceName',
                            },
                            {
                                title: i18n.t('name', '归属页面'),
                                dataIndex: 'belongPage',
                            },
                        ]}
                        moduleName="operation"
                        moduleTitle={i18n.t('message', '操作国际化')}
                        fetchData={fetchResourcePageList}
                        entryField="resourceName"
                        langField="resourceCode"
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURIComponent(
                                      `${search} in:resourceName[tr],resourceCode[tr]`,
                                  )
                                : '';
                            return params;
                        }}
                        languageType={languageType}
                        defaultParams={{
                            resourceType: 3,
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '操作编码'),
                                dataIndex: 'resourceCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '操作名称'),
                                dataIndex: 'resourceName',
                            },
                            {
                                title: i18n.t('name', '归属页面'),
                                dataIndex: 'parentName',
                                fixed: false,
                                render: (text: any, record: any) =>
                                    i18n.t(
                                        `@i18n:@page__${record.parentCode}`,
                                        text,
                                    ),
                            },
                        ]}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Operation),
                            {
                                label: i18n.t('name', '操作'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'name',
                                        '请输入操作编码或操作名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={[
                            'resourceCode',
                            'resourceName',
                            'parentName',
                            'parentId',
                        ]}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '角色'),
                key: TabKey.Role,
                render: (
                    <RightTable
                        sheetName="Role"
                        fileName={i18n.t('name', '角色')}
                        INserviceCode="03e69bc125c8427cbc1a49d216c3c13a"
                        EXserviceCode="b4f86fc5ff88433a884be7444a1467c1"
                        taskTypes={15}
                        moduleName="role"
                        moduleTitle={i18n.t('message', '角色国际化')}
                        fetchData={fetchRoleInternationalPage}
                        entryField="roleName"
                        langField="roleId"
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '角色名称'),
                                dataIndex: 'roleName',
                            },
                            {
                                title: i18n.t('name', '角色编码'),
                                dataIndex: 'roleCode',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        languageType={languageType}
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURI(`${search} in:roleName,roleCode`)
                                : '';
                            params.appId = params.appId || 0;
                            return params;
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '角色编码'),
                                dataIndex: 'roleCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '角色名称'),
                                dataIndex: 'roleName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'applicationName', // applicationName
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Role),
                            {
                                label: i18n.t('name', '角色'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入角色名称或角色编码',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={[
                            'roleCode',
                            'roleName',
                            'applicationName',
                        ]}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '报警等级'),
                key: TabKey.AlarmLevel,
                render: (
                    <RightTable
                        sheetName="Alarm Level"
                        fileName={i18n.t('name', '报警等级')}
                        INserviceCode="c5d2fa429c454555932022b971f18907"
                        EXserviceCode="dfcaab57527e4aa2baf9fd715c8eb0d8"
                        taskTypes={17}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '报警等级'),
                                dataIndex: 'alarmLevelName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        moduleName="alarmLevel"
                        moduleTitle={i18n.t('message', '报警等级国际化')}
                        fetchData={fetchAlarmLevelPage}
                        entryField="levelName"
                        langField="uniqueCode"
                        languageType={languageType}
                        filterBP={true}
                        itemColumns={[
                            {
                                title: i18n.t('name', '报警等级'),
                                dataIndex: 'levelName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.AlarmLevel, true),
                            {
                                label: i18n.t('name', '报警等级'),
                                name: 'levelName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入报警等级名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['levelName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '报警分类'),
                key: TabKey.AlarmCategory,
                render: (
                    <RightTable
                        sheetName="Alarm Category"
                        fileName={i18n.t('name', '报警分类')}
                        INserviceCode="8e1c67d59011470bab57f97e5e475b01"
                        EXserviceCode="c270f996491a4d788d27b746b04e91fe"
                        taskTypes={19}
                        moduleName="alarmCategory"
                        moduleTitle={i18n.t('message', '报警分类国际化')}
                        fetchData={(params: any) =>
                            fetchAlarmCategoryPage({ ...params })
                        }
                        entryField="categoryName"
                        langField="uniqueCode"
                        languageType={languageType}
                        filterBP={true}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '报警分类'),
                                dataIndex: 'alarmCategoryName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '报警分类'),
                                dataIndex: 'categoryName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.AlarmCategory, true),
                            {
                                label: i18n.t('name', '报警分类'),
                                name: 'categoryName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'name',
                                        '请输入报警分类名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['categoryName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            // 新增
            {
                title: i18n.t('name', '用户设置'),
                key: TabKey.UserStrategy,
                render: (
                    <RightTable
                        configureType={6}
                        sheetName="User Settings"
                        fileName={i18n.t('name', '用户设置')}
                        INserviceCode="bef5c0e69f164bb7aa99ecf75a27db79"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={56}
                        moduleName="userStrategy"
                        moduleTitle={i18n.t('message', '用户设置国际化')}
                        appList={appList}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                        ]}
                        fetchData={(params: any) =>
                            getUserStrategyListByPage({
                                ...params,
                                configureType: 6,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                        ]}
                        searchParams={[
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ]}
                        disabledItems={['configureName', 'appName', 'appId']}
                    />
                ),
            },
            {
                title: i18n.t('name', '报警通知设置'),
                key: TabKey.AlarmLinkageStrategy,
                render: (
                    <RightTable
                        configureType={2}
                        sheetName="Alarm Notification"
                        fileName={i18n.t('name', '报警通知设置')}
                        INserviceCode="dd61825fbb024f40a69f2563a6bf03fb"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={52}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        appList={appList}
                        moduleName="alarmLinkageStrategy"
                        moduleTitle={i18n.t('message', '报警通知设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 2,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        filterBP={true}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.AlarmLinkageStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '证据上传设置'),
                key: TabKey.EvidenceReturnStrategy,
                render: (
                    <RightTable
                        configureType={1}
                        sheetName="Evidence Upload"
                        fileName={i18n.t('name', '证据上传设置')}
                        INserviceCode="0d78654cece2439c8c751d038a3f64ab"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={51}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        appList={appList}
                        moduleName="evidenceReturnStrategy"
                        moduleTitle={i18n.t('message', '证据上传设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 1,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        filterBP={true}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.EvidenceReturnStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '发件邮箱设置'),
                key: TabKey.EmailSendingStrategy,
                render: (
                    <RightTable
                        configureType={3}
                        sheetName="Outbox Settings"
                        fileName={i18n.t('name', '发件邮箱设置')}
                        INserviceCode="23c10e45bd3a4603b20b4288e4ff9c0b"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={53}
                        appList={appList}
                        moduleName="emailSendingStrategy"
                        moduleTitle={i18n.t('message', '发件邮箱设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 3,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        filterBP={true}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.EmailSendingStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '人脸比对设置'),
                key: TabKey.FaceCompareStrategy,
                render: (
                    <RightTable
                        configureType={8}
                        sheetName="Face Comparison"
                        fileName={i18n.t('name', '人脸比对设置')}
                        INserviceCode="309d3af39a77488f990a006b82191d80"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={58}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        appList={appList}
                        moduleName="faceComparisonStrategy"
                        moduleTitle={i18n.t('message', '人脸比对设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 8,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        filterBP={true}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.FaceCompareStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '报警处理设置'),
                key: TabKey.AlarmWebResponseStrategy,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="alarmWebResponseStrategy"
                        moduleTitle={i18n.t('message', '报警处理设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 10,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        configureType={10}
                        sheetName="Alarm Processing"
                        fileName={i18n.t('name', '报警处理设置')}
                        INserviceCode="87c0dc60744f46f89d69a8ec0a20f9c1"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={60}
                        filterBP={true}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.AlarmWebResponseStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '图片抓拍设置'),
                key: TabKey.PictureCaptureStrategy,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="pictureCaptureStrategy"
                        moduleTitle={i18n.t('message', '图片抓拍设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 11,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        configureType={11}
                        sheetName="Picture Capture"
                        fileName={i18n.t('name', '图片抓拍设置')}
                        INserviceCode="8f98f15a4d6840679537f187f7b18d70"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={207}
                        filterBP={true}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.PictureCaptureStrategy,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '通道设置'),
                key: TabKey.ChannelSetting,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="channelSettingStrategy"
                        moduleTitle={i18n.t('message', '通道设置国际化')}
                        fetchData={async (params: any) => {
                            const data = await getCurrentChannelMode({
                                appId: params.appId,
                            });
                            const res = await getStrategyListByPage({
                                ...params,
                                configureType: channelMode[data?.channelSettingType] || 18,
                            });
                            res.configureType = channelMode[data?.channelSettingType]
                            return res;
                        }}
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        configureType={18}
                        sheetName="Channel Setting"
                        fileName={i18n.t('name', '通道设置')}
                        INserviceCode="58e49641b882df5a94b2c7426bd2a629"
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        taskTypes={1528}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.ChannelSetting, true),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '通道类型设置'),
                key: TabKey.ChannelType,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="channelType"
                        moduleTitle={i18n.t('message', '通道类型国际化')}
                        fetchData={async (params) => {
                            const { channelTypeName, ...restParams } =
                                params || {};
                            const data = await fetchChannelTypePageList({
                                ...restParams,
                                channelTypeName: encodeURIComponent(
                                    channelTypeName ?? '',
                                ),
                            });
                            const result = data?.list?.map((item) =>
                                omit(item, ['type']),
                            );
                            return {
                                ...data,
                                list: result,
                            };
                        }}
                        entryField="channelTypeName"
                        langField="typeId"
                        defaultParams={{
                            type: ChannelTypeEnum.default,
                        }}
                        languageType={languageType}
                        sheetName="Channel Type"
                        fileName={i18n.t('name', '通道类型')}
                        exportParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'channelExcelExport',
                            taskType: 1525,
                        }}
                        importParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'channelExcelImport',
                            taskType: 1526,
                        }}
                        taskTypes={1526}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '通道类型名称'),
                                dataIndex: 'channelTypeName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '通道类型名称'),
                                dataIndex: 'channelTypeName',
                                width: 200,
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.ChannelType, true),
                            {
                                label: i18n.t('name', '通道类型名称'),
                                name: 'channelTypeName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入通道类型名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        disabledItems={['channelTypeName']}
                        searchFormNumberKeys={['appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '车辆类型'),
                key: TabKey.VehicleType,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="vehicleTypeEnum"
                        moduleTitle={i18n.t('message', '车辆类型国际化')}
                        fetchData={async (params) => {
                            const data = await fetchEnumPage({
                                ...params,
                                enumType: DictionaryTypeEnum.vehicleType,
                            });
                            return data;
                        }}
                        entryField="enumCode"
                        langField="enumCode"
                        defaultParams={{
                           
                        }}
                        languageType={languageType}
                        sheetName="Vehicle Type"
                        fileName={i18n.t('name', '车辆类型')}
                        exportParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'vehicleTypeLanguageExcelExport',
                            taskType: InternationalEnum.vehicleTypeLanguageExcelExport,
                        }}
                        importParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'vehicleTypeLanguageExcelImport',
                            taskType: InternationalEnum.vehicleTypeLanguageExcelImport,
                        }}
                        taskTypes={InternationalEnum.vehicleTypeLanguageExcelImport}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '车辆类型编码（勿动）'),
                                dataIndex: 'uniqueCode',
                            },
                            {
                                title: i18n.t('name', '车辆类型名称'),
                                dataIndex: 'enumName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '车辆类型名称'),
                                dataIndex: 'enumName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.VehicleType, true),
                            {
                                label: i18n.t('name', '车辆类型'),
                                name: 'nameCode',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入车辆类型名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        disabledItems={['enumCode', 'enumName', 'appId']}
                        searchFormNumberKeys={['appId']}
                        needSpliceAppId
                        formValidate={formValidate}
                    />
                ),
            },
            {
                title: i18n.t('name', '默认设置配置'),
                key: 22,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="defaultStrategyInternational"
                        moduleTitle={i18n.t('message', '默认设置配置国际化')}
                        fetchData={(params: any) =>
                            getDefaultStrategyListByPage({ ...params })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        sheetName="Default Settings"
                        fileName={i18n.t('name', '默认设置')}
                        INserviceCode="24b29456338f4384ade2d87cbb1cd97c"
                        EXserviceCode="d44eac810e964a6da488025c16b5a982"
                        taskTypes={132}
                        filterBP={true}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置类型'),
                                dataIndex: 'strategyTypeName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置类型'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.DefaultStrategy, true),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureType',
                                field: Select,
                                fieldProps: {
                                    placeholder: i18n.t(
                                        'message',
                                        '请选择设置',
                                    ),
                                    options: [
                                        {
                                            label: i18n.t(
                                                'name',
                                                '证据上传设置',
                                            ),
                                            value: 1,
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '报警通知设置',
                                            ),
                                            value: 2,
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '人脸对比设置',
                                            ),
                                            value: 8,
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '报警处理设置',
                                            ),
                                            value: 10,
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '图片抓拍设置',
                                            ),
                                            value: 11,
                                        },
                                        {
                                            label: i18n.t('name', '通道设置'),
                                            value: 18,
                                        },
                                    ],
                                    allowClear: true,
                                    filterOption: (
                                        inputValue: any,
                                        option: any,
                                    ) => {
                                        return option.label?.includes(
                                            inputValue,
                                        );
                                    },
                                    showSearch: true,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '流量设置'),
                key: TabKey.flowSetting,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="flowLimitConfig"
                        moduleTitle={i18n.t('message', '流量设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 17,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        configureType={17}
                        sheetName="Flow Limit Config"
                        fileName={i18n.t('name', '流量设置')}
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        importParams={{
                            serviceName: 'base-business-service',
                            executorHandler: 'configureLanguageImporter',
                            taskType: 63,
                        }}
                        taskTypes={63}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(TabKey.flowSetting, true),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '保存有效期设置'),
                key: TabKey.retentionPeriodSetting,
                render: (
                    <RightTable
                        appList={appList}
                        moduleName="dataCleanStrategy"
                        moduleTitle={i18n.t('message', '保存有效期设置国际化')}
                        fetchData={(params: any) =>
                            getStrategyListByPage({
                                ...params,
                                configureType: 19,
                            })
                        }
                        entryField="configureName"
                        langField="configureId"
                        languageType={languageType}
                        configureType={19}
                        sheetName="Data Clean"
                        fileName={i18n.t('name', '保存有效期设置')}
                        EXserviceCode="761c148391fe4d1b81e2f724a0360600"
                        importParams={{
                            serviceName: 'base-business-service',
                            executorHandler: 'configureLanguageImporter',
                            taskType: 1530,
                        }}
                        taskTypes={1530}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'strategyName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        itemColumns={[
                            {
                                title: i18n.t('name', '设置名称'),
                                dataIndex: 'configureName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appId',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS &&
                                selectApplication(
                                    TabKey.retentionPeriodSetting,
                                    true,
                                ),
                            {
                                label: i18n.t('name', '设置'),
                                name: 'configureName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入设置名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['configureName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '服务'),
                key: TabKey.Service,
                render: (
                    <RightTable
                        sheetName="Service"
                        fileName={i18n.t('name', '服务')}
                        INserviceCode="0f5cc03d8ca642d49b8aadce1213b07e"
                        EXserviceCode="9f400a6c8ed945309868834105898960"
                        EXColumns={[
                            {
                                title: i18n.t('name', 'id（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '服务编码'),
                                dataIndex: 'resourcegroupCode',
                            },
                            {
                                title: i18n.t('name', '服务名称'),
                                dataIndex: 'resourcegroupName',
                            },
                        ]}
                        taskTypes={36}
                        moduleName="resourcegroup"
                        moduleTitle={i18n.t('message', '服务国际化')}
                        fetchData={fetchServicePageList}
                        entryField="resourcegroupName"
                        langField="resourcegroupCode"
                        languageType={languageType}
                        itemColumns={[
                            {
                                title: i18n.t('name', '服务编码'),
                                dataIndex: 'resourcegroupCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '服务名称'),
                                dataIndex: 'resourcegroupName',
                            },
                        ]}
                        formatReqParams={(params: any) => {
                            const search =
                                params.search && params.search.trim();
                            params.search = search
                                ? encodeURI(
                                      `${search} in:resourcegroupName,resourcegroupCode`,
                                  )
                                : '';
                            params.appId = params.appId || 0;
                            return params;
                        }}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Service),
                            {
                                label: i18n.t('name', '服务名称'),
                                name: 'search',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'name',
                                        '请输入服务名称或服务编码',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={[
                            'resourcegroupCode',
                            'resourcegroupName',
                        ]}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '报警类型'),
                key: TabKey.AlarmType,
                render: (
                    <RightTable
                        sheetName="AlarmType"
                        fileName={i18n.t('name', '报警类型')}
                        INserviceCode="d3230f11aa0a47f78377c79d2038cc35"
                        EXserviceCode="7170d0e38ec448bbadc399a8b1056dfe"
                        taskTypes={27}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '报警类型（勿动）'),
                                dataIndex: 'alarmType',
                            },
                            {
                                title: i18n.t('name', '类型编码'),
                                dataIndex: 'alarmTypeCode',
                            },
                            {
                                title: i18n.t('name', '类型名称'),
                                dataIndex: 'alarmTypeName',
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ].filter(Boolean)}
                        moduleName="alarmType"
                        moduleTitle={i18n.t('message', '报警类型国际化')}
                        fetchData={getAlarmTypeAuthorityPageSort}
                        entryField="alarmTypeName"
                        langField="alarmType"
                        sortKey={'typeName'}
                        formatReqParams={(params: any) => {
                            const typeName =
                                params.typeName && params.typeName.trim();
                            params.typeName = typeName ? typeName : '';
                            params.alarmType = typeName ? typeName : '';
                            if (!inSaaS) {
                                params.appId = getAppGlobalData('APP_ID');
                            }
                            return params;
                        }}
                        languageType={languageType}
                        defaultParams={{
                            resourceType: 3,
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '类型编码'),
                                dataIndex: 'alarmTypeCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '类型名称'),
                                dataIndex: 'alarmTypeName',
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ].filter(Boolean)}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.AlarmType, true),
                            {
                                label: i18n.t('name', '报警类型'),
                                name: 'typeName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入报警类型或者报警编码',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter(Boolean)}
                        disabledItems={['alarmTypeCode', 'alarmTypeName']}
                        searchFormNumberKeys={['appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '标签'),
                key: TabKey.Label,
                render: (
                    <RightTable
                        sheetName="Lable"
                        fileName={i18n.t('name', '标签')}
                        INserviceCode="2a8ad9fd690a492db06701380a113b68"
                        EXserviceCode="6aa3b552bfe5458b9150e3bd92993cde"
                        taskTypes={49}
                        EXColumns={[
                            {
                                title: i18n.t('name', '序列号（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '标签'),
                                dataIndex: 'labelName',
                            },
                            {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ]}
                        moduleName="label"
                        moduleTitle={i18n.t('name', '标签国际化')}
                        fetchData={getLabeIinterList}
                        entryField="labelName"
                        langField="id"
                        languageType={languageType}
                        filterBP={true}
                        itemColumns={[
                            {
                                title: i18n.t('name', '标签'),
                                dataIndex: 'labelName',
                                width: 200,
                            },
                            inSaaS && {
                                title: i18n.t('name', '归属应用'),
                                dataIndex: 'appName',
                            },
                        ].filter((item) => item)}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Label, true),
                            {
                                label: i18n.t('name', '标签'),
                                name: 'labelName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'message',
                                        '请输入标签名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['labelName', 'appName', 'appId']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '功能套餐'),
                key: TabKey.Sets,
                render: (
                    <RightTable
                        sheetName="Function Package"
                        fileName={i18n.t('name', '功能套餐')}
                        INserviceCode="0cd0573c1f674b87ba279c0c029ee9df"
                        EXserviceCode="728d9933d66f46b0b74cd4414fd66864"
                        EXColumns={[
                            {
                                title: i18n.t('name', 'id（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '套餐编码'),
                                dataIndex: 'setsCode',
                            },
                            {
                                title: i18n.t('name', '套餐名称'),
                                dataIndex: 'setsName',
                            },
                        ]}
                        taskTypes={700}
                        moduleName="sets"
                        moduleTitle={i18n.t('message', '套餐国际化')}
                        fetchData={getTenantSetMealPage}
                        entryField="setsName"
                        langField="setsId"
                        languageType={languageType}
                        defaultParams={{
                            tenantId:
                                getAppGlobalData('APP_USER_INFO').tenantId,
                            appId: appList.filter(
                                (item) => !false || item.value,
                            )[0]?.value,
                            setsTypes: '1,2', // 存储套餐
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '套餐编码'),
                                dataIndex: 'setsCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '套餐名称'),
                                dataIndex: 'setsName',
                            },
                        ]}
                        formatReqParams={(params: any) => {
                            params.attributionAppId = params.appId || 0;
                            return params;
                        }}
                        searchParams={[
                            inSaaS && selectApplication(TabKey.Sets),
                            {
                                label: i18n.t('name', '套餐名称'),
                                name: 'setsName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'name',
                                        '请输入套餐名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        searchFormNumberKeys={['appId']}
                        disabledItems={['setsCode', 'setsName']}
                        needSpliceAppId
                    />
                ),
            },
            {
                title: i18n.t('name', '存储套餐'),
                key: TabKey.StoreMeal,
                render: (
                    <RightTable
                        sheetName="Storage Package"
                        fileName={i18n.t('name', '存储套餐')}
                        importParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'storageExcelImport',
                            taskType: 1501,
                        }}
                        exportParams={{
                            serviceName: 'base-server-service',
                            executorHandler: 'storageExcelExport',
                            taskType: 1502,
                        }}
                        EXColumns={[
                            {
                                title: i18n.t('name', 'id（勿动）'),
                                dataIndex: 'id',
                            },
                            {
                                title: i18n.t('name', '套餐编码'),
                                dataIndex: 'setsCode',
                            },
                            {
                                title: i18n.t('name', '套餐名称'),
                                dataIndex: 'setsName',
                            },
                        ]}
                        taskTypes={1501}
                        moduleName="setsStorage" // 动态词条名称 后端定
                        moduleTitle={i18n.t('message', '存储套餐国际化')}
                        fetchData={getTenantSetMealPage}
                        entryField="storeMealName"
                        langField="setsId"
                        languageType={languageType}
                        defaultParams={{
                            setsTypes: [MEAL.storeMeal].join(
                                ',',
                            ), // 存储套餐
                            setsStatus: 1, // 0-停用，1-启用,
                            tenantId:
                                getAppGlobalData('APP_USER_INFO').tenantId,
                            appId: appList.filter(
                                (item) => !false || item.value,
                            )[0]?.value,
                            distinct: 1,
                        }}
                        itemColumns={[
                            {
                                title: i18n.t('name', '套餐编码'),
                                dataIndex: 'setsCode',
                                width: 200,
                            },
                            {
                                title: i18n.t('name', '套餐名称'),
                                dataIndex: 'setsName',
                            },
                        ]}
                        formatReqParams={(params: any) => {
                            params.setsNameCode = params.setsName;
                            delete params.setsName;
                            return params;
                        }}
                        searchParams={[
                            {
                                label: i18n.t('name', '套餐名称'),
                                name: 'setsName',
                                field: Input,
                                fieldProps: {
                                    allowClear: true,
                                    placeholder: i18n.t(
                                        'name',
                                        '请输入套餐名称',
                                    ),
                                    maxLength: 50,
                                },
                            },
                        ].filter((item) => item)}
                        disabledItems={['setsCode', 'setsName']}
                    />
                ),
            },
        ];
        const newList = getCustomItems(getPageTabsConfigItems, list, {
            languageType,
        });
        setMenus(newList);
    }

    return (
        <div className="internationalization-container">
            <RspDrawerTemplate breakpoint="lg" gutter={[24,24]}>
                <RspDrawerTemplate.Left
                    ref={drawerRef}
                    drawerTrigger={
                        <div>
                            <span className="rsp-drawer-title">{menus.find((item: any) => activeKey === item.key)?.title}</span>
                            <StarryAbroadIcon>
                                <IconSwitch02Fill />
                            </StarryAbroadIcon>
                        </div>
                    }
                    drawerProps={{width:'400px',className:'internationalization-container-drawer'}}
                >
                    <div className="left">{tabs}</div>
                </RspDrawerTemplate.Left>
                <RspDrawerTemplate.Right>
                <div className="right">
                <TabsContext.Provider value={initialObj}>
                    {menus.map((item: any) => {
                        return activeKey === item.key ? item.render : null;
                    })}
                </TabsContext.Provider>
            </div>
                </RspDrawerTemplate.Right>
            </RspDrawerTemplate>

        </div>
    );
};
export default withSharePropsHOC<any, InternationalizationShareProps>(
    Internationalization,
);
