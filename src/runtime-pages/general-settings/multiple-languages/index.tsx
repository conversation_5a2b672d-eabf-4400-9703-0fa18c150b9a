/*
 * @LastEditTime: 2024-08-16 17:26:45
 */
import { Auth, i18n, useUrlSearchStore } from '@base-app/runtime-lib';
import { Tabs } from '@streamax/poppy';
// @ts-ignore
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import Internationalization from './components/Internationalization';
import EntrySettings from './components/EntrySettings';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageBase } from '@/types/pageReuse/pageReuseBase';
import { useSystemComponentStyle } from '@base-app/runtime-lib';
import StarryTabs from '@/components/StarryTabs';
import './index.less';

export type MultipleLanguagesShareProps = PageBase;
const { TabPane } = Tabs;
const entrySettingsQueryKey =  [
    'entrySettingsPage',
    'entrySettingsPageSize',
    'entrySettingsSearchValue',
    'entrySettingsAppId'
]

const MultipleLanguages = (props: MultipleLanguagesShareProps) => {
    /**定制**/
    const { children } = props;
    /**定制**/
    const { isAbroadStyle } = useSystemComponentStyle();

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <StarryTabs
                    hiddenLine={isAbroadStyle}
                    className="tabs-overflow"
                >
                    {Auth.check(
                        '@base:@page:multiple.languages@action:tab.internationalization',
                    ) && (
                        <TabPane key="internationalization" tab={i18n.t('name', '国际化设置')}>
                            <Internationalization entrySettingsQueryKey={entrySettingsQueryKey}/>
                        </TabPane>
                    )}
                    {Auth.check('@base:@page:multiple.languages@action:tab.entry.settings') && (
                        <TabPane key="entry-settings" tab={i18n.t('name', '词条设置')}>
                            <EntrySettings />
                        </TabPane>
                    )}
                </StarryTabs>
                {children}
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(MultipleLanguages);
