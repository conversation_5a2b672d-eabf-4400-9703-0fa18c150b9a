import { withShareRootH<PERSON> } from '@streamax/page-sharing-core';
import { Button, Space, Input, Select, message, Form, Tooltip } from '@streamax/poppy';
import { IconDeleteFill, IconAddFill, IcListEditFill, IconCopyFill, IconRequestFill } from '@streamax/poppy-icons';
import { utils, i18n, Auth, useUrlSearchStore, StarryAbroadOverflowEllipsisContainer } from '@base-app/runtime-lib';
// @ts-ignore
import {
    StarryBreadcrumb,
    StarryCard,
    Action,
    StarryModal,
} from '@base-app/runtime-lib';
// @ts-ignore
import { StarryTable } from '@base-app/runtime-lib';
import { Link, useHistory } from '@base-app/runtime-lib/core';
import { useEffect, useRef } from 'react';
import { useAppList } from '../../../hooks';
import { fetchEmailTempPage, deleteEmailTemp } from '../../../service/email';
import { useAppSelect } from '@/hooks/useAppSelect';
import type { PageBase } from '@/types/pageReuse/pageReuseBase';
import { isShowBreadCrumb } from '@/utils/pageReuse';
import './index.less';

interface ParamsType {
    page: number;
    pageSize: number;
    categoryName?: string;
    appId?: number | null | '';
    complexSort?: string;
    templateName?: string;
    templateType?: string;
}

const { Option } = Select;

/**定制**/
export type MailTemplateShareProps = PageBase;

const MailTemplate = (props: MailTemplateShareProps) => {
    /**定制**/
    const { showBreadCrumb } = props;
    /**定制**/

    // 过滤bp和运维应用
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const history = useHistory();
    const TableRef = useRef<any>(null);
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const formQuery = searchStore.get();
    const { disableProps, onSelectChange } = useAppSelect(inSaaS);

    useEffect(() => {
        const queryParams = {
            templateName: formQuery?.templateName || null,
            templateType: formQuery?.templateType || null,
            appId: formQuery?.appId ? Number(formQuery?.appId) : appList[0]?.value,
            page: Number(formQuery?.page || 1),
            pageSize: Number(formQuery?.pageSize || 20),
        };
        form.setFieldsValue(queryParams);
        onSelectChange(formQuery?.appId ? Number(formQuery?.appId) : appList[0]?.value);
        TableRef.current.loadDataSource({
            ...queryParams,
            complexSort: formQuery?.complexSort,
        });
    }, [appList]);
    const sortArray = formQuery?.complexSort?.split(' ');

    const handleDelete = (record: any): void => {
        const { id, templateName } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            icon: <IconRequestFill />,
            content: i18n.t('message', '确认要删除“{name}”邮箱模板吗？', {
                name: templateName,
            }),
            onOk() {
                deleteEmailTemp({
                    ids: id.toString(),
                    logParams: [{ data: templateName }],
                }).then((rs: boolean) => {
                    if (rs) {
                        message.success(i18n.t('message', '操作成功'));
                        TableRef.current.reload();
                    } else {
                        message.error(i18n.t('message', '操作失败'));
                    }
                });
            },
        });
    };
    const getSortOrder = (sortField: string) => {
        if (sortArray == undefined) {
            return null;
        } else {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const [_, field, sortType] = sortArray;
            return sortArray[1] == sortField ? `${sortArray[2]}end` : null;
        }
    };
    const columns: any[] = [
        {
            title: i18n.t('name', '模板名称'),
            dataIndex: 'templateName',
            ellipsis: { showTitle: false },
            render: (text: any, record: any) => (
                <StarryAbroadOverflowEllipsisContainer>
                    <Action
                        code="@base:@page:email.template@action:detail"
                        url="/mail-template/detail/:mailTmpId"
                        params={{
                            mailTmpId: record.id,
                        }}
                        fellback={text}
                    >
                        {text}
                    </Action>
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: i18n.t('name', '模板类型'),
            dataIndex: 'templateType',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <>
                    {text === 'ALARM' && i18n.t('name', '报警邮件')}
                    {text === 'EVIDENCE' && i18n.t('name', '证据邮件')}
                    {text === 'REPORT' && i18n.t('name', '报表邮件')}
                </>
            ),
        },
        inSaaS && {
            title: i18n.t('name', '归属应用'),
            dataIndex: 'appId',
            ellipsis: { showTitle: false },
            render: (text: any, record: any) => (
                <Tooltip title={i18n.t(`@i18n:@app__${record.appId}`, record.appName)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {i18n.t(`@i18n:@app__${record.appId}`, record.appName)}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'createUserName',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            ellipsis: { showTitle: false },
            render: (text: number) => (
                <Tooltip title={utils.formator.zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {utils.formator.zeroTimeStampToFormatTime(text) || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
            sorter: true,
            defaultSortOrder: getSortOrder('createTime'),
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'updateUserName',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作时间'),
            dataIndex: 'updateTime',
            ellipsis: { showTitle: false },
            render: (text: number) => (
                <Tooltip title={utils.formator.zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {utils.formator.zeroTimeStampToFormatTime(text) || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
            sorter: true,
            defaultSortOrder: getSortOrder('updateTime'),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            width: 180,
            render: (_: any, record: any) => {
                const { id, quoteDetails = [] } = record;
                return (
                    <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Action
                            code="@base:@page:email.template@action:edit"
                            url="/mail-template/edit/base-info"
                            fellback={''}
                            params={{
                                mailTmpId: id,
                                pageType: 'edit'
                            }}
                        >
                            <Tooltip placement="top" title={i18n.t('action', '编辑')}>
                                <IcListEditFill />
                            </Tooltip>
                        </Action>
                        <Action
                            code="@base:@page:email.template@action:copy"
                            url="/mail-template/copy"
                            fellback={''}
                            params={{
                                mailTmpId: id,
                                pageType: 'copy'
                            }}
                        >
                            <Tooltip placement="top" title={i18n.t('action', '复制')}>
                                <IconCopyFill />
                            </Tooltip>
                        </Action>
                        {quoteDetails.length === 0 && (
                            <Auth code="@base:@page:email.template@action:delete">
                                <Tooltip placement="top" title={i18n.t('action', '删除')}>
                                    <a onClick={() => handleDelete(record)}>
                                        <IconDeleteFill />
                                    </a>
                                </Tooltip>
                            </Auth>
                        )}
                    </Space>
                );
            },
        },
    ].filter((item) => item);
    const getTableList = async (params: ParamsType) => {
        let requestParams = { ...params };
        if (!requestParams?.appId && inSaaS) {
            return Promise.resolve({
                list: [],
                total: 0,
            });
        }
        searchStore.set({
            ...formQuery,
            templateName: requestParams?.templateName,
            templateType: requestParams?.templateType,
            appId: requestParams?.appId,
            page: requestParams?.page,
            pageSize: requestParams?.pageSize,
        });
        if (requestParams.hasOwnProperty('complexSort')) {
            searchStore.set({
                ...searchStore.get(),
                complexSort: requestParams?.complexSort,
            });
        } else if (formQuery.complexSort) {
            requestParams = { ...requestParams, complexSort: formQuery.complexSort };
        }
        const { list, total } =
            (await fetchEmailTempPage({
                ...requestParams,
                quoteInfo: 1,
            })) || {};
        return Promise.resolve({
            list: list || [],
            total: total || 0,
        });
    };
    const goToAddTemplate = () => {
        Action.openActionUrl({
            code: '@base:@page:email.template@action:add',
            url: '/mail-template/add',
            //@ts-ignore
            history
        });
    };
    const toolbar = {
        leftRender: () => (
            <Auth code="@base:@page:email.template@action:add">
                <Button
                    type="primary"
                    icon={<IconAddFill />}
                    onClick={goToAddTemplate}
                >
                    {i18n.t('action', '新增')}
                </Button>
            </Auth>
        ),
        columnSetting: {
            disabledKeys: ['templateName', 'templateType', 'operate'],
            storageKey: '@base:@page:email.template.table',
        },
    };
    const formItems: any = [
        {
            label: i18n.t('name', '模板名称'),
            name: 'templateName',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('name', '请输入模板名称'),
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '模板类型'),
            name: 'templateType',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('name', '请选择模板类型'),
                allowClear: true,
                children: (
                    <>
                        <Option value="ALARM">{i18n.t('name', '报警邮件')}</Option>
                        <Option value="EVIDENCE">{i18n.t('name', '证据邮件')}</Option>
                        {Auth.check('@base:@page:email.template@action:select.report.mail') ? (
                            <Option value="REPORT">{i18n.t('name', '报表邮件')}</Option>
                        ) : null}
                    </>
                ),
            },
        },
        inSaaS && {
            label: i18n.t('name', '归属应用'),
            name: 'appId',
            field: Select,
            itemProps: {
                initialValue: appList?.[0]?.value,
            },
            fieldProps: {
                placeholder: i18n.t('name', '请选择归属应用'),
                children: (
                    <>
                        {appList.map((app: any) => (
                            <Option value={app.value} key={app.value}>
                                {app.label}
                            </Option>
                        ))}
                    </>
                ),
                onChange: onSelectChange,
            },
        },
    ].filter((item) => item);

    const CardDom = (
        <StarryCard>
            <StarryTable
                fetchDataAfterMount={false}
                aroundBordered
                fetchDataFunc={getTableList}
                ref={TableRef}
                queryProps={{
                    items: formItems,
                    form,
                    ...disableProps,
                }}
                pagination={{
                    defaultCurrent: Number(formQuery.page) || 1,
                    defaultPageSize: Number(formQuery.pageSize) || 20,
                }}
                rowKey="id"
                toolbar={toolbar}
                columns={columns}
            />
        </StarryCard>
    );

    return isShowBreadCrumb(showBreadCrumb) ? (
        <StarryBreadcrumb>{CardDom}</StarryBreadcrumb>
    ) : (
        CardDom
    );
};

export default withShareRootHOC(MailTemplate);
