//@ts-nocheck
import { Input, Select } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import { i18n } from '@base-app/runtime-lib';
import { SHAPE_ARR, SHAPE_STATE_ARR } from '../../constants';

export const formItems: Record<string, QueryFormProps['items']> = {
    area: [
        {
            name: 'areaName',
            label: i18n.t('name', '区域名称'),
            field: Input,
            fieldProps: {
                placeholder: i18n.t('name', '请输入区域名称'),
                maxLength: 50,
                allowClear: true,
            },
        },
        {
            label: i18n.t('name', '区域形状'),
            name: 'type',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('name', '请选择区域形状'),
                allowClear: true,
                options: SHAPE_ARR(),
            },
        },
        {
            label: i18n.t('name', '区域状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('name', '请选择区域状态'),
                allowClear: true,
                options: SHAPE_STATE_ARR(),
            },
        },
    ],
    line: [
        {
            name: 'areaName',
            label: i18n.t('name', '线路名称'),
            field: Input,
            fieldProps: {
                placeholder: i18n.t('name', '请输入线路名称'),
                maxLength: 50,
                allowClear: true,
            },
        },
        {
            label: i18n.t('name', '线路状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('name', '请选择线路状态'),
                allowClear: true,
                options: SHAPE_STATE_ARR(),
            },
        },
    ],
};
