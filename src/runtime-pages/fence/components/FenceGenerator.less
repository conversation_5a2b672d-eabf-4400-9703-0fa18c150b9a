@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.fence-generator {
    position: relative;
    width: 100%;
    height: 100%;
    &-toolbar-selector {
        position: absolute;
        top: 24px;
        left: 24px;
        z-index: 1000;
        .fence-generator-toolbar-drawer {
            height: 48px;
            padding: 0 24px;
            background-color:  #000000d9;
            border-radius: 8px;
        }
        .fence-generator-toolbar-import {
            height: 48px;
            padding: 0 16px;
            background-color: #000000d9;
            border-radius: 8px;
        }
        .selector-split-line {
            display: inline-block;
            width: 1px;
            height: 16px;
            margin: 0;
            background-color: rgba(255, 255, 255, .85);;
        }
        .selector-btn-item {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, .85);
             font-size: 20px;
            cursor: pointer;
            &.selected,
            &:hover {
                color: @primary-color;
            }
            &.disabled {
                cursor: not-allowed;
            }
            .item-name {
                margin-left: 8px;
            }
        }
    }

    &-toolbar-operator {
        position: absolute;
        top: 24px;
        right: 24px;
        z-index: 1000;
        .operator-btn-item,
        .operator-btn-item > button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 52px;
            height: 52px !important;
            font-size: 20px !important;
            border: none;
            border-radius: 50% !important;
            color: @starry-text-color-primary;
            background: @starry-bg-color-component-elevated;
            box-shadow: 0 1px 10px 0 #0000000d, 0 4px 5px 0 #00000014, 0 2px 4px -1px #0000001f;
        }
        .disabled,.disabled > button {
            color: @starry-text-color-disabled;      
        }
    }

    &-toolbar-color-picker {
        .poppy-popover-inner-content {
            padding: 10px;
        }
        .sketch-picker {
            box-shadow: none !important;
        }
    }
    .leaflet-control-container {
        .lr-control-top {
            z-index: 1000 !important;
        }
    }
}
.leaflet-pane {
    z-index: 1 !important;
}
