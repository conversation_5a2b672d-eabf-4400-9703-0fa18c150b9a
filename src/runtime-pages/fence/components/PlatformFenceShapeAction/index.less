@import '~@streamax/poppy-themes/starry/index.less';
.starry-page-fence-platform-action {
    .fence-form-wrapper {
        width: 400px;
        min-height: 672px;
        padding: 24px;
        border: 1px solid hsv(0, 0, 85%);
        .camera-power-switch-check {
            margin-left: 8px;
        }
        .icon-style-uniform-substitution > svg {
            color: #bbb;
        }
        .icon-style-uniform-substitution > svg:hover {
            color: @primary-color;
        }
    }
    .fence-generator-wrapper {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 400px;
        z-index: 1;
    }
    .fence-form-group {
        .fence-form-group-title {
            display: block;
            margin-bottom: 8px;
            color: @primary-color;
            font-weight: bold;
            font-size: 16px;
        }
    }
    .starry-card-layout {
        padding-bottom: 24px;
    }
}
