import React from 'react';
import FenceDetail from '../components/PlatformFenceDetail';
import { useParams } from '@base-app/runtime-lib/core';
import { Provider } from './Provider';

const BaseFenceDetail: React.FC = () => {
    const { shapeId, tab } = useParams<any>();
    return (
        <Provider>
            <FenceDetail eventId={shapeId} tabKey={tab || null}/>
        </Provider>
    );
};

export default BaseFenceDetail;
