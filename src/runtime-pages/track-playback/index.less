@import '~@streamax/poppy-themes/starry/index.less';
@import "~@streamax/poppy-themes/starry/abroad.less";
.travel-playback-container {
    &-header {
    }
    &-body {
        display: flex;
        justify-content: space-between;
        overflow: hidden;
        &-left {
            position: relative;
            height: 100%;
            // width: 460px;
            transition: all linear 0.3s;
            &.hidden-travel {
                width: 0;
                .travel-toggle-btn {
                    transform: translate(40px, 2px);
                }
            }
            .travel-toggle-btn {
                position: absolute;
                top: 64px;
                right: 0;
                z-index: 999;
                width: 40px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background: @starry-bg-color-container;
                border-radius: 20px;
                box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
                transform: translate(20px, -20px);
                cursor: pointer;
                transition: 0.5s;
            }
        }
        &-right {
            height: 100%;
            position: relative;
            display: flex;
            flex: 1;
            flex-direction: column;
            // width: calc(100% - 560px);
            overflow: hidden;
            .track-spining {
                position: absolute;
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                background-color: #fff;
                opacity: 0.5;
            }
        }
        .travel-list-btn{
            position: absolute;
            font-size: 40px;
        }
        // 小屏样式
        .starry-responsive-drawer-template-row{
            row-gap: 0!important;
        }
        .track-playback-drawer-trigger {
            transition: all .3s linear;
            position: absolute;
            z-index: 1000;
            left: 28px;
            top: 280px;
            &-wrap {
                width: 56px;
                transition: all 0.6s;
                &-content {
                    position: relative;
                    width: 56px;
                    height: 56px;
                    background: @starry-bg-color-component-elevated;
                    border-radius: 50%;
                    box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a,
                        0 8px 10px -5px #00000014;
                    .red-circle,
                    .bell,
                    .small-red-circle {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        margin: auto;
                        animation-duration: 0.8s;
                    }
                    .breathe-animation {
                        animation-name: breathe;
                        animation-duration: 1s;
                        animation-iteration-count: infinite;
                    }
                    .red-circle {
                        width: 40px;
                        height: 40px;
                        background: @primary-color;
                        border-radius: 50%;
                    }
                    .bell {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #ffffff;
                        font-size: 24px;
                    }
                }
            }
        }
        .track-playback-drawer-trigger-open{
            top: 540px;
        }
        // 小屏样式
    }
}
.travel-playback-container-lg{
    height: 96.5vh;
    .travel-toggle-btn{
        display: none;
    }
    .track-playback-travel{
        border-right: none;
        .track-playback-travel-title {
            width: 460px;
        }
    }
}

