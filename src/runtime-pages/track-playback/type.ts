import { FormateType } from '@/types/pageReuse/trackPlayback';
import { Key, MutableRefObject } from 'react';
import type { Map, Polyline  } from 'leaflet';
import { JSXBase } from '@/types/pageReuse/pageReuseBase';
import { UseMapLayerToolParams } from '@/types/pageReuse/mapCommonTypes';

export interface VehicleInfo {
    // 选择的车辆数据
    vehicleId: string;
    vehicleNumber?: string;
    deviceId?: string;
    authId?: string;
}

export interface SpeedDataItem {
    time: number; // 行驶时间
    speed: number; // 行驶速度
}

export interface AlarmDataItem {
    alarmId: string; // 报警id
    alarmType: string | number; // 报警类型
    alarmName: string; // 报警名称
    lat: string;
    lng: string;
    alarmTime: number; // 报警时间
    startTime: number;
    endTime: number;
}

export interface ParkDataItem {
    lat: number;
    lng: number;
    startTime: number; // 停车开始时间
    endTime: number; // 停车结束时间
    deviceId?: string;
}

export interface TimeInfo {
    selectedTime?: number; // 选择的时间点
    startTime?: number; // 截取的开始时间点
    endTime?: number; // 截取的结束时间点
}

export interface GpsItem {
    lng: number;
    lat: number;
    time: number; // gps时间
    speed?: number; // 速度
    angle?: number; // 方位
}

export interface TravelDataItem {
    mileage: number; // 行驶里程
    travelStartTime: number; // 行程开始时间
    travelEndTime: number; // 行程结束时间
    driverId: string;
    driverName: string;
    startAddr?: string;
    endAddr?: string;
    parkTime: number;
    travelTime: number;
    endFlag?: boolean; // 标记是否进行中行程
    startPos?: any;
    endPos?: any;
    isStart?: boolean;
    isEnd?: boolean;
    uuid4Coding: string;
    startTime: number;
    endTime: number;
    startTimeOfGps: number | string;
}

export interface SelectedTravelData {
    mileage: number; // 行驶里程
    travelStartTime: number; // 行程开始时间
    travelEndTime: number; // 行程结束时间
    endFlag?: boolean;
}

export interface TravelInfo {
    selectedTravelData?: SelectedTravelData;
    travelData?: TravelDataItem[];
    gpsData?: GpsItem[];
    alarmData?: AlarmDataItem[]; // 报警点数据
    parkData?: ParkDataItem[]; // 停车点数据
    speedData?: SpeedDataItem[];
    totalTime?: number;
    totalMileage?: number;
    showLastGps?: boolean;
    noTravelData?: boolean;
    isAllTravelInfo?: boolean; // 是否所有行程都展示
    hasRealtimeTravel?: boolean; // 是否包含实时行程
    isIncrementData?: boolean; // 是否包含增量更新
    gpsDataOfRisk?: GpsOfRiskItem[];
}

export interface TravelProps {
    queryTime: number[]; // 轨迹查询时间区间
    vehicleInfo: VehicleInfo;
    customFormateSeconds?: ((time: number, type: FormateType) => React.ReactNode) | undefined;
    getParkTimeBlock?: JSXBase;
    onUpdateData: (data: TravelInfo) => void;
    onSelectedTravelData: (data?: SelectedTravelData) => void;
    onSelectedTravel: (data: SelectedTravelData) => void;
}

export interface TravelRef {
    fetchTravelData: () => void;
}

export interface MapProps {
    selectedTravelData?: SelectedTravelData;
    gpsData?: GpsItem[]; // --与TravelInfo共用 修改
    alarmData?: AlarmDataItem[]; // --与TravelInfo共用 修改
    parkData?: ParkDataItem[]; // 停车点数据 // --与TravelInfo共用 修改
    selectedTime?: number; // 轨迹点时间
    playTime?: number; // 回放时间
    playState?: 'play' | 'pause' | 'stop';
    rate?: number;
    alarmTypes?: (string | number)[]; // 需要展示的报警类型
    showLastGps?: boolean;
    hasRealtimeTravel?: boolean;
    isIncrementData?: boolean;
    gpsDataOfRisk?: GpsOfRiskItem[];
    selectedRisk?: riskDataOfGpsItem;
    vehicleId: string;
    vehicleType: string;
    customFormateSeconds?: ((time: number, type: FormateType) => React.ReactNode) | undefined;
    onLoadParkData?: (params: {
        vehicleId: string;
        MapPanelParams: any;
        parkData?: ParkDataItem[];
        map?: Map | undefined;
        hasWatermark?: boolean;
        hasHeadVideo?: boolean;
    }) => void;
    onLoadAlarmData?: (params: {
        vehicleId: string;
        MapPanelParams: any;
        alarmData?: AlarmDataItem[];
        alarmTypes?: Key[];
        map?: Map | undefined;
        hasWatermark?: boolean;
        hasHeadVideo?: boolean;
    }) => void;
    getUseMapLayerOptions?: (params: UseMapLayerToolParams) => UseMapLayerToolParams;
    getLineInstance?: (params: any) => Polyline | undefined;
    onMapInstance?: (map?: Map | null) => void;
}

// 不同等级标记线数据
export interface MarkLineItem {
    alarmTypeName: string; // 风险等级描述
    alarmTypeText: string; // 风险等级描述
    color: string; // 风险等级颜色
    score: number; // 风险分值
    riskLevelNameId: string;
    riskLevelName: string;
}
export interface riskDataOfGpsItem {
    lat: number;
    lng: number;
    riskEventId: string;
    riskEventTime: number;
    score: number;
}

export interface GpsOfRiskItem {
    angle: number;
    color: string;
    gpsTime: number;
    lat: number;
    lng: number;
    score: number;
    speed: number;
}
export type seriesDataItem = riskDataOfGpsItem | SpeedDataItem;
export interface OptionItem {
    labelText?: string | number;
    label: string | number;
    value: string | number;
}
export type TimeValue = string | number | undefined;
export interface TimeValues {
    startValue?: TimeValue;
    endValue?: TimeValue;
}
export interface AppendFormData extends TimeValues {
    driverId: string;
    time: TimeValues;
}
