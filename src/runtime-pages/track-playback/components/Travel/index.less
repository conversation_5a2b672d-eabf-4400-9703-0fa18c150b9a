@import '~@streamax/poppy-themes/starry/index.less';
@import "~@streamax/poppy-themes/starry/abroad.less";
.track-playback-travel {
    height: 100%;
    max-height: 100%;
    background-color: @starry-bg-color-container;
    border-right: 1px solid @starry-border-level-1-color;
    &-title {
        height: 64px;
        font-weight: bold;
        font-size: 16px;
        line-height: 64px;
        text-align: center;
        border-bottom: 1px solid @starry-border-level-1-color;
    }
    &-list {
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
        .poppy-spin-nested-loading {
            height: 100%;
            .poppy-spin-container {
                height: 100%;
            }
        }
        .card-travel-list {
            // padding-top: 20px;
            padding-bottom: 24px;
        }
        &::-webkit-scrollbar-track {
            background-color: rgb(255, 255, 255);
        }
        &::-webkit-scrollbar {
            width: 6px;
            height: 20px;
        }
        &::-webkit-scrollbar-thumb {
            background-color: #c0c0c0;
            border-radius: 16px;
        }
    }
}
