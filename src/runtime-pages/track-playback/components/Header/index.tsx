import React, { useEffect, useState } from 'react';
import type { Moment } from 'moment';
import moment from 'moment';
import { useHistory } from '@base-app/runtime-lib/core';
import { Space, Switch } from '@streamax/poppy';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Auth, getAppGlobalData, i18n } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import DateRange from '@/components/DateRange';
import type { TimeInfo, VehicleInfo } from '../../type';
import { getTime, getInitTimeRange } from '../../constant';
import type { State } from '../PlayBar';
import './index.less';
import { disabledAfterDate } from '@/utils/commonFun';
import { RspFormLayout, RspGridLayout, useResponsiveShow } from '@streamax/responsive-layout';

const { timeFormat } = getAppGlobalData('APP_USER_CONFIG');
const TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

interface HeaderProps extends TimeInfo {
    playState: State;
    vehicleInfo: VehicleInfo;
    startTime: number;
    endTime: number;
    onHeaderChange: (
        timeRange: Moment[],
        visible: boolean,
        riskVisible: boolean,
        isTimeChange?: boolean,
    ) => void;
}

const Header: React.FC<HeaderProps> = (props) => {
    const {
        playState,
        startTime,
        endTime,
        vehicleInfo: { vehicleNumber },
        onHeaderChange,
    } = props;
    const history: any = useHistory();
    const [visible, setVisible] = useState(false);
    const [riskVisible, setRiskVisible] = useState(false);
    const [timeRange, setTimeRange] = useState<Moment[]>(getInitTimeRange());
    const show = useResponsiveShow({
        xl: true,
        xxl: true
    });
    const toggleVisible = () => {
        setRiskVisible(false);
        setVisible((val) => {
            return !val;
        });
    };
    const toggleRiskVisible = () => {
        setVisible(false);
        setRiskVisible((val) => {
            return !val;
        });
    };
    const onTimeChangeHandle = (dates: Moment[]) => {
        if (dates) setTimeRange(dates);
    };

    const goBack = () => {
        history.goBack();
    };

    useEffect(() => {
        if (startTime && endTime) {
            setTimeRange([
                moment(getTime(startTime, TIME_FORMAT)),
                moment(getTime(endTime, TIME_FORMAT)),
            ]);
        }
    }, [startTime, endTime]);

    useEffect(() => {
        onHeaderChange && onHeaderChange(timeRange, visible, riskVisible);
    }, [visible, riskVisible]);

    useEffect(() => {
        onHeaderChange && onHeaderChange(timeRange, visible, riskVisible, true);
    }, [timeRange]);

    useEffect(() => {
        if (playState === 'play') {
            // setVisible(false);
            setRiskVisible(false);
        }
    }, [playState]);

    return (
        <div className={`track-playback-header ${!show ? 'track-playback-header-xl' : ''}`}>
            <div className={`track-playback-header-title`}>
                <Space>
                    <ArrowLeftOutlined onClick={goBack} />
                    <OverflowEllipsisContainer>
                        <div className="track-playback-header-title-vehicleNumber">
                            {vehicleNumber}
                        </div>
                    </OverflowEllipsisContainer>
                </Space>
            </div>
            <div className="track-playback-header-search">
                <Space size="large">
                    <Auth code="@base:@page:track.playback@action:common.risk.manage">
                        <div className="search-switch">
                            <span
                                style={{
                                    verticalAlign: 'middle',
                                    marginRight: '12px',
                                }}
                            >
                                {i18n.t('name', '风险曲线')}
                            </span>
                            <Switch
                                size="small"
                                checked={riskVisible}
                                disabled={playState === 'play'}
                                onChange={toggleRiskVisible}
                            />
                        </div>
                    </Auth>
                    <div className="search-switch">
                        <span
                            style={{
                                verticalAlign: 'middle',
                                marginRight: '12px',
                            }}
                        >
                            {i18n.t('name', '速度曲线')}
                        </span>
                        <Switch
                            size="small"
                            checked={visible}
                            onChange={toggleVisible}
                        />
                    </div>
                    <DateRange
                        value={timeRange}
                        maxInterval={{
                            value: 7,
                            unitOfTime: 'days',
                        }}
                        pickerProps={{
                            allowClear: true,
                            showTime: {
                                hideDisabledOptions: true,
                                defaultValue: [
                                    moment('00:00:00', 'HH:mm:ss'),
                                    moment('23:59:59', 'HH:mm:ss'),
                                ],
                            },
                            separator: '~  ',
                            ranges: {
                                [`${i18n.t('action', '当天')}`]: [
                                    moment().subtract(0, 'days').startOf('day'),
                                    moment().endOf('day'),
                                ],
                                [`${i18n.t('action', '最近7天')}`]: [
                                    moment().subtract(6, 'days').startOf('day'),
                                    moment().endOf('day'),
                                ],
                            },
                            format: `${timeFormat} HH:mm`,
                            disabledDate: disabledAfterDate,
                            style:{
                                width: '100%',
                            }
                        }}
                        onChange={onTimeChangeHandle}
                    />
                </Space>
            </div>
        </div>
    );
};

export default Header;
