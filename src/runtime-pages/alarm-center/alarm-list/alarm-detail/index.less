.alarm-detail-page {
    .starry-card-layout-header-title {
        display: flex;
        width: 68%;

        .alarm-title-name {
            max-width: 1000px;
            // width: 1800px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .state-bg {
            margin-left: 12px;
            padding: 3px 8px;
            overflow: hidden;
            color: #fff;
            font-size: 12px;
            white-space: nowrap;
            text-overflow: ellipsis;
            border-radius: 4px;
            &.pending {
                background: #faad14;
            }
            &.misreport {
                background: #bfbfbf;
            }
        }
        .tag-box {
            width: ~'calc(100% - 686px)';
            margin-left: 10px;
        }
    }
    .header-icon-item > svg {
        display: inline-block;
        width: 20px;
        height: 20px;
    }
}
