@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.alarm-list-page {
    &-list-table-tag {
        display: flex;
        max-width: 100%;

        &-item {
            padding: 0px 4px;
            background-color: @primary-1;
            border: 1px solid @primary-3;
        }
    }
    .cards-wrapper {
        position: relative;
        border-bottom: 1px solid @starry-border-level-2-color;
        .card-item {
            //display: inline-block;
            //width: calc((100% - 48px) * 0.25);
            //margin-right: 16px;
            //margin-bottom: 16px;
            //&:nth-child(4n + 4) {
            //    margin-right: 0;
            //}
        }
    }
    .cards-wrapper::abroad {
        border-bottom: 0;
    }
    &-data-wrapper {
        .alarm-process-state {
            &-icon {
                display: inline-block;
                width: 6px;
                height: 6px;
                margin-right: 8px;
                background: #faad14;
                border-radius: 6px;
            }
            &-icon.processed {
                background: #d9d9d9;
            }
        }
        .alarm-list-card-layout {
            .toolbar-wrapper {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16px;
                .card-right-icons svg {
                    width: 16px;
                    height: 16px;
                }
                .card-right-icons svg::abroad {
                    width: 20px;
                    height: 20px;
                }
            }
        }
        .poppy-table-body {
            .alarm-cell-td {
                padding-bottom: 4px !important;
            }
        }
        .alarm-list-table-scroll {
            .poppy-table-sticky-scroll {
                display: none;
            }
        }
        .alarm-list-table{
            .poppy-table-sticky-scroll {
                position: fixed !important;
                bottom: 54px !important;
                z-index: 11;
            }
        }
    }
    &-pagination-wrapper {
        text-align: right;
        .alarm-list-pagination-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 15px 0;
            background-color: #fff;
            &-item {
                width: 30px;
                height: 30px;
                margin-left: 10px;
                line-height: 30px;
                text-align: center;
                border: 1px solid #cacaca;
                cursor: pointer;
                transition: 0.5s;
                &:hover {
                    color: @primary-color;
                    border-color: @primary-color;
                }
            }
            .current-page {
                color: @primary-color;
                border-color: @primary-color;
            }
            &-item-disabled {
                color: #dddddd;
                cursor: not-allowed;
                &:hover {
                    color: #dddddd;
                    border-color: #cacaca;
                }
            }
        }
        .poppy-pagination {
            // padding: 24px 0;
            background: #fff;
        }

        .alarm-list-total-pagination {
            .poppy-pagination {
                text-align: left;
                // padding: 24px 0;
                background: #fff;
            }
        }
        .transparent-bg::abroad {
            background: @starry-bg-color-layout;
            backdrop-filter: blur(6px);
        }
    }
    .poppy-table-cell-ellipsis {
        & > span {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
    .pagination-total-nums {
        color: @primary-color;
    }
    .form-wrapper-container {
        padding-bottom: 0 !important;
    }
}
