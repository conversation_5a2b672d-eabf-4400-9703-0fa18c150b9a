import Page from '@/runtime-pages/user-manage/user-detail';

export default () => {
    const getPageTabContent = (list, data) => {
        if (data.activeKey === 'vehicle') {
            return <>复写车辆内容</>;
        }
        return list;
    };
    return (
        <Page
            sharePropData={{
                DataAuth: {
                    getPageTabContent: getPageTabContent,
                },
            }}
        />
    );
};