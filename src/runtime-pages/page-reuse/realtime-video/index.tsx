/*
 * @LastEditTime: 2024-05-20 15:57:11
 */
//@ts-nocheck
import RealtimeVideoPage from '../../monitoring-center/realtime-video';
import React, { useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { Button, Select, Space } from '@streamax/poppy';
import { ItemConfig } from '@/runtime-pages/alarm-center/alarm-list/components/QueryForm';

export default () => {
    const getRightContainer = () => {
        return <div>13321</div>;
    };

    const handleLeftTreeClick = (keys: string[], deviceId?: string) => {
        console.log('handleLeftTreeClick', keys, deviceId);
    };

    const getVehicleDriver = (item, info) => {
        return item;
    };

    const getVehicleNumber = (jsx, data) => {
        if (data.type === 'fleet') return jsx;
        const { baseInfo } = data;
        const { vId, vNo, signal, hasVideo } = baseInfo;

        // vId 车辆Id
        // vNo 车牌号
        // signal 通过addLoopMethod 注入的数据
        // hasVideo 通过addLoopMethod 注入的数据
        return (
            <>
                <span>{signal}</span>
                <span>{vNo}</span>
                <span>{hasVideo}</span>
            </>
        );
    };
    const addLoopMethod = () => {
        return new Promise((resolve, reject) => {
            resolve([
                {
                    vId: "2839370380978695593",
                    signal: 'high',
                    hasVideo: 'false',
                }
            ])
        })
    }
    const getRealtimeVideoRecordModal = (recordEle: ReactNode[], recordParams: any) => {
        console.log('====[[[ getRealtimeVideoRecordModal recordParams]]]===', recordParams);
        return recordEle;
    }
    return (
        <RealtimeVideoPage
            sharePropData={{
                RealtimeVideo: {
                    onBeforeIntercom: (params) => {
                        debugger;
                        return { optId: 'yxfan' };
                    },
                    onIntercom: (params) => {
                        debugger;
                    },
                    onAfterIntercom: (params) => {
                        debugger;
                    },
                },
            }}
            getRightContainer={null}
            onLeftTreeClick={null}
            addLoopMethod={addLoopMethod}
            getVehicleDriver={getVehicleDriver}
            getVehicleNumber={getVehicleNumber}
            getRealtimeVideoRecordModal={getRealtimeVideoRecordModal}
        />
    );
};
