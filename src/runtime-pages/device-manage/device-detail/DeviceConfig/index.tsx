/*
 * @LastEditTime: 2024-07-01 09:29:52
 */
import { i18n } from '@base-app/runtime-lib';
import { Tabs } from '@streamax/poppy';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { useEffect, useRef, useState } from 'react';
import React from 'react';
import UseTime from '../UseTime';
import OtherAbility from '../OtherAbility';
import StorageSpace from '../StorageSpace';
import { storageSwitchOpen } from '@/utils/license-common';

const { TabPane } = Tabs;
export default function DeviceDetail(props: any) {
    const panels = [
        {
            key: 'use-time',
            title: i18n.t('name', '使用时长'),
            content: <UseTime />,
            rendered: false,
        },
        {
            key: 'other-ability',
            title: i18n.t('name', '额外功能'),
            content: <OtherAbility />,
            rendered: false,
        },
    ];
    storageSwitchOpen() &&
        panels.push({
            key: 'storage-space',
            title: i18n.t('name', '存储空间'),
            content: <StorageSpace />,
            rendered: false,
        });
    const [activeKey, setActiveKey] = useState(panels[0].key);
    const [contents, setContents] = useState<React.ReactNode[]>([]);
    const panelRendered = useRef({});
    useEffect(() => {
        const { key = '', content = '' } = panels.find((item) => item.key === activeKey) || {};
        if (!panelRendered.current[key]) {
            panelRendered.current[key] = true;
            setContents([
                ...contents,
                <div className="tenant-setting-content-panel" key={key}>
                    {content}
                </div>,
            ]);
        }
    }, [activeKey]);
    const handleTabClick = (key: string) => {
        const active = key === activeKey;
        !active && setActiveKey(key);
    };
    const tabs = (
        <Tabs onTabClick={(key: string) => handleTabClick(key)} tabPosition={'right'}>
            {panels.map(({ key, title }) => {
                return (
                    <TabPane
                        tab={
                            <div className="tab-pane-custom">
                                <OverflowEllipsisContainer>{title}</OverflowEllipsisContainer>
                            </div>
                        }
                        key={key}
                    />
                );
            })}
        </Tabs>
    );
    return (
        <div className="device-setting-wrapper">
            <div className="device-setting-vertical-tab-wrapper">{tabs}</div>
            <div className="device-setting-content-panel-wrapper">
                {React.Children.map(contents, (child) => {
                    if (!child) return null;
                    const style: React.CSSProperties = {};
                    // @ts-ignore
                    if (child.key !== activeKey) {
                        style.display = 'none';
                    }
                    return React.cloneElement(child as any, { style });
                })}
            </div>
        </div>
    );
}
