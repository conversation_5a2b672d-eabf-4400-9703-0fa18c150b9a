/**
 * 实时车组车辆数据，带司机信息，GPS信息，重点关注等仅仅用于实时监控
 */

 import useTransformTreeData from './useTransformTreeData';
 import { useEffect, useRef, useState } from 'react';
 import useFocusVehicles from './useFocusVehicles';
 import useRTDriverData from './useRTDriverData';
 import type { VehicleDriverMap } from './useRTDriverData';
 import useRTGPSData from './useRTGPSData';
 import type { VehicleGPSMap } from './useRTGPSData';
 import { AddLoopDataType } from '@/types/pageReuse/realtimeMonitoring';
 import useFleetVehicleData from './useFleetVehicleData';
 import useRTDeviceData, { VehicleStateMap } from './useRTDeviceData';
 import { DeviceStateType } from '@/service/vehicle';
 import { getCountFn, getSorterSetting, splitToMacroTask, asyncToMacroTask, CountFleet } from './util';
 import useRTReuseData, { tenSecond } from './useRTReuseData';
 import useLockTask from './useLockTask';
 import rtmLogger from '../../runtime-pages/monitoring-center/realtime-monitoring-old/logger';
 export interface Vehicle {
     fId: string;
     vId: string;
     vNo: string;
     vOnlineNumber: 1 | 0;
     vStates: string[];
     deviceList?: Record<string, any>[];
     createTime: number;
     vType: null | number;
     driverList: Record<string, any>[];
     gps: {
         lat: number;
         lng: number;
         ang: number;
         // 速度
         speed: number;
         gpsTime: number
     };
     driver: string;
     driverId: string;
 }
 
 export interface FocusVehicle {
     id: string;
     createTime?: number;
 }
 export interface Gps {
     // 车辆ID，Long型字符串
     vid: string;
     // 经度，单位0.000001
     lng: number;
     // 纬度，单位0.000001
     lat: number;
     // 方向，范围[0,359]
     ang: number;
 }
 export type GpsList = Gps[];
 
 const frequencyTime = Number((window as any).APP_CONFIG['realtime.monitoring.update.frequency'] || 500);
 
 // @ts-ignore
 const useRealtimeMonitoring = (
     countFleet = true,
     /**
      * 节点过滤函数，需要使用useCallback等手段进行优化，否者将引起不必要的性能开销
      */
     treeNodeFilter?: Parameters<typeof useTransformTreeData>['3'],
     getReuseDataConfig?: {
         getReuseData: AddLoopDataType;
         getLoopTime: (number: number) => number | undefined;
     },
 ) => {
     const loopTime = getReuseDataConfig?.getLoopTime?.(tenSecond) || undefined;
     const { getData } = useFleetVehicleData();
     const {
         stateConfig,
         getData: getRTDeviceData,
         getMergedData: getMergeDeviceData,
     } = useRTDeviceData(DeviceStateType.LIVE);
     const { getData: getRTDriverData, getMergeData: getMergeDriverData } = useRTDriverData();
     const { getData: getRTGPSData, start, getMergeData: getMergeGPSData } = useRTGPSData();  
     const {
         getData: getRTReuseData,
         reuseStart,
         getMergeData: getMergeReuseData,
     } = useRTReuseData(getReuseDataConfig?.getReuseData, loopTime);
 
     const countFleetRef = useRef<any>(asyncToMacroTask(getCountFn()));
     const [resultData, setResultData] = useState({
         loaded: false,
         fleetList: [] as CountFleet[],
         vehicleList: [] as Vehicle[],
         rtDeviceDataMap: {},
         onlineIds: [] as string[],
     });
     // 重点关注车辆
     const { focusVehicles, updateFocusVehicles } = useFocusVehicles();
     const getPollTime = (count: number) => {
         // 按照 1w 0.5s 标准延迟，最低0.5一次
         return Math.max((count / 10000) * frequencyTime, 500);
     };
     // 每个子任务5000项
     const mergeTaskSplitLength = 5000;
     const assignData = useRef(
         splitToMacroTask(
             async (vehicleList, rtDeviceDataMap, rtDriverDataMap, rtGPSMap, rtReuseDataMap) => {
                 const newVehicleList = [];
                 for (let index = 0; index < vehicleList.length; index++) {
                     const vehicle = vehicleList[index];
                     const vId = vehicle.vId;
                     const deviceItem = rtDeviceDataMap[vId];
                     newVehicleList.push(
                         Object.assign(
                             {},
                             vehicle,
                             getMergeGPSData(vId, rtGPSMap[vId]),
                             getMergeDeviceData(deviceItem),
                             getMergeDriverData(rtDriverDataMap[vId]),
                             getMergeReuseData(vId, rtReuseDataMap[vId]),
                         ),
                     );
                 }
                 return newVehicleList;
             },
             mergeTaskSplitLength,
         ),
     );
     const hasSetIntervalTime = useRef(false);
     const getResultData = async ()=>{
         rtmLogger.debug('poll start', Date.now());
         const { fleetList, vehicleList, changed: baseChanged } = await getData();
         // 根据车辆+车组树动态调整循环等待时间
         if(!hasSetIntervalTime.current) {
             const pollTime = getPollTime(fleetList.length + vehicleList.length);
             rtmLogger.info('更新间隔时间为：', pollTime);
             setIntervalTime(pollTime);
             hasSetIntervalTime.current = true;
         }        
         rtmLogger.debug('获取 车辆 数据', Date.now());
         const { data: rtGPSMap, changed: gpsChanged } = await getRTGPSData();
         rtmLogger.debug('获取gps数据', Date.now());
         const { data: rtDriverDataMap, changed: driverChanged } = await getRTDriverData();
         rtmLogger.debug('获取司机数据', Date.now());
         // 1211 ms
         const { data: rtDeviceDataMap, changed: deviceChanged } = await getRTDeviceData();
         rtmLogger.debug('获取设备数据', Date.now());
         const { data: rtReuseDataMap, changed: reuseChanged } = await getRTReuseData();
         rtmLogger.debug('获取复用页面数据', Date.now());
         //  gps、设备、司机都没有新数据，直接返回，不用处理
         if (!(baseChanged || gpsChanged || driverChanged || deviceChanged || reuseChanged)) {
             // 无数据更新，无需等待 打开锁即可。有数据更新就需要在 onTreeNodeGenerate 打开锁
             return false;
         }
         // 合并车辆数据
         const newVehicleList = await assignData.current(
             vehicleList,
             rtDeviceDataMap,
             rtDriverDataMap,
             rtGPSMap,
             rtReuseDataMap,
         );
         rtmLogger.debug('合并数据', Date.now());
         const onlineIds = newVehicleList.filter((i) => i?.vOnlineNumber).map((i) => i.vId as string);
         rtmLogger.debug('获取在线车辆', Date.now());
         // rtmLogger.debug('合并数据', Date.now());
         // 计算车组统计数据
         let countFleetList = [];
         if(countFleetRef.current) {
             countFleetList = await countFleetRef.current(fleetList, newVehicleList, countFleet);
         }
         rtmLogger.debug('统计数据', Date.now());
         return {
             loaded: true,
             rtDeviceDataMap,
             fleetList: countFleetList as CountFleet[],
             vehicleList: newVehicleList as Vehicle[],
             onlineIds,
         };
      };
      // 循环从各个hook组装数据，执行有锁保障每此数据都是渲染后才执行下一次
      const poll = async () => {
         // 为了避免异步问题，必须先上锁再设置值，不然会导致：设置了2次值 只上了一次锁 导致所永远打不开
         lock();
         let data: false | {
             loaded: boolean;
             rtDeviceDataMap: VehicleStateMap;
             fleetList: CountFleet[];
             vehicleList: Vehicle[];
             onlineIds: string[];
         } = false;
         try {
             data = await getResultData();
         } catch (error) {
             data = false;
         }
         if(data) {
             // 设置了 resultData 会引起树渲染，树渲染后触发 onTreeNodeGenerate， 由onTreeNodeGenerate中解锁
             setResultData(data);
             rtmLogger.debug('setResultData end', Date.now());
             rtmLogger.info('lock', Date.now());
         }else {
             unLock();
         }
      };
     const { isLock, lock, unLock, setIntervalTime } = useLockTask(poll);
     const { treeData, filteredVehicleList, loadFleets, refresh, loadedKeys, onTreeDataRender } = useTransformTreeData(
         resultData.fleetList,
         resultData.vehicleList,
         getSorterSetting,
         treeNodeFilter,
     );
     // 此函数会在子组件调用，当 treeData 渲染后调用，意味着每此 treeData 变化，子组件渲染后就会调用一次此函数
     const onTreeNodeGenerate = () => {
         // 回调会将当前 treeData 是否是数据源变化引起的状态flag返回，如果是则将数据变化任务锁打开
         const isRefresh = onTreeDataRender();
         if (isRefresh) {            
             unLock();
         }
     };
     rtmLogger.debug('useRealtimeMonitoring render', Date.now());
     return {
         isLock,
         unLock,
         onTreeNodeGenerate,
         loaded: resultData.loaded,
         treeData,
         loadFleets,
         refresh,
         loadedKeys, // 已经加载得节点
         vehicleStateConfig: stateConfig, // 车辆状态配置项
         fleetList: resultData.fleetList, // 全量车组数据
         vehicleList: resultData.vehicleList, // 全量车辆数据
         // 过滤后的车辆列表
         filteredVehicleList,
         rtDeviceDataMap: resultData.rtDeviceDataMap,
         onlineIds: resultData.onlineIds,
         focusVehicles,
         updateFocusVehicles,
         gpsUpdate: start,
         reuseStart: reuseStart,
     };
 };
 export default useRealtimeMonitoring;
 