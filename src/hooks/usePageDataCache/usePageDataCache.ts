/**
 * 用于页面数据缓存的hook
 * 主要作用是在即将跳离页面时，从各个组件搜集需要缓存的数据
 * 待返回至该页面时，当发现有需要恢复数据标识时，会将URL中缓存的数据分发至各个组件
 */
import { useRef } from 'react';
import { omit, remove } from 'lodash';
// @ts-ignore
import { StarryStorage } from '@base-app/runtime-lib';
import type { RegisterOption } from './types';

const cacheStorageKeyPrefix = '_page_cache__';
export const cacheObjectKeySplit = '__';

const usePageDataCache = () => {
    const storage = StarryStorage?.();
    const nameList = useRef<string[]>([]);
    // 存放搜集缓存数据方法
    const collectFuncMap = useRef<Record<string, RegisterOption['collect']>>({});
    // 存放分发缓存数据方法
    const distributeFuncMap = useRef<Record<string, RegisterOption['distribute']>>({});

    /**
     * 对外提供的注册搜集缓存数据和分发缓存数据方法的方法
     * @param name 注册名称（每个组件需保持唯一）
     * @param option 需要注册的方法
     */
    const _register = (name: string, option: RegisterOption) => {
        const { collect, distribute } = option;
        collectFuncMap.current[name] = collect;
        distributeFuncMap.current[name] = distribute;
        if (nameList.current.indexOf(name) === -1) {
            nameList.current.push(name);
        }
    };

    /**
     * 注销
     * @param name 需要注销的名称
     */
    const _off = (name: string) => {
        nameList.current = remove(nameList.current, (value: string) => value === name);
        collectFuncMap.current = omit(collectFuncMap.current, name);
        distributeFuncMap.current = omit(distributeFuncMap.current, name);
    };

    /**
     * 收集缓存数据
     * @param names 需要收集缓存的组件
     * @returns
     */
    const _collect = async (names?: string[]) => {
        // 把每个搜集方法对应的组件名称流传下去，方便存储数据时加上对应的组件前缀
        const funcList = (names || nameList.current)
            .map((name) => ({ func: collectFuncMap.current[name], name }))
            .filter(({ func }) => func)
            .map(({ func, name }) => ({ func: func(), name }));
        const cacheDataList = await Promise.all(funcList.map((item) => item.func));
        let obj = {};
        cacheDataList.forEach((item, index) => {
            // 给每个缓存数据的key都加上以自己注册时的组件名称前缀，防止在同一个页面中不同组件缓存了相同key的数据导致被覆盖
            const componentName = funcList[index].name;
            const prefixObj = {};
            Object.keys(item).forEach((key) => {
                const prefixKey = `${componentName}${cacheObjectKeySplit}${key}`;
                prefixObj[prefixKey] = item[key];
            });
            obj = {
                ...obj,
                ...prefixObj,
            };
        });
        storage.sessionStorage.setItem(`${cacheStorageKeyPrefix}${window.location.pathname}`, obj);
        return obj;
    };

    /**
     * 分发缓存的数据
     * @param names 需要分发缓存数据的组件
     * @returns
     */
    const _distribute = async (names?: string[]): Promise<void> => {
        const key = `${cacheStorageKeyPrefix}${window.location.pathname}`;
        const sessionData = await storage.sessionStorage.getItem(key);
        const originSessionDataStr = sessionStorage.getItem(key);
        if (!sessionData && !originSessionDataStr) return undefined;
        const originSessionData = originSessionDataStr ? JSON.parse(originSessionDataStr) : {};
        const distributeData = sessionData || originSessionData;

        // 还原原本缓存数据的key
        const getRealKeyObj = (name: string) => {
            const removedPrefixObj = {};
            Object.keys(distributeData).forEach((key) => {
                const [componentName, realKey] = key.split(cacheObjectKeySplit);
                if (componentName === name) {
                    removedPrefixObj[realKey] = distributeData[key];
                }
            });
            return removedPrefixObj;
        };
        const promiseList = (names || nameList.current)
            .map((name) => ({ func: distributeFuncMap.current[name], name }))
            .filter(({ func }) => func)
            .map(({ func, name }) => func(getRealKeyObj(name)));

        await Promise.all(promiseList);
        // TODO 退出登录清除缓存考虑
        /*****TODO 根据分发删除对应的数据***/ 
        const distributeDataNameKeys: string[] = [];
        if (Array.isArray(names)) {
            names.forEach((name) => {
                for (const key in distributeData) {
                    if (key.indexOf(name) > -1) {
                        distributeDataNameKeys.push(key);
                    }
                }
            });
        }
        distributeDataNameKeys.forEach((item) => {
            delete distributeData[item];
        });
        /*****根据分发删除对应的数据***/ 
        // storage.sessionStorage.setItem(key, distributeData);
        sessionStorage.removeItem(key);
        storage.sessionStorage.removeItem(key);
        return undefined;
    };

    const _existCache = async (name: string) => {
        const key = `${cacheStorageKeyPrefix}${window.location.pathname}`;
        const sessionData = await storage.sessionStorage.getItem(key);
        const originSessionDataStr = sessionStorage.getItem(key);
        if (!sessionData && !originSessionDataStr) return false;
        const originSessionData = originSessionDataStr ? JSON.parse(originSessionDataStr) : {};
        const data = sessionData || originSessionData;
        return (
            Object.keys(data).findIndex((p) => p.split(cacheObjectKeySplit).includes(name)) !== -1
        );
    };

    return {
        register: _register,
        off: _off,
        collect: _collect,
        distribute: _distribute,
        existCache: _existCache,
    };
};

export default usePageDataCache;
