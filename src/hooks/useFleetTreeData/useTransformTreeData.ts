/**
 * 将车组、车辆数据转为动态加载树所需数据结构
 */
import {useState,useRef, useEffect} from 'react';
import {utils} from '@base-app/runtime-lib';
import type { DataNode } from '@streamax/poppy/lib/tree';
import {FLEET_PREFIX, VEHICLE_PREFIX, UN_GROUP_FLEET_ID} from '@/const/vehicle';
import { useDebounceFn } from '@streamax/hooks';
import { getParentIds } from '../useRTData/util';
interface BaseFleet {
    fId: string;
    parentId: string;
    fName: string;
}
interface BaseVehicle {
    vId: string;
    fId: string;
    vNo: string;
}


const fleetPrefix = FLEET_PREFIX;
const vehiclePrefix = VEHICLE_PREFIX;

export const getTreeVehicleKey = (fId: string, vId: string)=>{
    return {
        key: `${vehiclePrefix}${fId}-${vId}`,
        // 后面需要转为树数据，id不能重复，所以必须加上车组id
        id: `${fId}-${vId}`,
    };
};
/**
 * 返回车辆树key对应的车辆id
 * @param key 
 * @returns vId string
 */
export const getVehicleIdByTreeKey = (key: string)=>{
    return key.split('-').pop();
};
export const getTreeFleetKey = (fId: string)=>{
    return {
        id: fId,
        key: `${fleetPrefix}${fId}`,
    };
};

export const vehicle2TreeNode = <V extends BaseVehicle>(vehicles: V[])=>{
    type TreeNode = DataNode & V;
    return vehicles.map(vehicle=>{
        const {id, key} = getTreeVehicleKey(vehicle.fId, vehicle.vId);
        return {
            ...vehicle,
            key,
            id,
            parentId: vehicle.fId,
            type: 'vehicle',
            title: vehicle.vNo,
            isLeaf: true,
        } as TreeNode;
    });
};

export const fleet2TreeNode = <F extends BaseFleet>(fleets: F[])=>{
    type TreeNode = DataNode & F;
    return fleets.map(fleet=>{
        const {id, key} = getTreeFleetKey(fleet.fId);
        return {
            ...fleet,
            id,
            key,
            type: 'fleet',
            title: fleet.fName,
            isLeaf: false,
        } as TreeNode;
    });
};
const useTransformTreeData = <F extends BaseFleet, V extends BaseVehicle>(
    fleetList: F[], 
    vehicleList: V[], 
    getSorterSetting: () => {
        sort: string[];
        order: string[];
    },
    filterVehicleCallbacks?: (item: DataNode & (F | V)) => boolean
    )=>{
    //  返回给Tree组件渲染的数据
    type FleetVehicle = (F | V);
    type TreeNode = DataNode & FleetVehicle;
    const [treeData, setTreeData] = useState<TreeNode[]>([]);
    const [loadedKeys, setLoadedKeys] = useState<string[]>([]);
    // 已加载的树数据
    const loadedData = useRef({
        vehicleList: [] as V[],
        fleetList: [] as F[],
    });    
    const generateTreeData = (fleetData: F[], vehicleData: V[]) => {
        let treeData: TreeNode[] = [...fleet2TreeNode(fleetData)];
        // 过滤
        if (typeof filterVehicleCallbacks === 'function') {
            const tmp = vehicle2TreeNode(vehicleData).filter((vehicle) =>
                filterVehicleCallbacks(vehicle),
            );
            treeData.push(...tmp);
        } else {
            treeData.push(...vehicle2TreeNode(vehicleData));
        }
        const { sort, order } =
            typeof getSorterSetting === 'function'
                ? getSorterSetting()
                : {
                      sort: [],
                      order: [],
                  };
        // arrayToTree sort order 定义反了
        treeData = utils.general.arrayToTree(treeData, {
            idFieldName: 'id',
            parentIdFieldName: 'parentId',
            order: sort.join(','),
            sort: order.join(','),
        }); 
        // 未分组放最后面
        const unGroupArray = treeData.filter((i) => i.fId === UN_GROUP_FLEET_ID);
        treeData = treeData.filter((i) => i.fId !== UN_GROUP_FLEET_ID); 
        //  车组内的车辆的顺序一定是紧跟着归属车组后面
        if (unGroupArray.length) {
            treeData = treeData.concat(unGroupArray);
        }
        return treeData;
    };
    /**
     * 加载车组节点
     * @param fIds  只接受不带前缀的fleetId
     * @returns
     */
     const loadFleets = async (fIds: string[]) => {
        if (!fIds || fIds.length === 0) return;
        // 找出要加载节点的父节点
        const IdArray = fIds.map(fId=>getParentIds(fId, fleetList)).reduce((re,item)=>re.concat(item), []);
        const ids = Array.from(new Set(IdArray));
        const newLoadedKeys = Array.from(
            new Set(loadedKeys.map((i) => i.replace(fleetPrefix, '')).concat(ids)),
        );
        const fleetData = fleetList.filter(
            (i) => newLoadedKeys.includes(i.fId) || newLoadedKeys.includes(i.parentId),
        );
        const vehicleData = vehicleList.filter((i) => newLoadedKeys.includes(i.fId));
        loadedData.current = {
            fleetList: fleetData,
            vehicleList: vehicleData,
        };
        const treeData = generateTreeData(fleetData, vehicleData);
        setTreeData(treeData);
        // 打造成Tree一样的数据
        setLoadedKeys(newLoadedKeys.map((i) => `${fleetPrefix}${i}`));
    };
    // 刷新已加载数据，用于排序规则改变后重新排序等
    const refresh = () => {
        const { fleetList, vehicleList } = loadedData.current;
        const treeData = generateTreeData(fleetList, vehicleList);
        setTreeData(treeData);
    };
    const { run: updateTreeData } = useDebounceFn(
        () => {
            //  更新状态
            loadFleets(
                loadedKeys.length > 0
                    ? loadedKeys.map((i) => i.replace(fleetPrefix, ''))
                    : fleetList.filter((i) => !i.parentId).map((i) => i.fId),
            );
        },
        { wait: 100 },
    );
    useEffect(() => {
        updateTreeData();
    }, [fleetList, vehicleList]);
    
    return {
        treeData,
        loadFleets,
        refresh,
        loadedKeys, // 已经加载得节点
    };
};
export type UseTransformTreeDataType = typeof useTransformTreeData;
export default useTransformTreeData;