@import '~@streamax/poppy-themes/starry/index.less';
.authorize-custom-user-transfer {
    display: flex;
    width: 100%;
    height: 100%;
    .operate-wrapper {
        display: flex;
        flex: 0 0 24px;
        flex-direction: column;
        justify-content: center;
        margin: 0 8px;
    }
    .custom-transfer-list {
        flex: 1;
        &-header {
            position: relative;
            display: flex;
            align-items: center;
            height: 40px;
            padding: 8px 12px 9px;
            color: rgba(0, 0, 0, 0.85);
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            border-radius: 2px 2px 0 0;
            .left-title-wrapper {
                .checkbox-wrapper {
                    margin-right: 4px;
                }
            }
            .right-title-wrapper {
                position: absolute;
                right: 12px;
            }
        }
        &-body {
            height: 100%;
            .search-form-wrapper {
                .submit-btn-wrapper {
                    .submit-btn {
                        width: 100%;
                    }
                }
                .poppy-select-selector {
                    max-height: 32px;
                    overflow: hidden;
                }
            }
        }
    }
    .left-wrapper {
        .pagination-total-nums {
            color: @primary-color;
        }
        .table-content-wrapper {
            height: calc(~'100% - 86px');
            .poppy-table-wrapper {
                height: 100%;
                .poppy-table-cell {
                    padding: 12px 16px;
                }
            }
        }
    }
    .right-wrapper {
        .custom-transfer-list-body {
            padding: 12px;
            overflow-y: auto;
            .poppy-tree-switcher {
                display: none;
            }
            .search-wrapper {
                padding-bottom: 12px;
            }
        }
    }
    .poppy-table-cell-ellipsis > span {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
