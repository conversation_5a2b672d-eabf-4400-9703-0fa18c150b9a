import React, { useState, useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { IconDelete, IconRequest } from '@streamax/poppy-icons';
import { StarryModal } from '@base-app/runtime-lib';
import TableWrapper from './TableWrapper';
import LineStationModal from './LineStationModal';
import { message, Tooltip } from '@streamax/poppy';
import { RspBasicLayout } from '@streamax/responsive-layout';

export type OperateType = 'include' | 'exclude';

interface VehicleItem {
    vehicleId: number;
    vehicleNumber: string;
    fleetName: string;
    powerId?: number;
}

interface AuthorizeVehicleValue {
    includeVehicle?: VehicleItem[];
    excludeVehicle?: VehicleItem[];
}

export interface AuthorizeVehicleProps {
    type: 'line' | 'station';
    showOperate?: boolean;
    showSearch?: boolean;
    authorizeAppId?: number | undefined;
    value?: AuthorizeVehicleValue;
    onChange?: (values: AuthorizeVehicleValue) => void;
}

const LineOrStation: React.FC<AuthorizeVehicleProps> = (props) => {
    const { showOperate, onChange, value, showSearch, authorizeAppId, type } = props;
    const { includeVehicle, excludeVehicle } = value || {};

    const [innerIncludeList, setInnerIncludeList] = useState([]);
    const [innerExcludeList, setInnerExcludeList] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [operateType, setOperateType] = useState<OperateType>('include');
    const [includeSearchValue, setIncludeSearchValue] = useState<string>();
    const [excludeSearchValue, setExcludeSearchValue] = useState<string>();

    const includeListRef = useRef();
    const excludeListRef = useRef();

    const triggerChange = (changedValue: AuthorizeVehicleValue) => {
        onChange?.(changedValue);
    };

    const handleDelete = (operateType: OperateType, record: any) => {
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            content: i18n.t('message', '确认要删除“{name}”授权吗？',{name: record.lineName || record.stationName}),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                // 这种情况是进入页面后，没有选取车组，直接点击删除（有回填数据）
                if (!includeListRef.current) {
                    // @ts-ignore
                    includeListRef.current = includeVehicle;
                }
                if (!excludeListRef.current) {
                    // @ts-ignore
                    excludeListRef.current = excludeVehicle;
                }
                if (operateType === 'include') {
                    // @ts-ignore
                    const newList = [...includeListRef.current];
                    const index = newList.findIndex((p: any) => type == 'line' ? p.lineId === record.lineId : p.stationId === record.stationId);
                    if (index !== -1) {
                        newList.splice(index, 1);
                    }
                    // @ts-ignore
                    setInnerIncludeList(newList);
                    // @ts-ignore
                    includeListRef.current = newList;
                    triggerChange({
                        includeVehicle: newList,
                        // @ts-ignore
                        excludeVehicle: excludeListRef.current,
                    });
                } else if (operateType === 'exclude') {
                    // @ts-ignore
                    const newList = [...excludeListRef.current];
                    const index = newList.findIndex((p: any) => type == 'line' ? p.lineId === record.lineId : p.stationId === record.stationId);
                    if (index !== -1) {
                        newList.splice(index, 1);
                    }
                    // @ts-ignore
                    setInnerExcludeList(newList);
                    // @ts-ignore
                    excludeListRef.current = newList;
                    triggerChange({
                        // @ts-ignore
                        includeVehicle: includeListRef.current,
                        excludeVehicle: newList,
                    });
                }
                modal.destroy();
                message.success(i18n.t('message', '删除成功'));
            },
        });
    };

    const generateColumns = (type: OperateType, lineOrStation: string) => {
        const linecolumnList = [
            {
                title: i18n.t('name', '线路编号'),
                dataIndex: 'lineCode',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '线路名称'),
                dataIndex: 'lineName',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '归属车组'),
                dataIndex: 'fleetName',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
        ];
        const stationcolumnList = [
            {
                title: i18n.t('name', '场站名称'),
                dataIndex: 'stationName',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '归属车组'),
                dataIndex: 'fleetName',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
        ];

        if (showOperate) {
            (lineOrStation == 'line' ? linecolumnList : stationcolumnList).push({
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                // @ts-ignore
                render: (text: any, record: any) => {
                    return (
                        <Tooltip title={i18n.t('action', '删除')}>
                            <IconDelete
                                className="delete-btn"
                                onClick={() => handleDelete(type, record)}
                            />
                        </Tooltip>
                    );
                },
            });
        }

        return lineOrStation == 'line' ? linecolumnList : stationcolumnList;
    };

    const handleAddVehicle = (type: OperateType) => {
        setOperateType(type);
        setModalVisible(true);
    };

    const handleSelect = (type: OperateType, selectedList: any[]) => {
        if (type === 'include') {
            // @ts-ignore
            setInnerIncludeList(selectedList);
            // @ts-ignore
            includeListRef.current = selectedList;
            triggerChange({
                includeVehicle: selectedList,
                excludeVehicle: excludeVehicle || innerExcludeList,
            });
        } else if (type === 'exclude') {
            // @ts-ignore
            setInnerExcludeList(selectedList);
            // @ts-ignore
            excludeListRef.current = selectedList;
            triggerChange({
                includeVehicle: includeVehicle || innerIncludeList,
                excludeVehicle: selectedList,
            });
        }
        setModalVisible(false);
    };

    const includeColumns = generateColumns('include', type);
    const excludeColumns = generateColumns('exclude', type);
    return (
        <div className="authorize-company-container">
            <div className="table-wrappers">
            <RspBasicLayout preset="ListMap" gutter={[40, 24]}>
                    <RspBasicLayout.Item>
                    <div className="include-company-wrapper table-wrapper">
                    <TableWrapper
                        showSelectbox={false}
                        showOperate
                        showDelete={false}
                        title={
                            type == 'line' ? i18n.t('name', '包含线路') : i18n.t('name', '包含场站')
                        }
                        operateText={
                            type == 'line'
                                ? i18n.t('name', '添加包含线路')
                                : i18n.t('action', '添加包含场站')
                        }
                        columns={includeColumns}
                        rowKey={type == 'line' ? 'lineId' : 'stationId'}
                        showSearch={showSearch}
                        dataSource={(includeVehicle || innerIncludeList).filter((p: any) => {
                            if (!includeSearchValue) return true;
                            if (type == 'line') {
                                return (
                                    (p.lineCode.toString() || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(includeSearchValue.toLowerCase()) !== -1 ||
                                    (p.lineName.toString() || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(includeSearchValue.toLowerCase()) !== -1
                                );
                            }
                            if (type == 'station') {
                                return (
                                    (p.stationName || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(includeSearchValue.toLowerCase()) !== -1
                                );
                            }
                            return (
                                (p.vehicleNumber || '')
                                    .trim()
                                    .toLowerCase()
                                    .indexOf(includeSearchValue.toLowerCase()) !== -1
                            );
                        })}
                        onClick={() => handleAddVehicle('include')}
                        searchPlaceholder={
                            type == 'line'
                                ? i18n.t('name', '请输入线路名称或线路编号')
                                : i18n.t('message', '请输入场站名称')
                        }
                        onSearch={(e: any) => {
                            setIncludeSearchValue(e.target.value?.trim());
                        }}
                        // onDelete={(keys: React.Key[]) => handleMultiDelete('include', keys)}
                    />
                </div>
                    </RspBasicLayout.Item>
                    <RspBasicLayout.Item>
                    <div className="exclude-company-wrapper  table-wrapper">
                    <TableWrapper
                        showSelectbox={false}
                        showOperate
                        showDelete={false}
                        title={
                            type == 'line' ? i18n.t('name', '剔除线路') : i18n.t('name', '剔除场站')
                        }
                        operateText={
                            type == 'line'
                                ? i18n.t('name', '添加剔除线路')
                                : i18n.t('action', '添加剔除场站')
                        }
                        columns={excludeColumns}
                        rowKey={type == 'line' ? 'lineId' : 'stationId'}
                        showSearch={showSearch}
                        dataSource={(excludeVehicle || innerExcludeList).filter((p: any) => {
                            if (!excludeSearchValue) return true;

                            if (type == 'line') {
                                return (
                                    (p.lineCode.toString() || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(excludeSearchValue.toLowerCase()) !== -1 ||
                                    (p.lineName.toString() || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(excludeSearchValue.toLowerCase()) !== -1
                                );
                            }
                            if (type == 'station') {
                                return (
                                    (p.stationName || '')
                                        .trim()
                                        .toLowerCase()
                                        .indexOf(excludeSearchValue.toLowerCase()) !== -1
                                );
                            }
                            return (
                                (p.vehicleNumber || '')
                                    .trim()
                                    .toLowerCase()
                                    .indexOf(excludeSearchValue.toLowerCase()) !== -1
                            );
                        })}
                        onClick={() => handleAddVehicle('exclude')}
                        searchPlaceholder={
                            type == 'line'
                                ? i18n.t('name', '请输入线路名称或线路编号')
                                : i18n.t('message', '请输入场站名称')
                        }
                        onSearch={(e: any) => {
                            setExcludeSearchValue(e.target.value?.trim());
                        }}
                        // onDelete={(keys: React.Key[]) => handleMultiDelete('exclude', keys)}
                    />
                </div>
                    </RspBasicLayout.Item>
                </RspBasicLayout>               

            </div>

            <LineStationModal
                visible={modalVisible}
                type={operateType}
                authorizeAppId={authorizeAppId}
                onCancel={() => setModalVisible(false)}
                name={type}
                onOk={handleSelect}
                targetList={
                    operateType === 'include'
                        ? includeVehicle || innerIncludeList
                        : excludeVehicle || innerExcludeList
                }
            />
        </div>
    );
};

export default LineOrStation;
