import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
import React, { useState, useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { IconRequest } from '@streamax/poppy-icons';
import { StarryModal } from '@base-app/runtime-lib';
import TableWrapper from './TableWrapper';
import DriverModal from './DriverModal';
import { message } from '@streamax/poppy';
//@ts-ignore
import { OverflowEllipsisContainer, TipConfirm } from '@streamax/starry-components';
import AuthFleetShow from '@/components/AuthFleetShow';
import { RspBasicLayout } from "@streamax/responsive-layout";

export type OperateType = 'include' | 'exclude';

interface DriverItem {
    driverId: number;
    driverName: string;
    jobNumber: string;
    fleetName: string;
    powerId?: number;
}

interface AuthorizeDriverValue {
    includeDriver: DriverItem[];
    excludeDriver: DriverItem[];
}

interface AuthorizeDriverProps {
    showOperate?: boolean;
    showSearch?: boolean;
    authorizeAppId?: number | undefined;
    value?: AuthorizeDriverValue;
    onChange?: (values: AuthorizeDriverValue) => void;
}

const AuthorizeDriver: React.FC<AuthorizeDriverProps> = (props) => {
    const { showOperate, onChange, value, showSearch, authorizeAppId } = props;
    const { includeDriver, excludeDriver } = value || {};

    const [innerIncludeList, setInnerIncludeList] = useState([]);
    const [innerExcludeList, setInnerExcludeList] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [operateType, setOperateType] = useState<OperateType>('include');
    const [includeSearchValue, setIncludeSearchValue] = useState();
    const [excludeSearchValue, setExcludeSearchValue] = useState();

    const includeListRef = useRef();
    const excludeListRef = useRef();
    const includetabwaperRef = useRef<any>(null);
    const excludetabwaperRef = useRef<any>(null);

    const triggerChange = (changedValue: AuthorizeDriverValue) => {
        onChange?.(changedValue);
    };

    const handleDelete = (type: OperateType, record: any) => {
        // 这种情况是进入页面后，没有选取车组，直接点击删除（有回填数据）
        if (!includeListRef.current) {
            // @ts-ignore
            includeListRef.current = includeDriver;
        }
        if (!excludeListRef.current) {
            // @ts-ignore
            excludeListRef.current = excludeDriver;
        }
        if (type === 'include') {
            // @ts-ignore
            const newList = [...includeListRef.current];
            const index = newList.findIndex((p: any) => p.driverId === record.driverId);
            if (index !== -1) {
                newList.splice(index, 1);
            }
            // @ts-ignore
            setInnerIncludeList(newList);
            // @ts-ignore
            includeListRef.current = newList;
            triggerChange({
                includeDriver: newList,
                // @ts-ignore
                excludeDriver: excludeListRef.current,
            });
        } else if (type === 'exclude') {
            // @ts-ignore
            const newList = [...excludeListRef.current];
            const index = newList.findIndex((p: any) => p.driverId === record.driverId);
            if (index !== -1) {
                newList.splice(index, 1);
            }
            // @ts-ignore
            setInnerExcludeList(newList);
            // @ts-ignore
            excludeListRef.current = newList;
            triggerChange({
                // @ts-ignore
                includeDriver: includeListRef.current,
                excludeDriver: newList,
            });
        }
        // modal.destroy();
        message.success(i18n.t('message', '删除成功'));
    };

    const handleMultiDelete = (type: OperateType, keys: React.Key[]) => {
        if (!keys || keys.length === 0) {
            message.warning(i18n.t('message', '请选择司机'));
            return;
        }
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            content: `${i18n.t('name', '确认要删除已选司机姓名的授权吗？')}`,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                // 这种情况是进入页面后，没有选取车组，直接点击删除（有回填数据）
                if (!includeListRef.current) {
                    // @ts-ignore
                    includeListRef.current = includeDriver;
                }
                if (!excludeListRef.current) {
                    // @ts-ignore
                    excludeListRef.current = excludeDriver;
                }
                if (type === 'include') {
                    // @ts-ignore
                    const tmp = [...includeListRef.current];
                    const newList = tmp.filter((item: any) => {
                        return keys.findIndex((p: React.Key) => p === item.driverId) === -1;
                    });
                    // @ts-ignore
                    setInnerIncludeList(newList);
                    // @ts-ignore
                    includeListRef.current = newList;
                    triggerChange({
                        includeDriver: newList,
                        // @ts-ignore
                        excludeDriver: excludeListRef.current,
                    });
                    //@ts-ignore
                    includetabwaperRef.current.setSelectedKeys([]);
                } else if (type === 'exclude') {
                    // @ts-ignore
                    const tmp = [...excludeListRef.current];
                    const newList = tmp.filter((item: any) => {
                        return keys.findIndex((p: React.Key) => p === item.driverId) === -1;
                    });
                    // @ts-ignore
                    setInnerExcludeList(newList);
                    // @ts-ignore
                    excludeListRef.current = newList;
                    triggerChange({
                        // @ts-ignore
                        includeDriver: includeListRef.current,
                        excludeDriver: newList,
                    });
                    excludetabwaperRef.current?.setSelectedKeys([]);
                }
                modal.destroy();
                message.success(i18n.t('message', '删除成功'));
            },
        });
    };

    const generateColumns = (type: OperateType) => {
        const columnList = [
            {
                title: i18n.t('name', '司机姓名'),
                dataIndex: 'driverName',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '司机工号'),
                dataIndex: 'jobNumber',
                ellipsis: true,
                render: (text: any) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '归属车组'),
                dataIndex: 'fleetList',
                ellipsis: true,
                render: (text = []) => (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={text} />
                    </StarryAbroadOverflowEllipsisContainer>
                ),
            },
        ];
        if (showOperate) {
            columnList.push({
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                // @ts-ignore
                render: (text: any, record: any) => {
                    return (
                        <TipConfirm
                            popOptions={{
                                title: i18n.t('message', '确认要删除“{name}”授权吗？',{name:record.driverName}),
                                onConfirm: () => handleDelete(type, record),
                            }}
                        />
                    );
                },
            });
        }
        return columnList;
    };

    const handleAddDriver = (type: OperateType) => {
        setOperateType(type);
        setModalVisible(true);
    };

    const handleSelect = (type: OperateType, selectedList: any[]) => {
        if (type === 'include') {
            // @ts-ignore
            setInnerIncludeList(selectedList);
            // @ts-ignore
            includeListRef.current = selectedList;
            triggerChange({
                includeDriver: selectedList,
                excludeDriver: excludeDriver || innerExcludeList,
            });
        } else if (type === 'exclude') {
            // @ts-ignore
            setInnerExcludeList(selectedList);
            // @ts-ignore
            excludeListRef.current = selectedList;
            triggerChange({
                includeDriver: includeDriver || innerIncludeList,
                excludeDriver: selectedList,
            });
        }
        setModalVisible(false);
    };

    const includeColumns = generateColumns('include');
    const excludeColumns = generateColumns('exclude');
    return (
        <div className="authorize-company-container">
            <div className="table-wrappers">
                <RspBasicLayout preset="ListMap" gutter={[40, 24]}>
                    <RspBasicLayout.Item>
                    <div className="include-company-wrapper table-wrapper">
                    <TableWrapper
                        ref={includetabwaperRef}
                        showOperate
                        title={i18n.t('name', '包含司机')}
                        operateText={i18n.t('action', '添加包含司机')}
                        descText={`${i18n.t('message', '暂无包含司机')}`}
                        clickText={i18n.t('action', '请添加')}
                        columns={includeColumns}
                        rowKey="driverId"
                        showSearch={showSearch}
                        dataSource={(includeDriver || innerIncludeList).filter((p: any) => {
                            const name = `${p.driverName}-${p.jobNumber}`;
                            return (
                                `${name.replace(/(^\s*)|(\s*$)/g, '').toLowerCase()}`.indexOf(
                                    (includeSearchValue || '').toLowerCase(),
                                ) !== -1
                            );
                        })}
                        onClick={() => handleAddDriver('include')}
                        searchPlaceholder={i18n.t('message', '请输入司机姓名或工号')}
                        onSearch={(e: any) => {
                            setIncludeSearchValue(e.target.value?.trim());
                        }}
                        onDelete={(keys: React.Key[]) => handleMultiDelete('include', keys)}
                    />
                </div>
                    </RspBasicLayout.Item>
                    <RspBasicLayout.Item>
                    <div className="exclude-company-wrapper  table-wrapper">
                    <TableWrapper
                        ref={excludetabwaperRef}
                        showOperate
                        title={i18n.t('name', '剔除司机')}
                        operateText={i18n.t('action', '添加剔除司机')}
                        descText={`${i18n.t('message', '暂无剔除司机')}`}
                        clickText={i18n.t('action', '请添加')}
                        columns={excludeColumns}
                        rowKey="driverId"
                        showSearch={showSearch}
                        dataSource={(excludeDriver || innerExcludeList).filter((p: any) => {
                            const name = `${p.driverName}-${p.jobNumber}`;
                            return (
                                `${name.replace(/(^\s*)|(\s*$)/g, '').toLowerCase()}`.indexOf(
                                    (excludeSearchValue || '').toLowerCase(),
                                ) !== -1
                            );
                        })}
                        onClick={() => handleAddDriver('exclude')}
                        searchPlaceholder={i18n.t('message', '请输入司机姓名或工号')}
                        onSearch={(e: any) => {
                            setExcludeSearchValue(e.target.value?.trim());
                        }}
                        onDelete={(keys: React.Key[]) => handleMultiDelete('exclude', keys)}
                    />
                </div>
                    </RspBasicLayout.Item>
                </RspBasicLayout>
            </div>

            <DriverModal
                visible={modalVisible}
                type={operateType}
                onCancel={() => setModalVisible(false)}
                //@ts-ignore
                onOk={handleSelect}
                authorizeAppId={authorizeAppId}
                targetList={
                    operateType === 'include'
                        ? includeDriver || innerIncludeList
                        : excludeDriver || innerExcludeList
                }
            />
        </div>
    );
};

export default AuthorizeDriver;
