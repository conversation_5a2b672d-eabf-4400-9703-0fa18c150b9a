import React, { useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { CustomTransferModal } from '@/components/custom-transfer';
import CustomTransfer from './CustomTransfer';
import type { OperateType } from './AuthorizeVehicle';

interface VehicleModalProps {
    visible: boolean;
    type: 'include' | 'exclude';
    targetList: any[];
    authorizeAppId?: number | undefined;
    onCancel?: () => void;
    onOk?: (type: OperateType, values: any[]) => void;
}

const VehicleModal: React.FC<VehicleModalProps> = (props) => {
    const { visible, type, targetList, onCancel, onOk, authorizeAppId } = props;
    const transferRef = useRef();

    const handleOk = () => {
        // @ts-ignore
        const selectedList = transferRef.current.getSelected();
        onOk?.(type, selectedList);
    };

    return (
        <CustomTransferModal
            className="data-authorize-add-modal"
            centered
            destroyOnClose
            visible={visible}
            maskClosable={false}
            title={
                type === 'include'
                    ? i18n.t('message', '添加包含车辆')
                    : i18n.t('message', '添加剔除车辆')
            }
            onCancel={onCancel}
            onOk={handleOk}
            focusTriggerAfterClose={false}
        >
            <CustomTransfer
                type="vehicle"
                titles={[i18n.t('message', '待选车辆'), i18n.t('message', '已选车辆')]}
                targetList={targetList}
                ref={transferRef as any}
                authorizeAppId={authorizeAppId}
                visible={visible}
            />
        </CustomTransferModal>
    );
};

export default VehicleModal;
