import React, { useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { CustomTransferModal } from '@/components/custom-transfer';
import CustomTransfer from './CustomTransfer';
import type { OperateType } from './AuthorizeDevice';

interface DeviceModalProps {
    visible: boolean;
    type: 'include' | 'exclude';
    targetList: any[];
    authorizeAppId?: number | undefined;
    onCancel?: () => void;
    onOk?: (type: OperateType, values: any[]) => void;
}

const DeviceModal: React.FC<DeviceModalProps> = (props) => {
    const { visible, type, targetList, onCancel, onOk, authorizeAppId } = props;
    const transferRef = useRef();

    const handleOk = () => {
        // @ts-ignore
        const selectedList = transferRef.current.getSelected();
        onOk?.(type, selectedList);
    };

    return (
        <CustomTransferModal
            className="data-authorize-add-modal"
            centered
            destroyOnClose
            maskClosable={false}
            visible={visible}
            title={
                type === 'include'
                    ? i18n.t('message', '添加包含设备')
                    : i18n.t('message', '添加剔除设备')
            }
            onCancel={onCancel}
            onOk={handleOk}
            focusTriggerAfterClose={false}
        >
            <CustomTransfer
                type="device"
                titles={[i18n.t('message', '待选设备'), i18n.t('message', '已选设备')]}
                targetList={targetList}
                ref={transferRef as any}
                authorizeAppId={authorizeAppId}
                visible={visible}
            />
        </CustomTransferModal>
    );
};

export default DeviceModal;
