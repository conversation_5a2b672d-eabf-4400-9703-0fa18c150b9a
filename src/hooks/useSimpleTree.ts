// 获取车组车辆树
import { useRef, useEffect, useState } from 'react';
import { utils } from '@base-app/runtime-lib';
import { getSimpleFleetList } from '@/service/fleet';
import { getSimpleVehicleList } from '@/service/vehicle';
import { i18n } from '@base-app/runtime-lib';
import { useDebounceFn } from '@streamax/hooks';
import { groupBy } from 'lodash';
import { getSetting } from '@/components/FleetVichileSort';
import { getDynamicDriverList } from '@/service/driver';
import type { DataNode } from 'antd/lib/tree';
import { getParentIds } from '@/hooks/useRTData/util';

export interface Fleet {
    fId: string;
    parentId: string;
    fName: string;
    path: string[];
    childrenIds: string[];
    vNumber: number;
    createTime: number;
}
export interface Vehicle {
    fId: string;
    vId: string;
    vNo: string;
    createTime: number;
}

export interface VehicleMap {
    fId: string;
    vList: Vehicle[];
}
export const unGroupFleetId = '0';

const useFleetTreeData = (fleetPrefix = 'fleet-', isShowVehicle = true) => {
    // 映射车辆对应的直属车队id
    const vehicleMap = useRef<Record<string, Set<string>>>({});
    // 映射车组id 及其 父车组id（包含自身）
    const fleetParentsMap = useRef<Record<string, Set<string>>>({});
    // 全量车组数据
    const [fleetList, setFleetList] = useState<Fleet[]>([]);
    //  返回给Tree组件渲染的数据
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [loadedKeys, setLoadedKeys] = useState<string[]>([]);
    // 全量车辆数据
    const [vehicleList, setVehicleList] = useState<Vehicle[]>([]);
    // 已加载的树数据
    const loadedData = useRef({
        vehicleList: [] as Vehicle[],
        fleetList: [] as Fleet[],
    });
    // 是否已初始化加载完成
    const [loaded, setLoaded] = useState(false);
    // 根据后端数据规则模拟未分组数据
    const unGroupFleet = {
        fleetId: unGroupFleetId,
        fleetName: i18n.t('name', '未分组'),
        path: `/0`,
        createTime: 0,
    };

    const initAllData = async () => {
        const reqs = [
            // 所有车组
            getSimpleFleetList(utils.general.getSortParam()),
            // 所有车辆
            getSimpleVehicleList({
                // 1启用 2停用
                vehicleState: 1,
            }),
        ];
        const resArr = await Promise.all(reqs);

        // vehicleRes 结构 ["fid,vid,vno","fid1,vid1,vno1",...]
        const [fleetsRes, vehicleRes] = resArr;

        // 将所有车辆构造为指定格式
        const unGroupVIds: string[] = [];
        // 过滤 车组id为空 且此车的车组id有不为空的情况。要求有车组的不展示到 未分组车组
        let vehicleTemp: Pick<Vehicle, 'fId' | 'vId' | 'vNo' | 'createTime'>[] = vehicleRes.map(
            (item: any) => {
                const [fId, vId, vNo, createTime] = item.split(',');
                const re = { fId, vId, vNo, createTime };
                return re;
            },
        );
        vehicleTemp = vehicleTemp.filter((item) => {
            return !(
                item.fId === unGroupFleet.fleetId &&
                vehicleTemp.some((i) => i.fId !== unGroupFleet.fleetId && i.vId === item.vId)
            );
        });
        let driverList = await getDynamicDriverList({
            page: 1,
            pageSize: 1e6,
        });
        driverList = driverList?.list || [];

        const allVehicleList: Vehicle[] = vehicleTemp.map((item) => {
            const { fId, vId, vNo, createTime } = item;
            if (fId === unGroupFleet.fleetId) {
                unGroupVIds.push(vId);
            }

            if (!vehicleMap.current[vId]) {
                vehicleMap.current[vId] = new Set();
            }
            vehicleMap.current[vId].add(fId);
            return {
                fId,
                vId,
                vNo,
                createTime: Number(createTime),
            };
        });
        // 缓存每个车队的子车队
        const fleetChildrenMap: Record<string, Set<string>> = {};
        // 构造车队为指定结构
        fleetsRes.forEach((item: any) => {
            const { fleetId } = item;
            const fId = fleetId || unGroupFleet.fleetId;
            // 父级车组id路径
            const pIds = getParentIds(fId, fleetsRes, {
                idKey: 'fleetId',
                parentIdKey: 'parentId',
            });
            // path 表示车组ID路径，从顶级到自身，层级间以ASCII-2F分隔。此处需要去除最后一个自身节点
            pIds.pop();
            // 将自己节点id加入父节点数组，方便计算车辆在线数和总数
            pIds.forEach((pid: string) => {
                if (!fleetChildrenMap.hasOwnProperty(pid)) {
                    fleetChildrenMap[pid] = new Set();
                }
                fleetChildrenMap[pid].add(fleetId);
            });
        });
        const allFleetList: Fleet[] = fleetsRes.map((item: any) => {
            const { fleetId, fleetName, parentId, createTime } = item;
            const fId = fleetId || unGroupFleet.fleetId;
            // 父级车组id路径
            const path = getParentIds(fId, fleetsRes, {
                idKey: 'fleetId',
                parentIdKey: 'parentId',
            });
            const childrenIds = fleetChildrenMap[fId] ? Array.from(fleetChildrenMap[fId]) : [];
            // 以每个车组id为key。值为包含自己以及所有的父节点id。为的是在后续统计 在线数、 总数 时，避免再次遍历所有车组。节省时间
            if (!fleetParentsMap.current.hasOwnProperty(fId)) {
                fleetParentsMap.current[fId] = new Set([fId]);
            }
            childrenIds.forEach((childrenId) => {
                if (!fleetParentsMap.current.hasOwnProperty(childrenId)) {
                    fleetParentsMap.current[childrenId] = new Set([childrenId]);
                }
                fleetParentsMap.current[childrenId].add(fId);
            });
            return {
                fId,
                // 未分组Id为0。顶级车组需置为null path 为1也是顶级车组
                parentId: path.length === 1 || parentId == 0 ? '' : parentId,
                fName: fleetName,
                path,
                childrenIds,
                vNumber: 0,
                createTime: Number(createTime),
            };
        });
        // 有未分组的车辆时才加入未分组车队
        unGroupVIds.length > 0 &&
            allFleetList.push({
                fId: '0',
                // 未分组Id为0。顶级车组需置为null path 为1也是顶级车组
                parentId: '',
                fName: unGroupFleet.fleetName,
                path: ['0'],
                childrenIds: [],
                vNumber: 0,
                createTime: 0,
            });
        // 算出车队对应的车辆总数，并赋值到车队 vNumber 属性上（车辆最多，优先遍历车辆，减少遍历次数）
        // 转为对象，便于访问，避免遍历访问。在大循环里相对节约时间
        const allFleetListMap = groupBy(allFleetList, 'fId');
        // Set 判断是否存在比数组判断会节约时间，特别是在大数据量循环里面
        allVehicleList.forEach((item: any) => {
            item.driver =
                ((driverList || []).find((p: any) => p.vehicleId === item.vId) || {})?.driverName ||
                '';
            // fId 为 null 表示未分组
            item.fId = item.fId || unGroupFleet.fleetId;
            // 车组自身或子节点包含此车辆id。则将此车组总数+1
            fleetParentsMap.current[item.fId]?.forEach((fId: string) => {
                allFleetListMap[fId][0].vNumber += 1;
            });
        });
        setFleetList(allFleetList);
        setVehicleList(allVehicleList);
    };

    //  加载全量车组、车辆数据
    useEffect(() => {
        initAllData().then(() => {
            setLoaded(true);
            // startTasks();
        });
        // return () => {
        //     cancelTask();
        // };
    }, []);

    const { run: updateTreeData } = useDebounceFn(
        () => {
            //  更新状态
            loadFleets(
                loadedKeys.length > 0
                    ? loadedKeys.map((i) => i.replace(fleetPrefix, ''))
                    : fleetList.filter((i) => i.path.length === 1).map((i) => i.fId),
            );
        },
        { wait: 100 },
    );

    useEffect(() => {
        updateTreeData();
    }, [fleetList, vehicleList]);

    const generateTreeData = (fleetData: Fleet[], vehicleData?: Vehicle[]) => {
        const data: any[] = [];
        const orderSetting = getSetting();
        fleetData.forEach((fleet) => {
            data.push({
                ...fleet,
                id: fleet.fId,
                key: `${fleetPrefix}${fleet.fId}`,
                type: 'fleet',
                title: fleet.fName,
                isLeaf: fleet.childrenIds.length === 0 || false,
            });
        });
        vehicleData?.forEach((vehicle) => {
            data.push({
                ...vehicle,
                key: `vehicle-${vehicle.fId}-${vehicle.vId}`,
                // 后面需要转为树数据，id不能重复，所以必须加上车组id
                id: `${vehicle.fId}-${vehicle.vId}`,
                parentId: vehicle.fId,
                type: 'vehicle',
                title: vehicle.vNo,
                isLeaf: true,
            });
        });

        const sorts = ['type'];
        const orders = ['asc'];
        if (orderSetting.top) {
            orders.push('desc');
        }
        orders.push(orderSetting.order);
        sorts.push(orderSetting.sort);
        // arrayToTree sort order 定义反了
        const treeData = utils.general.arrayToTree(data, {
            idFieldName: 'id',
            parentIdFieldName: 'parentId',
            // order: sorts.join(','),
            // sort: orders.join(','),
        });
        // 未分组放最后面
        const unGroupIndex = treeData.findIndex((i) => i.fId === unGroupFleet.fleetId);
        if (unGroupIndex !== -1) {
            treeData.push(treeData.splice(unGroupIndex, 1)[0]);
        }
        return treeData;
    };

    /**
     * 加载车组节点
     * @param fIds  只接受不带前缀的fleetId
     * @returns
     */
    const loadFleets = async (fIds: string[]) => {
        if (fIds.length === 0) return;
        // 找出要加载节点的父节点
        const ids = fleetList
            .filter((i) => fIds.includes(i.fId))
            .reduce((total, i) => {
                return total.concat(i.path);
            }, [] as string[]);
        const newLoadedKeys = Array.from(
            new Set(loadedKeys.map((i) => i.replace(fleetPrefix, '')).concat(ids)),
        );
        const fleetData = fleetList.filter(
            (i) => newLoadedKeys.includes(i.fId) || newLoadedKeys.includes(i.parentId),
        );
        const vehicleData = vehicleList.filter((i) => newLoadedKeys.includes(i.fId));

        loadedData.current = {
            vehicleList: vehicleData,
            fleetList: fleetData,
        };

        const treeData = isShowVehicle
            ? generateTreeData(fleetData, vehicleData)
            : generateTreeData(fleetData);
        setTreeData(treeData);
        // 打造成Tree一样的数据
        setLoadedKeys(newLoadedKeys.map((i) => `${fleetPrefix}${i}`));
    };
    // 刷新已加载数据，用于排序规则改变后重新排序等
    const refresh = () => {
        const { fleetList } = loadedData.current;
        const treeData = isShowVehicle
            ? generateTreeData(fleetList, vehicleList)
            : generateTreeData(fleetList);

        setTreeData(treeData);
    };
    return {
        treeData,
        loadFleets,
        refresh,
        loadedKeys,
        fleetList,
        vehicleList,
        loaded,
    };
};
export default useFleetTreeData;
