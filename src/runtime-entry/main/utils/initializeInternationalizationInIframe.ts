
import i18n, { init as i18nInit } from '../../../runtime-lib/i18n';
import moment from 'moment';
import IframeSDK from '@streamax/iframe-sdk';
import { getAppGlobalData } from '../../../runtime-lib';
import { mainDataSettingsIframeType, mainDataSettingsMessageSubTypeMap } from '../const';
import { Message } from '@streamax/iframe-sdk/lib/types';

/**
* 接收到iframe或者主页面推送的消息
* @returns 
*/
const receiveIframeMessage = (event: MessageEvent<any>, data: Message, resolve) => {
   const { type, subType, payload } = data;
   // 接收到的iframe消息，类型为主流程
   if (type === mainDataSettingsIframeType) {
       switch (subType) {
           // 接收到国际化数据的消息
           case mainDataSettingsMessageSubTypeMap.sendI18nLocals:
                resolve(payload);
               break;
           default:
               break;
       }
   }
};


// 像父页面发送消息获取i8n数据
type I18nLocals = {
    [key:string]: {
        translation: {
            [key:string]: string
        }
    }
}

const getI18nLocals: () => Promise<I18nLocals> = () => {
    return new Promise((resolve, reject) => {
        // 监听父页面传递的消息，获取i8n数据
        IframeSDK.receive((event: MessageEvent<any>, data: Message) => receiveIframeMessage(event, data, resolve));
        // 向父页面发起消息，获取i8n数据
        IframeSDK.send({
            type: mainDataSettingsIframeType,
            subType: mainDataSettingsMessageSubTypeMap.needI18nLocals
        });
    });
}

export default async () => {
    // 向主页面获取词条
    const runtimeLocales = await getI18nLocals();
    const lang = getAppGlobalData('APP_LANG');
    await i18nInit(lang, runtimeLocales[lang.replace('_', '-')]?.translation);
    moment.locale(lang);
}