
import {
    transformI18nValue,
} from '../../../runtime-lib/service';
import { init as i18nInit } from '../../../runtime-lib/i18n';
import moment from 'moment';
import {checkLangUsable} from "@/runtime-entry/main/utils/lang";

export type InitializeInternationalizationParams = {
    languageFrontListNew: any;
    languageAllByType: any;
    lang: string;
    systemInitLangType?: string
}

export default async (params: InitializeInternationalizationParams) => {
    const {
        languageFrontListNew,
        languageAllByType,
        lang,
        systemInitLangType
    } = params;
    const runtimeLocales: Record<string, string | undefined> = {};
    for (const key in languageFrontListNew) {
        runtimeLocales[key] = transformI18nValue(languageFrontListNew[key]);
    }
    for (const key in languageAllByType) {
        runtimeLocales[key] = transformI18nValue(languageAllByType[key]);
    }
    await i18nInit(lang, runtimeLocales);
    checkLangUsable(lang,systemInitLangType);
    moment.locale(lang);
}
