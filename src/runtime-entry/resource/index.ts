import Graph from './graph';
import { getAppGlobalData } from '@/runtime-lib/global-data';
import { getFilesOfMapById } from '@/runtime-lib/utils/service';

export interface Resource {
    resourceType: number;
    resourceCode: string;
    resourceId: string | number;
    resourceName: string;
    icon?: string;
    resourceUrl?: string;
    parentId?: string | number;
    sortValue: number;
    pageId?: number;
    level: number;
    clickResponseType: number;
}
type Menus = Resource[];
type Pages = Resource[];
type Actions = Record<string, Resource>;

export interface IResourceData {
    menus: Menus;
    pages: Pages;
    actions: Actions;
    graph: any;
    hashMap: Record<string, Resource>;
}

const patchMenus = (menus: Menus, hashMap: any) => {
    menus.forEach((item) => {
        const find = item.pageId ? hashMap[item.pageId] : null;
        find && (item.resourceUrl = find.resourceUrl);
    });
    return menus.sort((a, b) => a.sortValue - b.sortValue);
};

const generateGraphMapTable = (
    resource: Resource[],
    resourceId: any,
    pageId: any,
) => {
    const table = {};
    const childs: any[] = [];
    const pages: any[] = [];
    resource.forEach((item) => {
        item.parentId === resourceId && childs.push(item);
        item.resourceId === pageId && pages.push(item);
    });
    childs.forEach((child) => {
        table[child.resourceId] = 1;
    });
    pages.forEach((page) => {
        table[page.resourceId] = 1;
    });
    return Object.keys(table).length ? table : null;
};

const createResoruce = async (resource: Resource[]): Promise<IResourceData> => {
    let menus: any[] = [];
    const pages: any[] = [];
    const actions = {};
    const graphMap = {};
    const hashMap = {};
    resource.forEach((item) => {
        const resourceId = item.resourceId;
        const pageId = item.pageId;
        const table = generateGraphMapTable(resource, resourceId, pageId);
        table && (graphMap[resourceId] = table);

        hashMap[resourceId] = item;

        if (item.resourceType === 1) {
            menus.push(item);
        }
        if (item.resourceType === 2) {
            pages.push(item);
        }
        if (item.resourceType === 3 && item.resourceCode) {
            actions[item.resourceCode.trim()] = item;
        }
    });

    const entry = getAppGlobalData('APP_ENTRY');

    if (entry) {
        const entryMenu = menus.find((item) => item.resourceUrl === entry);
        const tmpMenus = menus.filter(
            (item) => item.parentId === entryMenu?.resourceId,
        );
        menus = menus.filter((item) =>
            tmpMenus.find(
                (tmp) =>
                    item.parentId === tmp.resourceId ||
                    item.resourceId === tmp.resourceId,
            ),
        );
    }

    menus = menus.filter((item) => item.level !== 1);
    return {
        menus: patchMenus(menus, hashMap),
        pages,
        actions,
        hashMap,
        // @ts-ignore
        graph: new Graph(graphMap),
    };
};

export default createResoruce;
