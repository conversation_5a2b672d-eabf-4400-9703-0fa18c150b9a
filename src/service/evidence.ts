//@ts-nocheck
import { mosaic<PERSON>anager, MosaicTypeEnum, request } from '@base-app/runtime-lib'; // 使用request工具
const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
import md5 from 'js-md5';
import { getLastOffset, setLastOffset } from '@/utils/setAdnGetLastOffset';
import { FileChannelAuthEnum, parseToSecond } from '@/utils/commonFun';
import {FileCleanStateEnum} from "@/modules/evidence/types";

const successCode = 200;

// 获取证据详情
enum StreamType {
    // 主码流
    MAIN = 1,
    // 子码流
    CHILD = 2,
}
enum EvidenceFileType {
    VIDEO = 1,
    GPS = 2,
    ALARM_INFO = 3,
    ACC = 4,
    IMAGE = 9,
    BLACK_BOX = 12,
    H264 = 13,
}
interface EvidenceFileItemType {
    fileId: string;
    fileUuid: string;
    url: string;
    fileType: EvidenceFileType;
    fileSize: number;
    startTime: number;
    endTime: number;
    channelNo: number;
    streamType: StreamType;
    videoTime: number;
}
interface DriverItem {
    driverId: number;
    driverName: string;
}
enum HandleStatus {
    DONE = 1,
    TODO = 0,
}
enum Valid {
    VALID = 1,
    INVALID = 2,
}
enum State {
    WAITING = 1,
    DOING = 2,
    DONE = 3,
    FAIL = 4,
}
interface BatchDeleteEvidenceProps {
    evidenceIds: string;
    delFlag?: number;
}
export interface EvidenceType {
    evidenceId: string;
    evidenceName: string;
    sourceType: number;
    evidenceType: number;
    startTime: number;
    endTime: number;
    driverName: string;
    handleStatus: HandleStatus;
    valid: Valid;
    state: State;
    fileCleaned: FileCleanStateEnum;
    fileList: EvidenceFileItemType[];
    driverList: DriverItem[];
    deviceId: string;
    alarmInfo: {
        alarmId: string;
        alarmType: string;
        alarmTime: number;
        alarmTypeName: string;
        speed: number;
        lng: number;
        lat: number;
    };
    vehicleInfo?: {
        vehicleNumber: string;
        addInfo: null | Record<string, any>;
        authId: string;
        deviceNo: string;
        fleetList: any[];
        protocolType: number;
        simNo: null | string;
        vehicleColor: number;
        vehicleId: string;
        vin: null | string;
    };
    fileChannelAuth: FileChannelAuthEnum | null;
}
export const getEvidenceDetail = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-alarm-service/api/v1/evidence/detail',
        params,
        headers: {
            // _appId: 10001
        },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    if (data && data.realtimeDriverList && data.realtimeDriverList.length) {
        data.driverName = data.realtimeDriverList
            .map((p: any) => p.driverName as string)
            .join('、');
    }
    return data as EvidenceType;
};
export const adjustEvidenceDetailData = async (params?: any) => {
    const data = await getEvidenceDetail(params);
    const videoList = data.fileList.filter((p: any) => p.fileType === 1 || p.fileType === 13);
    if (videoList.length) {
        data.startTime = videoList.sort((a: any, b: any) => a.startTime - b.startTime)[0].startTime;
        data.endTime = videoList.sort((a: any, b: any) => b.endTime - a.endTime)[0].endTime;
    }
    return data;
};

// 获取分享id，并记录操作记录
export const getSharedIdGenerate = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/share/key/generate',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export const createEvidenceDownload = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/vision/evidence/download/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 单车护航-视频剪辑
export const videoCut = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 边看边录-创建任务
export const recordCreate = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/transcribe/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 边看边录-开始录制
export const recordOpen = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/mms/v1/video/record/open',
        params,
    });
    if (!success || code !== 0) {
        throw message;
    }
    return data;
};
// 边看边录-结束录制
export const recordClose = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/mms/v1/video/record/close',
        params,
        showFailedMessage: false,
    });
    if (!success || code !== 0) {
        throw message;
    }
    return data;
};
// 获取服务器时间
export const getTime = async (params?: any) => {
    const data = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/system/current/timestamp',
        params,
    });

    return data;
};
// 删除证据
export const deleteEvidence = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/batch/delete',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 调整证据优先级
export const updateEvidencePriority = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/priority/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 重试失败的证据
export const retry = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-alarm-service/api/v2/evidence/retry`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 回写保存地址  注意2.5.0 废弃
export const saveAddress = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-evidence-service/api/v1/evidence/address/update`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

type EvidenceByAlarmParams = { alarmIds: string,fields?: string, accessLog?: number, mosaicFlag?: number }
export const getEvidenceByAlarm = async (params?: EvidenceByAlarmParams, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v2/evidence/by/alarm/list',
        data: params,
        timeout: 30000,
        headers
    });
    if (!success || code !== successCode) {
        throw message;
    }
    if (data.length) {
        data.forEach((item) => {
            if (item.fileList?.length) {
                item.fileList.forEach((item) => {
                    item.videoTime = parseToSecond(item.videoTime);
                });
            }
        });
    }
    return data;
};

export default {
    getPageListByAlarm: async (params?: EvidenceByAlarmParams) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v2/evidence/by/alarm/list',
            data: params,
            timeout: 20000,
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }
        if (data.length) {
            data.forEach((item) => {
                if (item.fileList?.length) {
                    item.fileList.forEach((item) => {
                        item.videoTime = parseToSecond(item.videoTime);
                    });
                }
            });
        }
        return data;
    },
    // 证据列表-已完成调用分页接口
    getPageList: async (params?: any, headers?: Record<string, any>) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v2/evidence/page',
            data: {...params, lastOffset: getLastOffset(params.page)},
            timeout: 20000,
            headers
        });
        if (!success || code !== successCode) {
            throw message;
        }
        setLastOffset(params.page, data?.lastOffset);

        if (data && data.list) {
            data.list.forEach((item: any) => {
                const { vehicleInfo } = item;
                if (vehicleInfo) {
                    item.vehicleNumber = vehicleInfo.vehicleNumber;
                    item.deviceNo = vehicleInfo.deviceNo;
                    if (vehicleInfo.fleetList) {
                        item.fleetName =
                            vehicleInfo.fleetList
                                .map((p: any) => p.fleetName as string)
                                .join('、') || '-';
                    }
                }
                item.driverName = '-';
                if (item.driverList && item.driverList.length) {
                    item.driverName = item.driverList
                        .map((p: any) => p.driverName as string)
                        .join('、');
                }
                if (item.alarmInfo) {
                    item.alarmType = item.alarmInfo.alarmType;
                }
                if (item.fileList?.length) {
                    item.fileList.forEach((item) => {
                        item.videoTime = parseToSecond(item.videoTime);
                    });
                }
            });
        }
        return data;
    },
    // 证据列表-已完成调用分页接口
    getPageProcessList: async (params?: any, headers?: Record<string, any>) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v2/evidence/process/page',
            data: params,
            timeout: 20000,
            headers
        });
        if (!success || code !== successCode) {
            throw message;
        }
        if (data && data.list) {
            data.list.forEach((item: any) => {
                const { vehicleInfo } = item;
                if (vehicleInfo) {
                    item.vehicleNumber = vehicleInfo.vehicleNumber;
                    item.deviceNo = vehicleInfo.deviceNo;
                    if (vehicleInfo.fleetList) {
                        item.fleetName =
                            vehicleInfo.fleetList
                                .map((p: any) => p.fleetName as string)
                                .join('、') || '-';
                    }
                }
                item.driverName = '-';
                if (item.driverList && item.driverList.length) {
                    item.driverName = item.driverList
                        .map((p: any) => p.driverName as string)
                        .join('、');
                }
                if (item.alarmInfo) {
                    item.alarmType = item.alarmInfo.alarmType;
                }
            });
        }

        return data;
    },
    // 进行中进度查询
    getDownloadPercent: async (params: {evidenceIds: string}, headers?: Record<string, any>) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-alarm-service/api/v1/evidence/file/download/percent/query',
            params,
            headers
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 获取分享 token
    getToken: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            // url: '/base-evidence-service/api/v1/vision/evidence/qr/expire',
            url: `/base-server-service/api/v1/user/tenant/token/share?tenantId=${params.tenantId}&expireTime=${params.expireTime}`,
            // query: params,
            data: {
                expireTime: params.expireTime,
            },
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 设置邮件分享有效期
    mailExpire: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/vision/evidence/email/expire',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // // 获取二维码有效期 token
    // getToken: async (params?: any) => {
    //     const { success, code, message, data } = await request({
    //         baseURL,
    //         method: 'post',
    //         url: '/base-server-service/api/v1/user/tenant/token/share',
    //         data: params,
    //     });
    //     if (!success || code !== successCode) {
    //         throw message;
    //     }
    //     return data;
    // },
    // 提交日志
    qrLog: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/vision/evidence/log/qr',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 邮件分享
    mailShare: async (params?: any) => {
        // 请求头中加上（请求参数JSON字符串+请求参数JSON字符串的md5）再加md5
        const paramsStr = (params?.mailContent || []).map(item => item.value).join('');
        console.log('====[[[paramsStr]]]====', paramsStr);
        const headerStr = paramsStr + md5(paramsStr);
        console.log('====[[[headerJson]]]====', headerStr);
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            // url: '/vision/evidence/email/share',
            url: '/base-alarm-service/api/v1/evidence/mail/send',
            data: params,
            headers: {
                _paramValidate: md5(headerStr),
            }
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 证据处理
    handle: async (params?: any) => {
        const {
            success,
            code,
            message: info,
            data,
        } = await request({
            baseURL,
            method: 'post',
            // url: '/base-alarm-service/api/v1/evidence/handle',
            url: '/base-alarm-service/api/v1/evidence/handle',
            data: params,
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            // message.error(i18n.t(langKey))
            throw info;
        }
        return data;
    },
    // 证据处理记录
    handleReport: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-alarm-service/api/v1/evidence/handle/page',
            params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 二维码，邮件分享处理记录
    handleShareReport: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-alarm-service/api/v1/evidence/handle/share/page',
            params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 二维码，邮件停用
    shareStop: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v1/evidence/handle/share/stop',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 查询是否有停用权限
    queryNotOnlyFunctionAdmin: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-server-service/api/v1/user/authority/notOnlyFunctionAdmin',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 证据访问记录
    accessReport: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-alarm-service/api/v1/evidence/access/page',
            params,
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 异步导出证据
    exportFileAsync: async (params?: any, headers?: Record<string, any>) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v1/evidence/file/export',
            data: params,
            headers
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },

    // H264导出证据
    exportH264: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-config-service/api/v1/imexport/evidence/h264/export',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 证据报告导出
    exportReport: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-alarm-service/api/v1/evidence/report/export',
            params,
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
    // 获取 s17 文件下载地址
    getDownloadUrl: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/gss/v1/file/download/url',
            params: { ...params, validTime: 7 },
            headers: {
                // _appId: 10001
            },
        });
        if (!success || code != successCode) {
            throw message;
        }
        return data;
    },
    // 获取证据总大小
    getFileTotalSize: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'post',
            url: '/base-alarm-service/api/v2/evidence/file/size/count',
            data: params,
        });
        if (!success || code !== successCode) {
            throw message;
        }
        return data;
    },
};
export enum DElFlAG {
    File, // 删除文件
    File_Record, // 文件和记录都删除
}
// 批量删除证据
export const batchDeleteEvidence = async (params: BatchDeleteEvidenceProps) => {
    const { delFlag } = params;
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/batch/delete',
        data: {
            // 0-物理删除，1-逻辑删除
            ...params,
            delFlag: delFlag ?? DElFlAG.File_Record,
        },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 报警打标，支持单个报警处理和批量报警处理
export const batchMarkEvidence = async (params: {
    evidenceIdList: string[];
    labelIdList?: string[];
    content?: string;
}) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v1/evidence/handle/label',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};


// 根据evidenceIds 批量查询证据信息
export const fetchEvidenceListByEvidenceId = async (params: {
    evidenceIds: string;
    fields?: string;
}) => {
    // id逗号隔开
    const { success, code, message, data } = await request<Request.Response<any[]>>({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v2/evidence/by/key/list',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询文件列表
export const fetchEvidenceFileList = async (params: {
    vehicleId: string;
    startTime: number;
    endTime: number;
    fileType: number;
    fields?: string;
    deviceId?: string;
    mosaicFlag?: number;
}) => {
    const { success, code, message, data } = await request<Request.Response<any[]>>({
        baseURL,
        method: 'get',
        url: '/base-alarm-service/api/v2/evidence/file/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询文件列表（支持通道号过滤）
export const fetchEvidenceFileListWithChannelFilter = async (params: {
    vehicleId: string;
    startTime: number;
    endTime: number;
    fileType: number;
    fields?: string;
    deviceId?: string;
    mosaicFlag?: number;
    channelNos?: (string | number)[];
}) => {
    const { channelNos, ...baseParams } = params;
    
    // 调用原接口获取所有数据
    const data = await fetchEvidenceFileList(baseParams);
    
    // 如果没有指定通道号过滤，直接返回所有数据
    if (!channelNos || channelNos.length === 0) {
        return data;
    }
    
    // 过滤指定通道号的数据
    const filteredData = data.filter(item => {
        return channelNos.includes(item.channelNo);
    });
    
    return filteredData;
};
