/*
 * @LastEditTime: 2024-06-25 14:18:20
 */
import { request, i18n, getAppGlobalData } from '@base-app/runtime-lib';
import { fetchFileDownloadUrl } from './gss';
const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

export interface AbnornalFaceInt {
    detailId: string;
    vehicleNumber: string;
    fileId: string;
    authId: string;
    vehicleId: string;
    handleStatus: 0 | 1;
    handleType: 0 | 1;
    faceCaptureTime: number;
    driverId: string;
    faceLocation: string;
    resultCode: string;
}
export interface AbnornalFace extends AbnornalFaceInt {
    pictureId: string;
    pictureUrl?: string;
    driverName?: string;
    picFrom: number;
}
export interface AbnornalFaceAggregate {
    recordId: string; // 聚合异常人脸待办记录id
    handleStatus: 0 | 1; // 办处理状态0-未处理 1-已处理
    createTime: number; // 创建时间 GMT 秒级时间戳
    createUser: string; // 创建人用户id
    hasPrivilege: 0 | 1 | null;
    updateUserName?: string; //处理人
    updateTime: number; // 更新时间 GMT 秒级时间戳
    handleTime: number; //处理时间
    updateUser: string; // 更新人用户id
    realDriverId: string; // 处理为司机时，对应的司机id
    driverName?: string; // 司机名称
    unknownDriverId: string; // 未知司机ID
    detailList: AbnornalFace[]; //每个照片item
    score: number;
}
export interface ComposeAbnornalFace extends AbnornalFaceAggregate, AbnornalFace {}
// 异常人脸分页查询
export const abnornalFaceGet = async (params: {
    startTime?: string;
    endTime?: string;
    handleStatus: 0 | 1;
    page: number;
    pageSize: number;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<{ list: AbnornalFaceAggregate[]; total: number }>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/abnormal/face/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }

    return data;
};

// 清理聚合异常人脸待办删除
export const abnornalFaceDelete = async (params: { recordIds: string }) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/abnormal/face/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 批量处理异常人脸待办
export const abnornalFaceHandle = async (params: {
    pictureIds?: string; // 绑定到司机的人脸文件id，多个用逗号分隔，最多n个
    driverId?: string; //处理异常人脸时绑定、新增的司机id 非必填
    handleType: 0 | 1; //0正常处理 1处理为未知司机
    recordIds: string; //需处理的聚合异常人脸待办ids，多个用逗号分隔
}) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/abnormal/face/batch/handle',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 聚合异常人脸待办详情查询
export const abnornalFaceGetDetailList = async (params: {
    recordId: string;
    page: number;
    pageSize: number;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<{ list: AbnornalFace[]; total: number; pageSize: number; page: number }>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/abnormal/face/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }

    return data;
};
// 剔除人脸待办详情图片
export const abnornalRemoveFaceImage = async (params: { recordId: string; detailIds: string }) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/abnormal/face/reject',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
/**************未知司机报警***********/
// 批量处理聚合未知司机报警
export const abnornalAlarmHandle = async (params: {
    pictureIds?: string; // 绑定到司机的人脸文件id，多个用逗号分隔，最多n个
    driverId?: string; //处理异常人脸时绑定、新增的司机id 非必填
    handleType: 0 | 1; //0正常处理 1处理为未知司机
    recordIds: string; //需处理的聚合异常人脸待办ids，多个用逗号分隔
}) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/unknown/driver/alarm/batch/handle',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 聚合未知司机报警分页查询
export const abnornalAlarmGet = async (params: {
    startTime?: string;
    endTime?: string;
    handleStatus: 0 | 1;
    page: number;
    pageSize: number;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<{ list: AbnornalFaceAggregate[]; total: number }>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/unknown/driver/alarm/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }

    return data;
};
// 聚合异常人脸待办详情查询
export const abnornalAlarmGetDetailList = async (params: {
    recordId: string;
    page: number;
    pageSize: number;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<{ list: AbnornalFace[]; total: number; pageSize: number; page: number }>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/unknown/driver/alarm/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }

    return data;
};
// 清理聚合未知司机报警
export const abnornalAlarmDelete = async (params: { recordIds: string }) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/unknown/driver/alarm/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 剔除未知司机报警
export const abnornalRemoveAlarmImage = async (params: { recordId: string; detailIds: string }) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/unknown/driver/alarm/reject',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 聚合未知司机报警合格照片查询
export const fetchUnknownDriverAlarmQualified = async (params: {
    /**聚合未知司机id */
    unknownDriverId: string;
    /**合格的照片条数 */
    qualifiedSize: number;
}) => {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/unknown/driver/alarm/qualified/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 聚合人脸异常代办合格照片查询
export const fetchAbnormalQualified = async (params: {
    /**聚合未知司机id */
    unknownDriverId: string;
    /**合格的照片条数 */
    qualifiedSize: number;
}) => {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/abnormal/face/qualified/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * @description 局点license更新&激活
 * @export
 * @param {{ license: string }} params
 * @return {*}  
 */
export async function licensePointActive(params: { license: string }) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/license/point/active',
        data: params,
        // @ts-ignore
        errorCodeMessageList: [{
            errorCode: 120020097,
            errorMessage: i18n.t('message', '无效的license')
        }]
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * @description 局点license信息查询
 * @export
 * @return {*}  
 */
export async function fetchLicensePointInfo() {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/license/point/info',
        // @ts-ignore
        errorCodeMessageList: [{
            errorCode: 120020090,
            errorMessage: i18n.t('message', '局点未激活')
        }]
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * @description 查询局点配置
 * @export
 * @return {*}  
 */
export async function fetchPointConfigInfo(params: unknown) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/license/package/record',
        params,
         // @ts-ignore
        errorCodeMessageList: [{
            errorCode: 120020090,
            errorMessage: i18n.t('message', '局点未激活')
        }]
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * @description 局点配置包导入&更新
 * @export
 * @return {*}  
 */
export async function pointPackageImport(params: unknown) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'post',
        data: params,
        timeout: 1000 * 60 * 60,
        url: '/base-server-service/api/v1/license/package/point/import/sync',
        // @ts-ignore
        errorCodeMessageList: [
            {
                errorCode: 120020090,
                errorMessage: i18n.t('message', '局点未激活')
            },
            {
                errorCode: 120020093,
                errorMessage: i18n.t('message', '局点不匹配')
            }
        ]
    });
    if (!success || code !== successCode) {
        throw message;
    }
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * @description 应用配置包导入
 * @export
 * @param {unknown} params
 * @return {*}  
 */
export async function appPackageImport(params: unknown) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'post',
        data: params,
        timeout: 1000 * 60 * 60,
        url: '/base-server-service/api/v1/license/package/app/import/sync',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * @description 资源包备份回滚
 * @export
 * @return {*}  
 */
export async function resourcePackageBackupRollback(params: { mainTaskId: number }) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/resource-package/backup/rollback',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

/**
 * 枚举项接口定义
 */
export interface EnumItem {
    /** 枚举类型 */
    enumType: string;
    /** 枚举值 */
    enumCode: string;
    /** 枚举名称 */
    enumName: string;
    /** 文件ID */
    fileId?: string;
    /** 排序 */
    sort?: number;
    /** 状态 */
    status?: number;
}

/**
 * 获取枚举分页数据
 */
export const fetchEnumPageList = async (params?: {
     /** 用户id */
    appId: string;
    /** 枚举类型 */
    enumType: string;
    /** 页码 */
    page?: number;
    /** 页大小 */
    pageSize?: number;
}) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<EnumItem>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/enum/page',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 获取车辆类型枚举及图标URL映射
 */
export const fetchVehicleTypeIconMap = async (): Promise<Record<string, string>> => {
    try {
        // 获取车辆类型枚举数据
        const enumData = await fetchEnumPageList({
            appId: getAppGlobalData('APP_ID') || '',
            enumType: 'vehicleType',
            page: 1,
            pageSize: 1e8,
        });
        
        if (!enumData?.list?.length) {
            return {};
        }

        // 提取所有有效的fileId
        const fileIds = enumData.list
            .filter(item => item.fileId)
            .map(item => item.fileId!)
            .join(',');

        if (!fileIds) {
            return {};
        }

        // 批量获取文件下载URL
        const fileUrls = await fetchFileDownloadUrl(
            {
                fileIdList: fileIds,
            },
            { _tenantId: 0, _appId: 0 },
        );  
        // 构建车辆类型到图标URL的映射
        const iconMap: Record<string, string> = {};
        enumData.list.forEach(item => {
            if (item.fileId) {
                const fileUrl = fileUrls?.find(f => f.fileId === item.fileId)?.fileUrl;
                if (fileUrl) {
                    iconMap[item.enumCode] = fileUrl;
                }
            }
        });

        return iconMap;
    } catch (error) {
        console.error('获取车辆类型图标映射失败:', error);
        return {};
    }
};

/**
 * @description 资源包备份删除
 * @export
 * @return {*}  
 */
export async function resourcePackageBackupDelete(params: { mainTaskId: number }) {
    const { success, code, message, data } = await request<Request.Response<any>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/resource-package/backup/delete',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
}

