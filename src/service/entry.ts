import { CacheType } from "@/types";
import { request } from "@base-app/runtime-lib";
const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

/**
 * 获取用户服务入口
 * @param setToLoginTime 是否设置异常时跳转登录的等待时间，只有框架主流程加载时才需要设置为 true
 * @param CacheControl 缓存策略，默认仅网络
 * @returns
 */
export const getUserAppEntrys = async (setToLoginTime = false, CacheControl?: CacheType) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/resource/entrance/query',
        baseURL,
        showFailedMessage: false,
        headers: {
            _langType: window.localStorage.getItem('LANG'),
        },
        ...(setToLoginTime ? { toLoginTime: 0 } : {}),
        CacheControl,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return (data.mappers || []).filter((item: any) => !!item.serviceEntrance);
};
