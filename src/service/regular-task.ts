import { request } from '@base-app/runtime-lib';
import type { BaseQueryParams } from './types';
import type { CycleTaskDetailSchema, CycleTaskParam, CycleTaskSchema, CycleTaskTypeEnum } from '@/types/cycleTask';
import { SwitchStatusEnum } from '@/types';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

export interface ListBasePramsProps{
    page: number;
    pageSize: number;
}
export interface DeviceRegularListParamsProps extends ListBasePramsProps{
    taskName?: string;
    taskStatus?: number;
    startCreateTime?: number;
    endCreateTime?: number;
}
export interface CycleTaskDetail {
    timeZone: string;
    cycleTaskParam: string;
    cycleInfo: {
        cycleType: number;
        dayWeek: number[];
        time: number[];
    }
    name: string;
}

/**
 * 周期任务查询参数
 */
export type CycleTaskParams = {
    name?: string;
    /**停启用状态，0-停用、1-启用，不传则所有状态 */
    cycleTaskType?: string;
    status?: number;
    createTimeStart?: number;
    createTimeEnd?: number;
} & BaseQueryParams;

export interface MessageRegularDistributeItem {
    id: string;
    status: number;
    name: string;
    messageType: number;
    cycleTaskType: number;
    executeTime: number;
    createUserName: string;
    createTime: number;
    appId: number;
    tenantId: string;
}
export interface MessagePageParams extends ListBasePramsProps{
    name?: string;
    status?: number;
    messageType?: number;
    createTimeStart?: number;
    createTimeEnd?: number;
    complexSort?: string;
}



export interface CycleTaskDetail {
    timeZone: string;
    cycleTaskParam: string;
    cycleInfo: {
        cycleType: number;
        dayWeek: number[];
        time: number[];
    };
    name: string;
}

export interface CycleTaskItem {
    id: string;
    appId: number;
    tenantId: number;
    name: string;
    cycleTaskType: number;
    status: number;
    createTime: number;
    updateTime: number;
    updateUserName: string;
    createUserName: string;
    time: number[];
    uniqueCode: string;
}
export interface TaskDispatchRecordProps extends ListBasePramsProps {
    startTime?: number;
    endTime?: number;
    states: number;
    vehicleNumber?: string;
    fleetId?: string;
}
export interface MessageRegularDistributeItem {
    id: string;
    status: number;
    name: string;
    messageType: number;
    cycleTaskType: number;
    executeTime: number;
    createUserName: string;
    createTime: number;
    appId: number;
    tenantId: string;
}
/**
 * 周期任务参数
 */
export type CycleAUParams = {
    id?: string;
    /**任务名称 */
    name: string;
    /**国家地区code  */
    cycleInfo: {
        cycleType: number;
        dayWeek: number[];
        time: number[];
    };
    cycleTaskParam: any;
    cycleTaskType: CycleTaskTypeEnum;
};
/**
 * 提取周期任务分页数据
 * @param params CycleTaskParams
 * @returns Request.ResponsePageList<CycleTaskSchema[]>
 */
export const fetchCycleTaskPageList = async (params?: CycleTaskParams) => {
    const { success, code, message, data } = await request<Request.ResponsePageList<CycleTaskSchema>>({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/cycletask/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 周期任务删除
 * @param params {ids: string}
 * @returns Request.Response<boolean>
 */
export const deleteCycleTask = async (params?: { ids: string}) => {
    const { success, code, message, data } = await request<Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/cycletask/delete',
        data: params, 
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 周期任务启动停用
 * @param params 
 * @returns Request.Response<boolean>
 */
export const changeCycleTaskStatus = async (params?: { id: string; status: SwitchStatusEnum }) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/cycletask/disenable',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 新增周期任务
 * @param params CycleAUParams
 * @returns string
 */
export const addCycleTask = async (params: CycleAUParams) => {
    const { success, code, message, data } = await request<Request.Response<string>>({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/cycletask/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 周期任务详情
export const getCycleTaskDetail = async (id: string) => { 
    const { success, code, message, data } = await request<Request.Response<CycleTaskDetail>>({
        baseURL,
        method: 'get',
        url: '/base-config-service/api/v1/cycletask/detail',
        params: {id},
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 编辑周期任务
export const editCycleTask = async (params: any, showFailedMessage = true) => { 
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/cycletask/edit',
        data: params,
        showFailedMessage
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;

    // return true;
};
// 周期任务调起记录列表
export const cycleTaskDispatchRecord = async (params: TaskDispatchRecordProps) => { 
    const { success, code, message, data } = await request<Request.ResponsePageList<TaskDispatchItem>>({
        baseURL,
        method: 'post',
        url: '/base-alarm-service/api/v2/evidence/page',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 周期任务分页
export const getCycleTaskList = async (params: DeviceRegularListParamsProps) => {
    const { success, code, message, data } = await request<Request.ResponsePageList<CycleTaskItem>>(
        {
            baseURL,
            method: 'get',
            url: '/base-config-service/api/v1/cycletask/page',
            params,
        },
    );
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

