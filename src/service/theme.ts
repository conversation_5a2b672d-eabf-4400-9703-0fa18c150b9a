import { request } from '@base-app/runtime-lib'; // 使用request工具
import type { ApplyTypeEnum, ExtendType, ThemeDetail, ThemeItem, ThemeStyleExtendShame, ThemeTypeEnum } from '@/types/theme';
import { BaseQueryParams } from './types';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

/**
 * 主题列表查询参数
 */
export type ThemeParams = {
    /**应用者id，当applyType=0是为租户id，applyType=2时为用户id，目前只存在应用者为租户的情况 */
    applyId?: string;
    themeId?: string;
    applyType?: ApplyTypeEnum;
    themeType: ThemeTypeEnum;
} & BaseQueryParams;

/**
 * 主题内容分页查询
 * @param params ThemeParams
 * @returns ThemeItem
 */
export const fetchThemeList = async (params?: ThemeParams) => {
    const { success, code, message, data } = await request<Request.ResponsePageList<ThemeItem>>({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/theme/page',
        params,
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw message;
    }
    return data;
};

/**
 * 查询正在使用的主题
 * @param params 
 * @returns 
 */
export const fetchThemeApplyDetail = async (params?: ThemeParams) => {
    const { success, code, message, data } = await request<Request.Response<ThemeDetail>>({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/theme/apply/detail',
        params,
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw message;
    }
    return data;
};

/**
 * 修改正在使用的主题
 * @param params ThemeParams
 * @returns  boolean
 */
export const updateThemeApply = async (params?: ThemeParams) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/theme/apply/create',
        data: params,
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw message;
    }
    return data;
};


export type ThemeStyleExtendParams = {
    themeId: string;
    appId: string;
    extendType: ExtendType;
    extendData: string;
};

/**
 * 主题扩展查询
 * @param params ThemeStyleExtendParams
 * @returns
 */
export const fetchThemeStyleExtend = async (params?: Partial<ThemeStyleExtendParams>) => {
    const { success, code, message, data } = await request<
        Request.Response<ThemeStyleExtendShame[]>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/theme/extend',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 主题扩展更新
 * @param params ThemeStyleExtendParams[]
 * @returns boolean
 */
export const updateThemeStyleExtend = async (params?: Omit<ThemeStyleExtendParams, 'extendType'>[]) => {
    const { success, code, message, data } = await request<Request.Response<boolean>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/theme/extend',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};