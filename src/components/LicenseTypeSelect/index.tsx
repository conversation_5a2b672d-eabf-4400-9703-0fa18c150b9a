/*
 * @LastEditTime: 2024-09-27 14:46:55
 * license 管理模块专用下拉选项组件
 */
import { Form, Select } from '@streamax/poppy';
import { i18n, StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { LICENSE_TYPE_ENUM, storageSwitchOpen } from '@/utils/license-common';
import { queryPackageSubOptions, SubLicenseItem } from '@/service/license';

import './index.less';
import { useDebounceFn } from '@streamax/hooks';
import uuid from '@/utils/uuid';
import { cloneDeep } from 'lodash';
type LicenseSelectProps = {
    licenseType: LICENSE_TYPE_ENUM;
    labelRequired?: boolean;
    formItem?: Record<string, any>;
    showLabel?: boolean;
    selectOptions?: SubLicenseItem[]; // select选项
    isRequestSelectOptions?: boolean; //是否需要请求对应的select选项
    licenseStatus?: string;
    onSelectedChange?: (license: any) => void;
    otherSearchParams?: Record<string, any>;//其他搜索条件
};
export type LicenseSelectRefProps = {
    reload: () => void; //重新加载下拉框
    getSelectedValue: () => any; //获取当前所选license对象
};
interface LicenseOptions {
    value: string;
    label: string;
    disabled?: boolean;
}
const licenseTypeObject = {
    [LICENSE_TYPE_ENUM.DEVICE]: i18n.t('name', '设备激活配置'),
    [LICENSE_TYPE_ENUM.FUNCTION]: i18n.t('name', '额外开通功能'),
    [LICENSE_TYPE_ENUM.STORAGE]: i18n.t('name', '存储空间'),
};
const LicenseSelect: React.ForwardRefRenderFunction<LicenseSelectRefProps, LicenseSelectProps> = (
    props,
    ref,
) => {
    const {
        onSelectedChange,
        licenseType,
        labelRequired,
        formItem,
        showLabel = true,
        selectOptions,
        isRequestSelectOptions = true,
        licenseStatus = '0',
        otherSearchParams = {}
    } = props;

    const [licenseList, setLicenseList] = useState<LicenseOptions[]>([]);
    const [currentSelect, setCurrentSelect] = useState({});
    const _fetchLicenseListByType = async (licenseTypes?: LICENSE_TYPE_ENUM) => {
        if (licenseTypes !== null && licenseTypes !== undefined) {
            const list = await queryPackageSubOptions({
                licenseTypes: licenseTypes, // 使用中的用户
                licenseStatus: licenseStatus,
                ...otherSearchParams
            });
            formateSetLicense(list);
            setCurrentSelect({});
        }
    };
    const { run: fetchLicenseListByType } = useDebounceFn(_fetchLicenseListByType, {
        wait: 300,
    });
    const formateSetLicense = (licenseList: SubLicenseItem[]) => {
        const cloneLicenseList = cloneDeep(licenseList);
        const licenseOptions = (cloneLicenseList || []).map((item, index) => {
            item.setsNames =
                (item.setsName ? `${item.setsName}-` : '') +
                ((item.storageSum && storageSwitchOpen() && licenseType != LICENSE_TYPE_ENUM.FUNCTION) ? `${item.storageSum}GB-` : '') +
                (item.remainder == -1
                    ? i18n.t('name', '永久有效')
                    : i18n.t('name', '{remainder}天', {
                          remainder: item.remainder,
                      }));
            return {
                ...item,
                label: (
                    <div className="license-select-item" key={item.setsCode}>
                        <span className="license-select-item-title">
                            <OverflowEllipsisContainer
                                tooltip={{
                                    placement: 'right',
                                }}
                            >
                                {item.setsNames}{!item.changeBinding && `-${i18n.t('message', '不支持换绑')}`}{item.autoExpireRemainder !== -1
                                    && `-${i18n.t('name', '{expireRemainder}天后过期', {
                                        expireRemainder: item.autoExpireRemainder,
                                    })}`}
                            </OverflowEllipsisContainer>
                        </span>
                        <span>
                            {i18n.t('name', '剩余:{number}', {
                                number: item.num,
                            })}
                        </span>
                    </div>
                ),
                value: item.autoId,
                key: uuid(),
            };
        });
        // @ts-ignore
        setLicenseList(licenseOptions);
    };
    useEffect(() => {
        if (selectOptions) {
            formateSetLicense(selectOptions.filter((item) => item.licenseType == licenseType));
        }
        if (isRequestSelectOptions) {
            fetchLicenseListByType(licenseType);
        }
    }, [selectOptions, licenseType]);
    useImperativeHandle(ref, () => ({
        reload: () => fetchLicenseListByType(licenseType),
        getSelectedValue: () => {
            const currentItem = licenseList.find((item) => item.value === currentSelect);
            if (currentItem) {
                const { label, value, key, ...rest } = currentItem;
                return rest;
            }
            return {};
        },
    }));
    const onChange = (value: any) => {
        setCurrentSelect(value);
        const currentItem = licenseList.find((item) => item.value === value);
        onSelectedChange?.(currentItem);
    };
    let rules = [
        {
            required: true,
            message:
                !formItem?.label && licenseType
                    ? i18n.t('message', '{title}不能为空', {
                          title: licenseTypeObject[licenseType],
                      })
                    : i18n.t('message', '{title}不能为空', {
                          title: formItem?.label,
                      }),
        },
    ];
    if (formItem?.customRules) {
        rules = rules.concat(formItem?.customRules);
    }
    
    return (
        <AFormItem
            className="license-type-form-select"
            rules={formItem?.required ? rules : []}
            label={showLabel === false ? '' : licenseTypeObject[licenseType]}
            {...formItem}
            required={labelRequired ?? formItem?.required ?? false}
        >
            <Select
                options={licenseList}
                placeholder={i18n.t('name', '请选择{title}', {
                    title: formItem?.label || licenseTypeObject[licenseType],
                })}
                showSearch
                allowClear
                getPopupContainer={(element) => element.parentNode}
                onChange={onChange}
                // @ts-ignore
                filterOption={(value, option) => {
                    return option?.setsName?.toLowerCase().includes?.(value?.trim()?.toLowerCase());
                }}
            />
        </AFormItem>
    );
};
export default forwardRef<LicenseSelectRefProps, LicenseSelectProps>(LicenseSelect);
