/*
 * @LastEditTime: 2025-06-25 11:25:40
 */
import { i18n } from "@base-app/runtime-lib";
import { ChannelSettingItem } from ".";

/**最大通道限制 */
export const MAX_LIMIT = 64;

/**最小通道限制 */
export const MIN_LIMIT = 1;

/**选择限制3个 */
export const CHANNEL_LIMIT = 3;

export const createChannelList = (numChannels=8): ChannelSettingItem[] => {
    return Array.from({ length: numChannels }).map((_, index) => ({
        channelNumber: index + 1,
		channelName: i18n.t('name', 'CH{num}', { num: index + 1 }),
		channelTypeId: null,
        algorithmModel: null,
        enable: true,
    }));
};