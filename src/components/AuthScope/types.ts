import react from 'react';

export interface SegmentData {
    key: string;
    segmentName: string;
    segmentContent: React.ReactElement;
    bottomDivider?: boolean;
    display?: boolean;
}

export interface StepData {
    key: string;
    stepName: string;
    segmentList: SegmentData[];
}

export interface TabItem {
    key: string;
    tabName: string | react.ReactElement;
    tabContent: react.ReactElement;
}

export type ModuleType = 'fleet' | 'vehicle' | 'device';

export enum MODEL {
    BASE = 'base',
    PERSONAL = 'personal',
}
