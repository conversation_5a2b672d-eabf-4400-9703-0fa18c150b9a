import { Checkbox, Space, TreeSelect } from '@streamax/poppy';
import { i18n, utils } from '@base-app/runtime-lib';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import companyRequest from '../../service/company';
import './index.less';

interface GroupSelectValue {
    fleetIds: string[] | undefined;
    includeSubFleet: 0 | 1;
}
interface GroupSelectProps {
    value?: GroupSelectValue;
    showSubBtn?: boolean;
    onChange: (value: GroupSelectValue) => void;
    newStyle: any;
    appId?: number | string;
}

const { TreeNode } = TreeSelect;

// 车队车辆联动下拉选择框
export default (props: GroupSelectProps) => {
    const {
        value = { fleetIds: undefined, includeSubFleet: 0 },
        onChange,
        showSubBtn = true,
        newStyle,
        appId,
    } = props;
    const [fleetList, setFleetList] = useState<any[]>([]);
    const [treeData, setTreeData] = useState<any[]>([]);
    const [loadDataFinish, setLoadDataFinish] = useState(false);
    const allTreeDataRef = useRef<any>([]);
    const addPath = (data: any[], parent: any) => {
        data.forEach((element) => {
            if (parent) {
                if (element.namePath) {
                    element.namePath += `/${parent.title}`;
                    element.idPath += `/${parent.key}`;
                } else {
                    element.namePath = `/${parent.title}`;
                    element.idPath = `/${parent.key}`;
                }
            }
            if (element.children) {
                addPath(element.children, element);
            }
        });
    };
    useEffect(() => {
        (async () => {
            const list = await companyRequest.getList({
                appId,
                ...utils.general.getSortParam(),
            });
            // 组装为树结构
            const data = utils.general.arrayToTree(cloneDeep(list), {
                idFieldName: 'key',
                parentIdFieldName: 'parentId',
                topIdValue: null,
                // order: 'title',
                // sort: 'desc',
                parentInfo: false,
            });
            // 先组装path
            addPath(data, null);
            allTreeDataRef.current = data;
            setTreeData(data);
            setLoadDataFinish(true);
            setFleetList(list);
        })();
    }, []);
    const handleValueChange = (companyValue: any) => {
        onChange({
            ...value,
            fleetIds: companyValue,
        });
    };
    const handleCheckChange = (e: any) => {
        onChange({
            ...value,
            includeSubFleet: e.target.checked ? 1 : 0,
        });
    };
    const handleSearch = (value: string) => {
        // TODO搜索后展示全路径
        let newTreeData: any[] = [];
        if (value) {
            newTreeData = fleetList.filter((item: any) =>
                item.title?.toLowerCase().includes(value.toLowerCase()),
            );
        } else {
            newTreeData = allTreeDataRef.current;
        }
        // 限制只展示20个搜索记录
        if (newTreeData.length > 20) {
            newTreeData = newTreeData.splice(0, 20);
        }
        setTreeData(newTreeData);
    };
    const generateNode = (treeData: any[]) => {
        return treeData.map((item: any) => (
            <TreeNode
                value={item.key}
                // title={<Tooltip title={item.namePath}><span>{item.title}</span></Tooltip>}
                title={item.title}
                key={item.key}
            >
                {item.children && generateNode(item.children)}
            </TreeNode>
        ));
    };
    return (
        <Space>
            {/* @ts-ignore */}
            <TreeSelect
                placeholder={i18n.t('message', '请选择归属车队')}
                treeCheckable={false}
                // showSearch
                allowClear
                // treeNodeFilterProp='title'
                onChange={handleValueChange}
                value={loadDataFinish ? value.fleetIds : undefined}
                style={newStyle ? newStyle : { width: '280px' }}
                onSearch={handleSearch}
            >
                {generateNode(treeData)}
            </TreeSelect>
            <span style={{ display: showSubBtn ? 'inline-bloc' : 'none' }}>
                <Checkbox
                    value={value.includeSubFleet}
                    checked={value.includeSubFleet == 1 ? true : false}
                    onChange={handleCheckChange}
                >
                    {i18n.t('message', '包含全部子车队')}
                </Checkbox>
            </span>
        </Space>
    );
};
