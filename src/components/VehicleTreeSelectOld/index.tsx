import { useState, useEffect, useImperativeHandle, forwardRef, useMemo, useRef } from 'react';
import { Badge, TreeSelect } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import React from 'react';
import classNames from 'classnames';
import { uniqBy } from 'lodash';
import { FLEET_PREFIX, UN_GROUP_FLEET_ID } from '@/const/vehicle';
import {
    getTreeFleetKey,
    vehicle2TreeNode,
    getTreeVehicleKey,
    getVehicleIdByTreeKey,
} from '@/hooks/useFleetTreeData/useTransformTreeData';
import type { Vehicle as RTVehicle } from '@/hooks/useRTData/useRealtimeMonitoring';
import type { CountFleet as RTFleet } from '@/hooks/useRTData/util';
import type {
    Fleet as BaseFleet,
    Vehicle as BaseVehicle,
} from '@/hooks/useRTData/useFleetVehicleData';
import type { DataNode } from '@streamax/poppy/lib/tree';
import type { VehicleOrderState } from '@/service/vehicle';
import { getRealtimeVideoVehicleDetail } from '@/service/vehicle';
import { useDebounceFn, useGetState } from '@streamax/hooks';
import './index.less';

const { TreeNode } = TreeSelect;

const fleetPrefix = FLEET_PREFIX;

type TreeNodeType = DataNode & (BaseFleet | BaseVehicle);
type RtTreeNodeType = DataNode & (RTFleet | RTVehicle);
interface VehicleTreeSelectModel {
    disabled?: boolean;
    vehicleId: any;
    size?: string;
    onlySelectOnline?: boolean;
    showState?: boolean;
    onChange: (vehicleId: string, vehicleInfo: Record<string, any>) => void;
    isExpandFirstGroup?: boolean;
    type?: string;
    fleetList: RTFleet[];
    vehicleList: BaseVehicle[];
    treeData: (TreeNodeType | RtTreeNodeType)[];
    loadFleets: (fIds: string[]) => Promise<void>;
    loadedKeys: string[];
    vehicleStateConfig?: VehicleOrderState[];
    onAutoRemoveSelectedVehicle?: (vehicleState: number | undefined) => void;
    onTreeNodeGenerate?: () => void;
}

interface RefVehicleTreeSelectProps {
    positionVehicle: (vehicleId: string, fleetId: string) => void;
}

const VehicleTreeSelect: React.ForwardRefRenderFunction<
    RefVehicleTreeSelectProps,
    VehicleTreeSelectModel
> = (props, ref) => {
    const {
        onChange,
        onlySelectOnline = false,
        showState = true,
        type,
        disabled,
        vehicleList,
        treeData,
        loadFleets,
        loadedKeys,
        vehicleStateConfig,
        fleetList,
        isExpandFirstGroup = true,
        onAutoRemoveSelectedVehicle,
        onTreeNodeGenerate,
        size,
    } = props;
    const [selectedVehicle, setSelectedVehicle] = useState<any>();
    const [expandedKeys, setExpandedKeys, getExpandedKeys] = useGetState<string[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const innerSearchValue = useRef('');
    const [isInitDataFinish, setIsInitDataFinish] = useState(false); // 初始化数据是否完成

    const positionVehicle = async (vehicleId: string, fleetId?: string) => {
        let fId = fleetId;
        if (!vehicleId) return;
        // 如何fleetId 未传，需要查询出fleetId
        const vehicle = vehicleList.find((i: any) => i.vId === vehicleId);
        if (!vehicle) return;
        if (!fId && vehicle) {
            fId = vehicle.fId;
        }
        const { key } = getTreeVehicleKey(fId || UN_GROUP_FLEET_ID, vehicleId);
        if (fId) {
            const fleet = fleetList.find((i: any) => i.fId === fId);
            const parentIds = fleet ? fleet.path : ([] as string[]);
            loadFleets([fId]);
            const newExpandedKeys = [
                ...expandedKeys,
                ...parentIds.map((id: string) => getTreeFleetKey(id).key),
            ];
            setExpandedKeys(newExpandedKeys);
        }
        setSelectedVehicle(key);
        vehicle && onChange(String(vehicleId), vehicle);
        setSearchValue('');
        innerSearchValue.current = '';
    };

    // 检查车辆状态并移除选择
    const checkVehicleStatusAndRemove = async (vehicleId: string) => {
        try {
            const vehicleDetail = await getRealtimeVideoVehicleDetail(
                { vehicleId, fields: 'state' },
                false // 不显示失败消息
            );

            // 移除选择并传递车辆状态给外层处理
            setSelectedVehicle(null);
            onChange('', {});
            onAutoRemoveSelectedVehicle?.(vehicleDetail?.vehicleState);
        } catch (error) {
            // 如果获取失败，传递undefined状态
            setSelectedVehicle(null);
            onChange('', {});
            onAutoRemoveSelectedVehicle?.(undefined);
        }
    };

    useImperativeHandle(ref, () => ({
        positionVehicle,
    }));
    useEffect(() => {
        if (treeData.length > 0) {
            setIsInitDataFinish(true);
        }
    }, [treeData]);
    useEffect(() => {
        // vehicleList改变时，检查当前选择的车辆状态
        // 只要车辆不是在线，就都会从vehicleList中去除，所以总是检测当前车辆的状态
        if (selectedVehicle) {
            const vId = getVehicleIdByTreeKey(selectedVehicle);
            const vehicleExists = vehicleList.some((i) => i.vId === vId);
            if (!vehicleExists) {
                // 车辆不在列表中，检查其状态并移除选择
                checkVehicleStatusAndRemove(vId);
            }
        }
    }, [vehicleList]);
    useEffect(() => {
        if (isExpandFirstGroup && isInitDataFinish) {
            // 展开一级车组
            setExpandedKeys(treeData.map((item) => item.key as string));
        }
    }, [isExpandFirstGroup, isInitDataFinish]);

    // 加载车队下的车辆数据
    async function loadVehicleData(node: any, callback?: any) {
        return new Promise<void>((resolve) => {
            const { key, children } = node;
            const id = key.replace(fleetPrefix, '');
            if ((children && children.length > 0) || loadedKeys.includes(key)) {
                resolve();
                return;
            }
            loadFleets([id]).then(() => {
                resolve();
                callback?.();
            });
        });
    }

    const onSelect = (e: any) => {
        if (!e) {
            setSearchValue('');
            innerSearchValue.current = '';
            return;
        }
        const infoArr = e.split('-');
        const vehicleId = infoArr[infoArr.length - 1];
        const fleetId = infoArr[1];
        // 搜索时选中的需要加载树种父节点数据
        if (!!searchValue) {
            positionVehicle(vehicleId, fleetId);
        } else {
            setSelectedVehicle(e);
            const vehicle = vehicleList.find((i: any) => i.vId === vehicleId) || {};
            onChange(vehicleId, vehicle);
        }
    };
    const onFleetClick = async (fleet: any) => {
        const { key } = fleet;
        // 展示状态收起即可
        if (getExpandedKeys().includes(key)) {
            setExpandedKeys(getExpandedKeys().filter((i) => i !== key));
            return;
        }
        // 已加载的节点直接展开即可
        if (loadedKeys.includes(key)) {
            setExpandedKeys([...getExpandedKeys(), key]);
            return;
        }
        // 折叠状态，复用加载节点的逻辑，只是少了loading效果
        await loadVehicleData(fleet);
        setExpandedKeys([...getExpandedKeys(), key]);
    };
    const getTreeTitleNode = (p: TreeNodeType) => {
        // @ts-ignore
        const { vStates = [], title = '', isLeaf, vOnlineNumber, vNumber } = p;
        const BadgeColor =
            vehicleStateConfig?.find((i: any) => i.stateId === vStates[0])?.stateColor || '#D9D9D9';
        return (
            <div className={classNames('tree-title')} onClick={() => !p.isLeaf && onFleetClick(p)}>
                {/* @ts-ignore */}
                <div className="vehicle-names" title={title}>
                    {isLeaf && showState && (
                        <Badge color={BadgeColor} className="vehicle-status-badge" />
                    )}
                    {title}
                </div>
                {p.isLeaf
                    ? null
                    : showState && (
                          <span className="vehicle-counts">
                              {vOnlineNumber || 0}/{vNumber || 0}
                          </span>
                      )}
            </div>
        );
    };
    const getTreeNode = (data: TreeNodeType[], onlySelectOnline: boolean) => {
        return data.map((item) => (
            <TreeNode
                value={item.key}
                key={item.key}
                selectable={item.isLeaf}
                // @ts-ignore
                disabled={onlySelectOnline && item.isLeaf && item.onlineState !== 1}
                title={getTreeTitleNode(item)}
                isLeaf={item.isLeaf}
                search={item.title}
            >
                {item.children && item.children.length > 0
                    ? // @ts-ignore
                      getTreeNode(item.children, onlySelectOnline)
                    : null}
            </TreeNode>
        ));
    };
    const treeDataNode = useMemo(() => {
        return getTreeNode(treeData, onlySelectOnline) as JSX.Element[];
    }, [treeData, onlySelectOnline]);

    const vehicleDataNode = useMemo(() => {
        return getTreeNode(vehicle2TreeNode(uniqBy(vehicleList, 'vId')), onlySelectOnline);
    }, [vehicleList, onlySelectOnline]);

    const [filteredNode, setFilteredNode] = useState<JSX.Element[]>([]);

    const { run: searchDebounceFn } = useDebounceFn(() => {
        const searchText = innerSearchValue.current;
        const data = vehicleDataNode.filter((i) =>
            i.props.search.toUpperCase().includes(searchText.toUpperCase()),
        );
        // fix bug 67914 避免性能问题，和产品已达成一致，最多显示搜索的500条
        setFilteredNode(data.slice(0, 500));
    });
    // 内部维护一个 innerSearchValue 是为了解决将大数据量直接给 TreeSelect 组件,TreeSelect 组件在频繁输入搜索字符时，存在卡顿的性能问题
    const onSearch = (value: any) => {
        //搜索之前情况上次搜索的内容
        setFilteredNode([]);
        const searchText = value.slice(0, 50);
        setSearchValue(searchText);
        innerSearchValue.current = searchText;
        searchDebounceFn();
    };
    useEffect(() => {
        onTreeNodeGenerate?.();
    }, [treeData]);

    const treeExpandChange = (keys: any) => {
        setExpandedKeys(keys);
    };
    return (
        <div
            className={classNames('vehicle-tree-select', {
                dark: type === 'dark',
            })}
        >
            {/* @ts-ignore */}
            <TreeSelect
                size={size}
                style={{ width: '100%' }}
                disabled={disabled}
                dropdownClassName={classNames('vehicle-tree-drop-box', {
                    dark: type === 'dark',
                })}
                listHeight={400}
                placeholder={i18n.t('message', '请选择车辆')}
                treeLine={false}
                loadData={loadVehicleData}
                treeExpandedKeys={expandedKeys}
                onTreeExpand={treeExpandChange}
                treeLoadedKeys={loadedKeys}
                onSelect={onSelect}
                value={selectedVehicle}
                showSearch={true}
                searchValue={searchValue}
                onSearch={onSearch}
                getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                filterTreeNode={false}
            >
                {!!searchValue ? filteredNode : treeDataNode}
            </TreeSelect>
        </div>
    );
};

const VehicleTree = forwardRef<RefVehicleTreeSelectProps, VehicleTreeSelectModel>(
    VehicleTreeSelect,
) as (
    props: React.PropsWithChildren<VehicleTreeSelectModel> & {
        ref?: React.Ref<RefVehicleTreeSelectProps>;
    },
) => React.ReactElement;

export default VehicleTree;
