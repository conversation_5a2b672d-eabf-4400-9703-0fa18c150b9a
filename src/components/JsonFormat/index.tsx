import { Input } from '@streamax/poppy';
import type { InputProps } from '@streamax/poppy/lib/input';
import type { FC } from 'react';
import './index.less';

interface PropsType extends InputProps {
    text: string;
}

const JsonFormat: FC<PropsType> = (props) => {
    function formatJsonString(text: string) {
        if (!text) {
            return text;
        }
        try {
            const obj = JSON.parse(text);
            if (typeof obj != 'object') {
                return text;
            }
            return JSON.stringify(obj, undefined, 4);
        } catch (err) {
            return text;
        }
    }

    return (
        <Input.TextArea
            className="format-json-input"
            disabled
            autoSize={{
                minRows: 16,
            }}
            value={formatJsonString(props.text)}
        />
    );
};

export default JsonFormat;
