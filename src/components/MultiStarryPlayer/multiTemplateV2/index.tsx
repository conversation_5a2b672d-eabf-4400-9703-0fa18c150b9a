/*
 * @LastEditTime: 2025-03-13 17:31:41
 */
import { StarryPlayer } from '@base-app/runtime-lib';
import MultiLayoutCount from './components/MultiLayoutCount';
import './index.less';
import React, { ReactNode, useCallback, useEffect, useMemo } from 'react';
import { useLocation } from '@base-app/runtime-lib/core';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

const { DefaultTemplate, LiveVideoMoreSetting } = StarryPlayer.templates;

const MultiMoreSetting = () => {
    const { pathname } = useLocation();
    const getMoreSettingsList = (components: any[]) => {
        const showList = ['FrameRateAndBitRate', 'PlayMode', 'PlayAspectRatio'];
        const filterComponents = (components || []).filter((component) =>
            showList.includes(component.componentName),
        );
        filterComponents.unshift(MultiLayoutCount);
        return filterComponents;
    };
    return (
        <LiveVideoMoreSetting
            key="multiLiveVideoMoreSetting"
            getMoreSettingsList={getMoreSettingsList}
            defaultMoreSettingCacheKey={'collected-key-multi-' + pathname}
        />
    );
};

const MultiTemplate = (props: { vehicleNumber: string }) => {
    const renderRight = (components: ReactNode[]) => {
        const newComponents = components.slice(0);
        const realtimeRecordIndex = newComponents.findIndex(
            (item) => item.key === 'RealtimeRecord',
        );
        realtimeRecordIndex > -1 &&
            newComponents.splice(realtimeRecordIndex, 1);
        const liveVideoMoreSettingIndex = newComponents.findIndex(
            (item) => item.key === 'LiveVideoMoreSetting',
        );
        liveVideoMoreSettingIndex > -1 &&
            newComponents.splice(
                liveVideoMoreSettingIndex,
                1,
                <MultiMoreSetting />,
            );
        return newComponents;
    };
    const customControlBar = useMemo(() => {
        return {
            renderRight,
        };
    }, []);
    const toolBarLeftContent = useCallback(
        (props) => {
            return (
                <div className="base-video-vehicle-number">
                    <OverflowEllipsisContainer
                        tooltip={{
                            preventBubble: true,
                            overlayStyle: {
                                "maxWidth": "200px",
                            }
                        }}
                    >
                        {props?.channelAlias || props?.vehicleNumber}
                    </OverflowEllipsisContainer>
                </div>
            );
        },
        [props.vehicleNumber],
    );
    return (
        <DefaultTemplate
            {...props}
            customControlBar={customControlBar}
            toolBarLeftContent={toolBarLeftContent}
        />
    );
};
export default React.memo(MultiTemplate);
