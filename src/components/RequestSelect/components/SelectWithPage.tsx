import { useState, useEffect } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { Select, Spin } from '@streamax/poppy';
import { useDebounceFn, useUpdateEffect } from '@streamax/hooks';
interface SelectOption {
    value: string | number;
    label: string | number;
}
type Value = string;
type Props = {
    getOptions: (text: Value) => Promise<SelectOption[]>;
    onInitDone?: () => void;
    hunt?: boolean;
    [Props: string]: any;
};
export default ({ getOptions, onInitDone, hunt, ...reset }: Props) => {
    const { value = '', initOption } = reset;
    const [options, setOptions] = useState<SelectOption[]>([...initOption]);
    const [loading, setLoading] = useState<boolean>(true);
    const [propsConfig, setPropsConfig] = useState({});
    useEffect(() => {
        initial();
    }, [value]);
    useUpdateEffect(() => {
        if (options.length) {
            return;
        }
        setOptions(initOption);
    }, [initOption]);
    const initial = () => {
        if (!!options.find((item) => item.value == value)) {
            setPropsConfig({});
            const item = options.find((item) => item.value == value) || undefined;
            getData('', item);
        } else {
            if (hunt && value && !initOption.length)
                setPropsConfig({ searchValue: i18n.t('name', '加载中...') });
            getData(value);
        }
    };
    const onBlur = () => {
        initial();
    };
    const getList = async (text: string, item?: SelectOption) => {
        setLoading(true);
        try {
            const data = (getOptions && (await getOptions(text))) || [];
            setOptions((prev) => {
                if (prev?.length == 0) onInitDone && onInitDone();
                let _data = [];
                if (item) {
                    if (data.find((itemData) => itemData.value == item?.value)) {
                        _data = data;
                    } else {
                        _data = [...data, item];
                    }
                } else {
                    _data = data;
                }
                return _data || [];
            });
            if (hunt) setPropsConfig({});
            setLoading(false);
        } catch (error) {
            console.error(error);
            setOptions([]);
            setLoading(false);
        }
    };
    const { run: getData } = useDebounceFn(
        (value: string, item?: SelectOption) => {
            getList(value, item);
        },
        {
            wait: 200,
        },
    );
    const handleSearch = (value: string) => {
        getData(value);
    };
    const handleClear = () => getList('');
    return (
        <Select
            placeholder={i18n.t('message', '请输入内容')}
            showSearch
            allowClear
            filterOption={false}
            options={options}
            onSearch={handleSearch}
            onClear={handleClear}
            {...propsConfig}
            onBlur={onBlur}
            {...reset}
        />
    );
};
