import { RightOutlined } from '@ant-design/icons';
import { useDebounceFn } from '@streamax/hooks';
import { Badge, Popover, Spin, Tooltip, message } from '@streamax/poppy';
import {
    IconMore,
    IconVideoFill,
    IconVideoTapeFill,
    IconServerPlayFill,
    IconTrajectory02Fill,
    IconListenFill,
    IconTextDownloadFill,
    IconIntercom,
    IconFusePlayFill,
    IconUnbind03Line,
    IconFleetFill,
} from '@streamax/poppy-icons';
import { i18n, Auth, StarryAbroadIcon } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { useRef, useState } from 'react';
import { VEHICLE_STATES } from '@/const/vehicle';
import {
    getRealtimeVideoVehicleDetail,
    VehicleOrderState,
    VehicleStateMenu
} from '@/service/vehicle';
import { getVehicleDetail } from '@/service/vehicle';
import type {
    OperateMenu,
    VehicleTreeType,
} from '@/types/pageReuse/realtimeMonitoring';
import { getDeviceStates } from '@/utils/commonFun';
import { getCustomItems } from '@/utils/pageReuse';
import './VehicleOperation.scope.less';
import { ModeType } from '../index-v2';
import { useRTDataStore, getRTDataStore } from '@/modules/vehicle-tree/data/realtime-data/useRTDataManager';

interface VehicleOperationProps extends VehicleTreeType {
    data?: any;
    // focusVehicles?: any[];
    onClickOperation?: (type: string, data: any) => void; // 点击车辆操作
    monitorMode?: ModeType;
    vehicleTreeType?: 'FocusVehicles' | 'monitorGroupVehicle';
    getIsHovering?: (isHovering: boolean) => void;
    instanceId?: string;
}

const VehicleOperation = (props: VehicleOperationProps) => {
    /**定制项 */
    const { getVehicleOperateMenu } = props;

    const {
        data,
        onClickOperation = () => {},
        monitorMode,
        vehicleTreeType,
        getIsHovering,
        instanceId = 'default'
    } = props;
    const { ONLINE_STATE, OFFLINE_STATE, DORMANCY_STATE } = VEHICLE_STATES;
    const rtDataStore = getRTDataStore(instanceId);
    const vehicleStateConfig = rtDataStore((state) => state.stateConfig);

    const [detailData, setVehicleDetail] = useState();
    const [loading, setLoading] = useState(false);
    const vehicleOperationPopoverRef = useRef(null);
    const { deviceList = [] } = data;

    const { run: onClickOperationBounce } = useDebounceFn(onClickOperation, {
        wait: 600,
        leading: true,
        trailing: false,
    });
    const { deviceList: detailDeviceList = [] } = detailData || {
        deviceList: [],
    };
    const onMouseEnter = () => {
        // 多个设备才查询，主要用于获取设备别名，单设备没有必要查询
        if (deviceList.length > 1 && !detailData && !loading) {
            setLoading(true);
            getVehicleDetail({
                vehicleId: data.vId,
                fields: 'device',
            })
                .then((data) => {
                    setVehicleDetail(data);
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    };

    const renderDevice =
        !loading && deviceList.length > 1 ? detailDeviceList : deviceList;
    const _operationEnable = async (
        device: any,
        showState: string[],
        validateOffLine = true,
    ) => {
        if (!device) {
            message.warn(i18n.t('message', '设备不存在'));
            return false;
        }
        const currentInfo = await getRealtimeVideoVehicleDetail({
            vehicleId: data.vId,
        });
        if (currentInfo.vehicleState === VehicleStateMenu.DISABLE) {
            message.warn(i18n.t('message', '车辆被停用'));
            return;
        } else if (currentInfo.vehicleState === VehicleStateMenu.NOT_ACTIVATED) {
            message.warn(i18n.t('message', '设备离线'));
            return;
        }
        const latest = (currentInfo?.deviceList || []).find(
            (item: any) => item.deviceId === device?.deviceId,
        );
        if (!latest) {
            message.warn(i18n.t('message', '设备不存在'));
            return false;
        }
        // 不存在表示离线不能操作   validateOffLine 给行业层复用点击按钮时，是否需要进行离线消息提示的判断
        if (
            !showState.includes(OFFLINE_STATE) &&
            latest?.onlineState != 1 &&
            validateOffLine
        ) {
            message.warn(i18n.t('name', '设备离线'));
            return false;
        }
        return latest;
    };
    const { run: operationEnable } = useDebounceFn(_operationEnable, {
        wait: 300,
        leading: true,
        trailing: false,
    });
    // 操作集合
    const OperationMap: OperateMenu[] = [
        {
            name: i18n.t('name', '实时视频'),
            key: 'realTimeVideo',
            icon: <IconVideoFill />,
            showState: [ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:realtime.video',
        },
        {
            name: i18n.t('name', '单车护航'),
            key: 'singleVehicleEscort',
            icon: <IconFleetFill />,
            showState: [ONLINE_STATE],
            showDeviceList: false,
            code: '@base:@page:realtime.monitoring@action:single.vehicle.escort',
        },
        {
            name: i18n.t('name', '设备回放'),
            key: 'devicePlayback',
            icon: <IconVideoTapeFill />,
            showState: [ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:device.playback',
        },
        {
            name: i18n.t('name', '服务器回放'),
            key: 'servePlayback',
            icon: <IconServerPlayFill />,
            showState: [OFFLINE_STATE, ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:serve.playback',
        },
        {
            name: i18n.t('name', '视频回放'),
            key: 'mixPlayback',
            icon: <IconFusePlayFill />,
            showState: [OFFLINE_STATE, ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:mix.playback',
        },
        {
            name: i18n.t('name', '轨迹回放'),
            key: 'trackPlayback',
            icon: <IconTrajectory02Fill />,
            showState: [OFFLINE_STATE, ONLINE_STATE, DORMANCY_STATE],
            showDeviceList: false,
            code: '@base:@page:realtime.monitoring@action:track.playback',
        },
        // {
        //     name: i18n.t('name', '设备唤醒'),
        //     key: 'deviceWakeUp',
        //     icon: <IconDetails />,
        //     showState: [2],
        //     code: '@base:@page:realtime-monitoring@action:deviceWakeUp',
        // },
        {
            name: i18n.t('name', '监听'),
            key: 'listen',
            icon: <IconListenFill />,
            showState: [ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:listen',
        },
        {
            name: i18n.t('name', '对讲'),
            key: 'talk',
            icon: <IconIntercom />,
            showState: [ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:Intercom',
        },
        // 单车对讲，133044定制功能。跳转到独立的对讲页面
        {
            name: i18n.t('name', '车辆对讲'),
            key: 'vehicleTalk',
            icon: <IconIntercom />,
            showState: [ONLINE_STATE],
            showDeviceList: true,
            code: '@base:@page:realtime.monitoring@action:VehicleIntercom',
        },
        {
            name: i18n.t('name', '消息下发'),
            key: 'messageSend',
            closePopover: true,
            icon: <IconTextDownloadFill />,
            showState: [ONLINE_STATE],
            code: '@base:@page:realtime.monitoring@action:messagesend',
        },
        // {
        //     name: data.isFocusVehicle ? i18n.t('name', '取消重点关注') : i18n.t('name', '重点关注'),
        //     key: 'focus',
        //     icon: <IconStar02 />,
        //     showState: [ONLINE_STATE, OFFLINE_STATE, DORMANCY_STATE],
        // },
        vehicleTreeType === 'monitorGroupVehicle' && {
            name: i18n.t('name', '移除监控组'),
            key: 'removeMonitorGroup',
            icon: <IconUnbind03Line />,
            showDeviceList: true,
            filterDeviceList: true,
            showState: [OFFLINE_STATE, ONLINE_STATE, DORMANCY_STATE],
        },
    ]
        .filter(Boolean)
        .filter((item) =>
            monitorMode === 'videoMode'
                ? [
                      'singleVehicleEscort',
                      'devicePlayback',
                      'servePlayback',
                      'mixPlayback',
                      'trackPlayback',
                      'messageSend',
                      'removeMonitorGroup',
                  ].includes(item.key)
                : true,
        );
    // console.log('====[[[getVehicleOperateMenu]]]====', data);
    
    const newOperationMap = getCustomItems(
        getVehicleOperateMenu,
        OperationMap,
        {
            VEHICLE_STATES,
            deviceList: data?.deviceList?.[0],
            data,
        },
    );

    /** 设备popover */
    const getDevicePopover = (
        deviceList: any[],
        key: any,
        operation: any,
        newData: any,
    ) => {
        return (
            <Spin spinning={loading}>
                {deviceList.length > 0 && (
                    <div className="operation-group">
                        {deviceList.map((pDevice: any, index: number) => {
                            // pDevice 信息残缺，只有 deviceId
                            const device = detailDeviceList.find(
                                (item: any) =>
                                    item?.deviceId === pDevice.deviceId,
                            ) as any;
                            if (!device) return null;
                            const deviceState = getDeviceStates(
                                pDevice,
                                vehicleStateConfig,
                            );
                            const badgeColor =
                                deviceState?.[0]?.stateColor || '#D9D9D9';
                            return (
                                <div
                                    // @ts-ignore
                                    key={device.deviceId}
                                    className="operation-item"
                                    onClick={async (e: any) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        const latestDevice =
                                            await operationEnable(
                                                device,
                                                operation.showState,
                                                operation.validateOffLine,
                                            );
                                        latestDevice &&
                                            onClickOperation(key, {
                                                ...newData,
                                                deviceInfo: latestDevice,
                                            });
                                    }}
                                >
                                    <OverflowEllipsisContainer tooltip={false}>
                                        <Badge
                                            color={badgeColor}
                                            className="device-status-badge"
                                            style={{
                                                width: 24,
                                                textAlign: 'center',
                                            }}
                                        />
                                        {loading
                                            ? ''
                                            : device.deviceAlias ||
                                              device.deviceNo}
                                    </OverflowEllipsisContainer>
                                </div>
                            );
                        })}
                    </div>
                )}
            </Spin>
        );
    };

    // 自定义操作功能
    function generateOperation() {
        const { vStates = [], deviceList = [], monitorDeviceList = [] } = data;
        const newData = {
            ...data,
            deviceList: detailDeviceList?.length
                ? detailDeviceList
                : deviceList,
        };
        return (
            <div className="operation-group vehicle-operation-group">
                {newOperationMap.map((operation: any) => {
                    const {
                        icon,
                        name,
                        key,
                        showDeviceList,
                        filterDeviceList,
                        code,
                        closePopover,
                    } = operation;
                    const newDeviceList = filterDeviceList
                        ? monitorDeviceList
                        : deviceList;
                    if (
                        operation.showState.some((state: string) =>
                            vStates.includes(state),
                        )
                    ) {
                        const renderItem =
                            !code || Auth.check(code) ? (
                                <div
                                    className="operation-item"
                                    onClick={async (e: any) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        if (
                                            showDeviceList &&
                                            newDeviceList.length > 1
                                        ) {
                                            return;
                                        } else if (showDeviceList) {
                                            const device = filterDeviceList
                                                ? newDeviceList[0]
                                                : renderDevice[0];
                                            const latestDevice =
                                                await operationEnable(
                                                    device,
                                                    operation.showState,
                                                    operation.validateOffLine,
                                                );
                                            latestDevice &&
                                                onClickOperationBounce(key, {
                                                    ...newData,
                                                    deviceInfo: latestDevice,
                                                });
                                        } else {
                                            onClickOperationBounce(key, {
                                                ...newData,
                                            });
                                        }
                                        if (closePopover) {
                                            // 点击之后关闭popover
                                            // @ts-ignore
                                            vehicleOperationPopoverRef.current?.close();
                                        }
                                        operation.onClick &&
                                            operation.onClick(data);
                                    }}
                                    key={key}
                                >
                                    <span className="operation-item-icon">
                                        <StarryAbroadIcon>
                                            {icon}
                                        </StarryAbroadIcon>
                                    </span>
                                    <OverflowEllipsisContainer tooltip={false}>
                                        {name}
                                    </OverflowEllipsisContainer>
                                    {showDeviceList &&
                                        newDeviceList.length > 1 && (
                                            <span className="operation-item-other-info">
                                                {/* <IconMore /> */}
                                                <RightOutlined className="arrow-right-icon" />
                                            </span>
                                        )}
                                </div>
                            ) : null;

                        if (showDeviceList && newDeviceList.length > 1) {
                            return (
                                <Popover
                                    placement="rightTop"
                                    overlayClassName="operation-popover"
                                    key={key}
                                    content={getDevicePopover(
                                        newDeviceList,
                                        key,
                                        operation,
                                        newData,
                                    )}
                                    overlayStyle={{ padding: 0 }}
                                >
                                    {renderItem}
                                </Popover>
                            );
                        } else {
                            return renderItem;
                        }
                    }
                    return null;
                })}
            </div>
        );
    }
    // 全部资源都没有时就不展示hover了
    const showPopover = () => {
        const { vStates } = data;
        const stateOperationMap = newOperationMap.filter((item) => {
            return (item.showState || []).some((state: string) =>
                vStates.includes(state),
            );
        });
        return stateOperationMap.some((item) => {
            if (!item.code) return true;
            return Auth.check(item.code);
        });
    };
    // TODO 设备为空时有ui问题
    return showPopover() ? (
        <Popover
            overlayClassName="operation-popover"
            placement="rightTop"
            ref={vehicleOperationPopoverRef}
            content={generateOperation()}
            onVisibleChange={(visible) => {
                getIsHovering && getIsHovering(visible);
            }}
        >
            <div style={{ height: 34, display: 'flex', alignItems: 'center' }}>
                <IconMore onMouseEnter={onMouseEnter} />
            </div>
        </Popover>
    ) : null;
};

export default VehicleOperation;
