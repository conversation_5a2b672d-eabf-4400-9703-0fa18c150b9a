import type { Vehicle } from '@/hooks/useRTData/useRealtimeMonitoring';
import type { TreeProps } from '@streamax/rc-tree';

/**
 * 车辆监控组树和重点车辆树的公共属性
 */
export interface BaseCommonMonitoringTree {
    /** 树选中回调*/
    onSelect: TreeProps['onSelect'];
    /**缓存数据 */
    cacheData: any;
    /**
     * 过滤车辆状态
     * @param focusVehicleList
     * @returns
     */
    filterVehicleCallbacks: (focusVehicleList: Vehicle[]) => Vehicle[];
}


/**
 * title的类型
 */
export enum TitleTypeEnum {
    FLEET = 'fleet',
    INVENTED_FLEET = 'invented-fleet',
    VEHICLE = 'vehicle',
    DEVICE="device"
}