/*
 * @LastEditTime: 2022-11-30 17:03:45
 */
import React from 'react';
import { usePlayerState } from '../store';
import { FMPTYPE } from '../types';

const useFrameRateAndBitRate = () => {
    const { dispatch, frameRateAndBitRate, disabledWidgets } = usePlayerState(state => ({
        dispatch: state.dispatch,
        frameRateAndBitRate: state.frameRateAndBitRate,
        disabledWidgets: state.disabledWidgets
    }), true);

    const setFrameRateAndBitRate = (value: FMPTYPE[]) => {
        dispatch({ type: 'setFrameRateAndBitRate', payload: value });
        dispatch({
            type: 'setActionInfo',
            payload: {
                actionName: 'frameRateAndBitRate',
                actionPayload: value,
            },
        });
    };

    function _disabled() {
        return disabledWidgets.includes('frameRateAndBitRate');
    }

    return {
        setFrameRateAndBitRate,
        disabled: _disabled(),
        frameRateAndBitRate,
    };
};

export default useFrameRateAndBitRate;
