/**
 * @description 倒计时hook
 */

import { isNil } from 'lodash';
import { useEffect, useRef } from 'react';
import { PLAYER_STATUS } from '../constant';
import { usePlayerState } from '../store';
import useShouldRender from './useShouldRender';

const INTERVAL = 1000;
let timer: any = null;
// 标记是否可倒计时（比如回放剪辑时，是需要停止计时的，即使播放状态改变）
let countDownAvailable = true;

const useCountDown = () => {
    const {
        remainingTime,
        autoCloseDuration = 0,
        autoCloseAvailable,
        playerStatus,
        dispatch,
    } = usePlayerState(state => ({
        remainingTime: state.remainingTime,
        autoCloseDuration: state.autoCloseDuration,
        autoCloseAvailable: state.autoCloseAvailable,
        playerStatus: state.playerStatus,
        dispatch: state.dispatch,
    }), true);
    const shouldRender = useShouldRender('closeTime');

    // 由于在setInterval中无法取到最新值，故暂时需要用ref存储一份数据
    const remainingTimeRef = useRef(remainingTime);
    const autoCloseDurationRef = useRef(autoCloseDuration);
    const autoCloseAvailableRef = useRef(autoCloseAvailable);

    useEffect(() => {
        remainingTimeRef.current = remainingTime;
    }, [remainingTime]);

    useEffect(() => {
        autoCloseDurationRef.current = autoCloseDuration;
    }, [autoCloseDuration]);

    useEffect(() => {
        autoCloseAvailableRef.current = autoCloseAvailable;
    }, [autoCloseAvailable]);
    // 数据备份结束

    useEffect(() => {
        const { pause, playing, loading } = PLAYER_STATUS;
        if ([pause, playing].includes(playerStatus) && autoCloseAvailable && countDownAvailable) {
            start();
        } else {
            stop();
        }
    }, [autoCloseAvailable, playerStatus]);

    // 设置自动关闭视频的计时时长
    const setAutoCloseDuration = (duration: number) => {
        dispatch({ type: 'setAutoCloseDuration', payload: duration });
        if (isNil(remainingTime)) {
            dispatch({ type: 'setRemainingTime', payload: duration });
        }
    };

    // 重置倒计时，重新从设置的自动关闭时间开始计时
    const reset = () => {
        dispatch({
            type: 'setRemainingTime',
            payload: autoCloseDurationRef.current,
        });
    };

    // 停止倒计时，停止计时，并恢复剩余时长
    const stop = () => {
        reset();
        pause();
    };

    // 开始倒计时
    const start = () => {
        clearInterval(timer);
        timer = setInterval(() => {
            // 倒计时结束时，停止计时
            if (!remainingTimeRef.current || remainingTimeRef.current <= 0) {
                stop();
                // 如果启用了倒计时结束自动关闭视频，则需要发送关闭视频指令，并指定更改状态为stop
                if (autoCloseAvailableRef.current) {
                    dispatch({
                        type: 'setActionInfo',
                        payload: {
                            actionName: 'closeVideo',
                            actionPayload: PLAYER_STATUS.stop,
                        },
                    });
                }
            } else {
                dispatch({
                    type: 'setRemainingTime',
                    payload: (remainingTimeRef.current || autoCloseDurationRef.current) - 1,
                });
            }
        }, INTERVAL);
    };

    // 暂停倒计时
    const pause = () => {
        clearInterval(timer);
    };

    const setAvailable = (available: boolean) => {
        countDownAvailable = available;
        stop();
    };

    return {
        remainingTime,
        reset,
        stop,
        start,
        pause,
        setAutoCloseDuration,
        shouldRender,
        setAvailable,
    };
};

export default useCountDown;
