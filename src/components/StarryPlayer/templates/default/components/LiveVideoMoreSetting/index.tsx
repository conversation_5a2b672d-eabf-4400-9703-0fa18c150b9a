/*
 * @LastEditTime: 2024-04-24 15:46:54
 */
/**
 * @description 回放的更多设置组件
 */
import { useState } from 'react';
import MoreSetting from '../../../../widgets/MoreSetting';
import { useLocation } from '@base-app/runtime-lib/core';
// @ts-ignore
import { Auth, StarryStorage } from '@base-app/runtime-lib';
import useMoreSettings from '@/components/StarryPlayer/hooks/useMoreSettings';
import { useAsyncEffect } from '@streamax/hooks';
import {
    ChannelSelect,
    LayoutCount,
    LayoutType,
    FrameRateAndBitRate,
    PlayMode,
} from '../../../../components';
import { usePlayerState } from '../../../../store';
import type { WidgetNameType } from '../../../../types';
import type { ComponentItem } from '../../../../widgets/MoreSetting';
type LiveVideoMoreSettingProps = {
    getMoreSettingsList?: (components: any[]) => any[];
    defaultMoreSettingCacheKey?: string;
    [key: string]: any;
}

const LiveVideoMoreSetting: React.FC = (props: LiveVideoMoreSettingProps) => {
    const storage = StarryStorage();
    const [collectedComponents, setCollectedComponents] = useState<string[]>([]);
    const { pageCode, authWidgets } = usePlayerState(state => ({
        pageCode: state.pageCode,
        authWidgets: state.authWidgets,
    }), true);
    const { disabled, hiddenWidgets } = useMoreSettings();
    const moreSettingsList = [
        ChannelSelect,
        LayoutCount,
        LayoutType,
        FrameRateAndBitRate,
        PlayMode,
    ];
    let list = props?.getMoreSettingsList?.(moreSettingsList) || moreSettingsList;

    if (pageCode) {
        list = list.filter((item) => {
            if (!item.componentName) return true;
            const actionCodeName = (item.componentName.slice(0, 1).toLowerCase() +
                item.componentName.slice(1)) as WidgetNameType;
            const resourceCode = `${pageCode}@action:${actionCodeName}`;
            if (authWidgets.length > 0 && !authWidgets.includes(actionCodeName)) return true;
            return Auth.check(resourceCode);
        });
    }

    const components: ComponentItem[] = list.map((Com) => {
        return {
            name: Com.componentName,
            component: <Com {...props} />,
        };
    });
    const { pathname } = useLocation();
    const cacheKey = props?.defaultMoreSettingCacheKey || `collected-key-${pathname}`;
    useAsyncEffect(async () => {
        try {
            const data = await storage.getItem(cacheKey);
            const originDataStr = localStorage.getItem(cacheKey);
            const originData = JSON.parse(originDataStr || '[]');
            const result = data ? data : originData;
            setCollectedComponents(result);
        } catch (error) {
            // empty
        }
    }, []);

    const handleChange = (v: string[]) => {
        try {
            setCollectedComponents(v);
            storage.setItem(cacheKey, v);
        } catch (error) {
            // empty
        }
    };

    return (
        <MoreSetting
            components={components}
            maxFollowCount={3}
            disabled={disabled}
            hiddenWidgets={hiddenWidgets}
            value={collectedComponents}
            onChange={handleChange}
        />
    );
};
// @ts-ignore
LiveVideoMoreSetting.componentName = 'LiveVideoMoreSetting';
export default LiveVideoMoreSetting;
