/**
 * @description 关闭视频UI小组件
 */

import React from 'react';
import cn from 'classnames';
import { IconClose01 } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';
import { useDebounceFn } from '@streamax/hooks';
import { COM_CLS_PREFIX_WIDGET } from '../../constant';
import BaseWidget, { BaseWidgetProps } from '../BaseWidget';
import { BaseWidgetExtendProps } from '../types';

const clsPrefix = COM_CLS_PREFIX_WIDGET;

export interface CloseVideoWidgetProps
    extends BaseWidgetProps,
        BaseWidgetExtendProps {}

const widgetName = 'closeVideo';

const CloseVideo: React.FC<CloseVideoWidgetProps> & {
    componentName: string;
} = (props) => {

    const { className, style, onClick = () => {}, disabled, children } = props;

    const { run: onClickDebounce } = useDebounceFn(onClick, { wait: 500 });

    return (
        <div className={cn(`${clsPrefix}-closevideo`, className)} style={style}>
            <BaseWidget
                title={i18n.t('message', '关闭视频')}
                onClick={onClickDebounce}
                disabled={disabled}
                toolTipPlacement='top'
            >
                {children ? children : <IconClose01 />}
            </BaseWidget>
        </div>
    );
};

CloseVideo.componentName = widgetName;

export default CloseVideo;
