/**
 * @description 关闭视频【逻辑组件】
 */
import React from 'react';
import CloseVideoWidget from '../../widgets/CloseVideo';
import useClose from '../../hooks/useClose';

export interface CloseVideoComponentProps {
    // 通道号
    channel?: number;
    // 点击
    onClick?: (channel?: string) => void;
}

const CloseVideo: React.FC<CloseVideoComponentProps> & {
    componentName: string;
} = (props) => {
    const { channel, onClick } = props;

    const { shouldRender, disabled, close } = useClose(channel);

    function handleOnclick() {
        if (!disabled) {
            close();
            onClick?.();
        }
    }

    if (shouldRender) {
        return <CloseVideoWidget {...props} onClick={handleOnclick} disabled={disabled} />;
    }
    return null;
};

CloseVideo.componentName = 'CloseVideo';

export default CloseVideo;
