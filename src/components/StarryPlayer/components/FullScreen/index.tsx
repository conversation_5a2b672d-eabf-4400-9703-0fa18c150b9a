/*
 * @LastEditTime: 2024-04-24 10:26:16
 */
/**
 * @description 全屏【逻辑组件】
 */
import React, { useContext } from 'react';
import FullScreenWidget from '../../widgets/FullScreen';
import useFullScreen from '../../hooks/useFullScreen';
export interface FullScreenComponentProps {
    // 通道号
    channel?: string;
    // 点击
    onClick?: (channel?: string) => void;
}

const FullScreen: React.FC<FullScreenComponentProps> & {
    componentName: string;
} = (props) => {
    const { channel, onClick } = props;

    const {
        shouldRender,
        disabled,
        fullScreen: fullScreenFn,
        fullscreen: fullScreenState,
    } = useFullScreen(channel);

    function handleOnclick() {
        if (!disabled) {
            fullScreenFn();
            onClick?.();
        }
    }
    if (shouldRender) {
        return (
            <FullScreenWidget
                {...props}
                onClick={handleOnclick}
                disabled={disabled}
                fullscreen={fullScreenState}
                toolTipPlacement={channel ? '' : 'top'}
            />
        );
    }
    return null;
};

FullScreen.componentName = 'FullScreen';

export default FullScreen;
