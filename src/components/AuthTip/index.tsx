/*
 * @LastEditTime: 2023-05-24 14:49:25
 */
import { Tooltip } from '@streamax/poppy';
import { IconInformation } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';
import './index.less';

type AuthTipProps = {
    text?: string;
    show?: boolean;
};

const AuthTip = (props: AuthTipProps) => {
    const { show, text = i18n.t('name', '暂无查看该数据的权限') } = props;
    return show ? (
        <Tooltip
            title={text}
            overlayClassName="auth-tip-popover"
            className="question-icon"
            placement="right"
        >
            <IconInformation
                style={{ marginLeft: '8px' }}
                className="auth-tip-default-icon-information"
            />
        </Tooltip>
    ) : null;
};
export default AuthTip;
