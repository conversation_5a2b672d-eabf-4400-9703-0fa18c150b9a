import { Spin } from '@streamax/poppy';
interface ComponentLoadingProps {
    loading: boolean;
    top?: number;
    center?: boolean;
}
export default (props: ComponentLoadingProps) => {
    const { loading, top = 24, center = false } = props;
    const style = {
        position: 'absolute' as any,
        left: '0',
        top: '0',
        width: '100%',
        height: '100%',
        paddingTop: `${top}px`,
    };
    const centerStyle = {
        position: 'absolute' as any,
        left: '0',
        top: '0',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    };
    return loading ? <Spin style={center ? centerStyle : style} /> : null;
};
