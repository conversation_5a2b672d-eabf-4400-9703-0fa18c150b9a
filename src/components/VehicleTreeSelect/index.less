.vehicle-tree-select {
    position: relative;
    &.dark {
        .tree-selection-box {
            color: rgba(255, 255, 255, 0.85);
            background-color: #202020;
            .tree-selection-item {
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .poppy-select-tree-switcher-noop {
        width: 0;
    }
    .poppy-select-tree-treenode-switcher-close {
        .poppy-select-tree-switcher-noop {
            width: 24px;
        }
    }

    .tree-selection-box {
        position: absolute;
        top: 34px;
        left: 0;
        z-index: 1051;
        box-sizing: border-box;
        width: 100%;
        max-height: 400px;
        padding: 8px 4px;
        overflow-y: auto;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.15);
        .poppy-empty {
            margin: 24px 0;
        }
        .poppy-empty-image {
            height: 45px;
        }
        .tree-selection-item {
            width: 100%;
            padding-left: 12px;
            overflow: hidden;
            line-height: 32px;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
            &:hover {
                background-color: rgba(89, 126, 247, 0.15);
            }
            &.disabled {
                cursor: not-allowed;
                // pointer-events: none;
            }
        }
    }
    .poppy-select-selector{
        .tree-node-vehicle{
            .tree-node-title{
                width: 96%
            }
        }
        .poppy-select-selection-item {
            .tree-node-title {
                display: flex;
                .vehicletree-number {
                    flex: 1;
                    overflow: hidden;
                    font-weight: normal;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}
.tree-title {
    position: relative;
    &.child-title {
        &:before {
            position: absolute;
            bottom: 10px;
            left: -40px;
            width: 9px;
            height: 33px;
            border-bottom: 1px dotted #d7dadf;
            border-left: 1px dotted #d7dadf;
            content: '';
        }
        &.first-child:before {
            height: 24px;
        }
    }
    &.leaf-title:after {
        position: absolute;
        top: 7px;
        left: -18px;
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: #888;
        border-radius: 50%;
        content: '';
    }
    &.leaf-title.red:after {
        background-color: #de6465;
    }
    &.leaf-title.green:after {
        background-color: #09ab69;
    }
}

.vehicle-tree-drop-box {
    /*  下拉树宽度样式写死到第二个控件的一半位置 */
    width: 384px;
    &.dark {
        background-color: #202020;
        .poppy-select-tree {
            color: rgba(255, 255, 255, 0.85);

            background-color: #202020;
            .poppy-select-tree-node-content-wrapper.poppy-select-tree-node-selected {
                background: rgba(89, 126, 247, 0.15);
            }
            .poppy-select-tree-node-content-wrapper {
                &:focus,
                &:active,
                &:hover {
                    background: rgba(89, 126, 247, 0.15);
                }
            }
        }
        .poppy-select-tree-treenode-disabled .poppy-select-tree-node-content-wrapper {
            color: rgba(255, 255, 255, 0.85);
        }
    }
    .poppy-select-tree-treenode {
        .poppy-select-tree-indent-unit {
            width: 12px;
        }
        .poppy-select-tree-node-content-wrapper {
            overflow: hidden !important;
            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            .poppy-select-tree-title {
                .tree-node-content{
                    .tree-node-title{
                        display: flex;
                        .vehicletree-number {
                            width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            display: block;
                        }
                    }
                }
                .tree-node-fleet {
                    display: flex;
                    justify-content: space-between;
                    gap: 8px;
                    .tree-node-title{
                        flex: 1;
                        width: 0;
                    }
                    .tree-node-info{
                        min-width: 40px;
                        max-width: 120px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        text-align: right;
                    }
                }
                //padding-right: 45px;
                .vehicle-names {
                    display: inline-block;
                    max-width: calc(100% - 40px) !important;
                    overflow: hidden;
                    // max-width: 70%;
                    font-weight: 400 !important;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    // 车辆状态要求8px
                    .vehicle-status-badge {
                        width: 24px;
                        text-align: center;
                        .poppy-badge-status-dot {
                            width: 8px;
                            height: 8px;
                        }
                    }
                }
                .vehicle-counts {
                    padding-right: 20px;
                }
            }
        }
    }
}
