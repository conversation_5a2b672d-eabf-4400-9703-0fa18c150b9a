import VehicleNumber from '@/components/VehicleTree/compontents/TitleComp/VehicleNumber';
import { TitleTypeEnum } from '@/components/VehicleTree/compontents/type';
import useTreeTitleStore from '../../useTreeTitleStore';
export default ({ vId, title, fId, type, instanceId }: { vId: string; title: string; fId: string; type: TitleTypeEnum; instanceId: string}) => {
	const getVehicleNumber = useTreeTitleStore(state => state?.getVehicleNumber);

	return <VehicleNumber title={title} vId={vId} fId={fId} type={type} instanceId={instanceId} getVehicleNumber={getVehicleNumber} />;
};