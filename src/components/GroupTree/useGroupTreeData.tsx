/**
 * 获取车组
 */
import { useRef, useEffect, useState } from 'react';
import { i18n, utils } from '@base-app/runtime-lib';
import { getSimpleFleetList } from '@/service/fleet';
import { useDebounceFn } from '@streamax/hooks';
import { getSetting } from '@/components/FleetVichileSort';
import type { DataNode } from 'antd/lib/tree';
export interface Fleet {
    fId: string;
    parentId: string;
    fName: string;
    path: string[];
    childrenIds: string[];
    vNumber: number;
    vOnlineNumber: number;
    createTime: number;
}

export const unGroupFleetId = '0';
const useFleetTreeData = () => {
    // 映射车组id 及其 父车组id（包含自身）
    const fleetParentsMap = useRef<Record<string, Set<string>>>({});
    // 全量车组数据
    const [fleetList, setFleetList] = useState<Fleet[]>([]);
    //  返回给Tree组件渲染的数据
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [loadedKeys, setLoadedKeys] = useState<string[]>([]);
    // 是否初始化数据完成
    const [loaded, setLoaded] = useState(false);
    // 已加载的树数据
    const loadedData = useRef({
        fleetList: [] as Fleet[],
    });
    // 根据后端数据规则模拟未分组数据
    const unGroupFleet = {
        fleetId: unGroupFleetId,
        fleetName: i18n.t('name', '未分组'),
        path: `/0`,
        createTime: 0,
    };

    const initAllData = async () => {
        const reqs = [
            // 所有车组
            getSimpleFleetList(),
        ];
        const resArr = await Promise.all(reqs);
        // vehicleRes 结构 ["fid,vid,vno","fid1,vid1,vno1",...]
        const [fleetsRes] = resArr;
        // 缓存每个车队的子车队
        const fleetChildrenMap: Record<string, Set<string>> = {};

        // 构造车队为指定结构
        const allFleetList: Fleet[] = fleetsRes.map((item: any) => {
            const { fleetId: fId, fleetName, createTime } = item;
            let { parentId } = item;
            if(parentId == 0 || !parentId) parentId = ''
            const path = parentId + '/' + fId
            const pathIds = path.split('/').filter((i: string) => i);
            const childrenIds = fleetChildrenMap[fId] ? Array.from(fleetChildrenMap[fId]) : [];
            // 以每个车组id为key。值为包含自己以及所有的父节点id。为的是在后续统计 在线数、 总数 时，避免再次遍历所有车组。节省时间
            if (!fleetParentsMap.current.hasOwnProperty(fId)) {
                fleetParentsMap.current[fId] = new Set([fId]);
            }
            childrenIds.forEach((childrenId) => {
                if (!fleetParentsMap.current.hasOwnProperty(childrenId)) {
                    fleetParentsMap.current[childrenId] = new Set([childrenId]);
                }
                fleetParentsMap.current[childrenId].add(fId);
            });
            return {
                fId,
                parentId,
                fName: fleetName,
                path: pathIds,
                childrenIds,
                vNumber: 0,
                vOnlineNumber: 0,
                createTime,
            };
        });
        setFleetList(allFleetList);
    };

    //  加载全量车组、车辆数据
    useEffect(() => {
        initAllData().then(() => {
            setLoaded(true);
        });
    }, []);

    const { run: updateTreeData } = useDebounceFn(
        () => {
            //  更新状态
            loadFleets(
                loadedKeys.length > 0
                    ? loadedKeys
                    : fleetList.filter((i) => i.path.length === 1).map((i) => i.fId),
            );
        },
        { wait: 100 },
    );
    useEffect(() => {
        updateTreeData();
    }, [fleetList]);

    const generateTreeData = (fleetData: Fleet[]) => {
        const data: any[] = [];
        const orderSetting = getSetting();
        fleetData.forEach((fleet) => {
            data.push({
                ...fleet,
                id: fleet.fId,
                key: fleet.fId,
                type: 'fleet',
                title: fleet.fName,
                isLeaf: false,
            });
        });

        const sorts = ['type'];
        const orders = ['asc'];
        if (orderSetting.top) {
            sorts.push('vOnlineNumber');
            orders.push('desc');
        }
        orders.push(orderSetting.order);
        sorts.push(orderSetting.sort);
        // arrayToTree sort order 定义反了
        const treeData = utils.general.arrayToTree(data, {
            idFieldName: 'id',
            parentIdFieldName: 'parentId',
            order: sorts.join(','),
            sort: orders.join(','),
        });
        // 未分组放最后面
        const unGroupIndex = treeData.findIndex((i) => i.fId === unGroupFleet.fleetId);
        if (unGroupIndex !== -1) {
            treeData.push(treeData.splice(unGroupIndex, 1)[0]);
        }
        return treeData;
    };

    /**
     * 加载车组节点
     * @param fIds  只接受不带前缀的fleetId
     * @returns
     */
    const loadFleets = async (fIds: string[]) => {
        if (!fIds || fIds.length === 0) return;
        // 找出要加载节点的父节点
        const ids = fleetList
            .filter((i) => fIds.includes(i.fId))
            .reduce((total, i) => {
                return total.concat(i.path);
            }, [] as string[]);
        const newLoadedKeys = Array.from(new Set(loadedKeys.concat(ids)));
        const fleetData = fleetList.filter(
            (i) => newLoadedKeys.includes(i.fId) || newLoadedKeys.includes(i.parentId),
        );

        loadedData.current = {
            fleetList: fleetData,
        };
        const treeData = generateTreeData(fleetData);

        setTreeData(treeData);
        // 打造成Tree一样的数据
        setLoadedKeys(newLoadedKeys);
    };
    return {
        // 数据是否已初始化完成
        loaded,
        treeData,
        loadFleets,
        loadedKeys, // 已经加载得节点
        fleetList, // 全量车组数据
    };
};
export default useFleetTreeData;
