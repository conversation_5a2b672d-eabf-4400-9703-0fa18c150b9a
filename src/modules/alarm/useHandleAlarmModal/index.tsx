import { useState, useEffect, useRef } from 'react';
import { Form, Input, message } from '@streamax/poppy';
import { getAppGlobalData, i18n, StarryAbroadFormItem } from '@base-app/runtime-lib';
// @ts-ignore
import { StarryModal } from '@base-app/runtime-lib';
import { handleAlarmPlayLabel } from '../../../service/alarm';
import LabelSelect from '../../label/LabelSelect';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import './index.less';

const MAX_SELECTED_NUMBER = 5; // 最多打标个数
const useHandleAlarmModal = () => {
    const [form] = Form.useForm<any>();
    const modal = useRef<ReturnType<typeof StarryModal.info>>();
    const result = useRef<{
        resolve: (value: boolean | PromiseLike<boolean>) => void;
        reject: (reason?: any) => void;
    }>({ resolve: () => {}, reject: () => {} });
    const destroyAndReset = () => {
        modal.current?.destroy();
        form.resetFields();
    };
    useEffect(() => {
        return () => {
            destroyAndReset();
        };
    }, []);
    const handleSubmitProcess = async (alarmIds: string[]) => {
        await form.validateFields().then((values) => {
            const { labelIds = [], content } = values;
            const params = {
                alarmIdList: alarmIds,
                labelIdList: labelIds.map((id: number) => String(id)) || [],
                content: content || '',
            };
            modal.current?.update({
                okButtonProps: {
                    loading: true,
                },
            });
            handleAlarmPlayLabel(params)
                .then(async () => {
                    message.success(i18n.t('message', '处理成功'));
                    result.current.resolve(true);
                    destroyAndReset();
                })
                .catch(() => {
                    modal.current?.update({
                        okButtonProps: {
                            loading: false,
                        },
                    });
                });
        });
    };

    const onCancel = () => {
        result.current.resolve(false);
        destroyAndReset();
    };

    const openModal = (
        alarmIds: string[],
        initialValues: {
            labelIds?: string[];
            content?: string;
        },
    ) => {
        form.setFieldsValue(initialValues);
        const content = (
            <Form layout="vertical" form={form}>
                <StarryAbroadFormItem
                    className="handle-alarm-modal-form-tag-item"
                    label={i18n.t('name', '标签')}
                    name="labelIds"
                    extra={i18n.t('message', '最多只能选择{maxCount}个标签', {
                        maxCount: MAX_SELECTED_NUMBER,
                    })}
                >
                    <LabelSelect maxSelectedNumber={MAX_SELECTED_NUMBER} />
                </StarryAbroadFormItem>
                <StarryAbroadFormItem label={i18n.t('name', '处理内容')} name="content">
                    <Input.TextArea
                        allowClear
                        rows={5}
                        maxLength={500}
                        placeholder={i18n.t('message', '请输入内容')}
                        showCount
                    />
                </StarryAbroadFormItem>
            </Form>
        );
        modal.current?.destroy();
        modal.current = StarryModal.info({
            className: 'handle-alarm-modal',
            closable: true,
            maskClosable: false,
            focusTriggerAfterClose: false,
            title: (
                <OverflowEllipsisContainer style={{ paddingRight: 8 }}>
                    <span>{i18n.t('name', '处理报警')}</span>
                </OverflowEllipsisContainer>
            ),
            content,
            visible: false,
            onCancel: onCancel,
            onOk: () => {
                handleSubmitProcess(alarmIds);
            },
            okButtonProps: {
                loading: false,
            },
        });
    };

    const handlerAlarm = async (
        alarmIds: string[],
        initialValues: {
            labelIds?: string[];
            content?: string;
        } = {
            labelIds: [],
            content: ''
        },
    ) => {
        return new Promise<boolean>((resolve, reject) => {
            result.current = { resolve, reject };
            openModal(alarmIds, initialValues);
        });
    };
    return { handlerAlarm };
};
export default useHandleAlarmModal;
