import { useRef, useEffect, useState } from 'react';
import { SOCKET_TOPIC } from '@/const/socket-topic';
import type { SOCKET_DATA } from '@/const/socket-topic';
import useSubscribe from '@/hooks/useSubscribe';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { getDynamicDriverList } from '@/service/driver';
import { useWebsocketMessage } from '@base-app/runtime-lib';
import { useSafeStore, withSafeStore } from '../common/withSafeStore';
import StoreFactory from '@/class/StoreFactory';

export type RTDriverData = SOCKET_DATA['BASE_DRIVER_STATUS'];
export interface VehicleDriverData {
    time: number;
    drivers: Omit<RTDriverData, 'time'>[];
}
export type VehicleDriverMap = Record<string, Omit<RTDriverData, 'time'>>;
interface DriverMap {
    [key: string]: VehicleDriverMap;
}
/**
 * 实时数据Store start
 */
type State = {
    driverMap: VehicleDriverMap;
};

type Actions = {
    updateDriverMap: (updateData: VehicleDriverMap) => Promise<void>;
    resetStore: () => void
};
export const getMergeData = (driverItem: any) => {
    return {
        driverList: driverItem ? [driverItem] : [],
        driver: driverItem?.driverName || '',
        driverId: driverItem?.driverId,
    };
};
/**
 * 创建司机Store的函数
 * @param instanceId 实例ID
 * @returns 司机Store实例
 */
const createDriverStore = (instanceId: string =  'default') => {
    return create<State & Actions>()(
        withSafeStore(
            (set) => ({
            driverMap: {},
            updateDriverMap: async (updateData: VehicleDriverMap) => {
                set((state) => {
                    return {
                        driverMap: Object.assign(state.driverMap, updateData)
                    }
                });
            },
            resetStore: () => {
                set((state) => {
                    return {
                        driverMap: {}
                    }
                });
            }
        })
        ),
    );
};

// 创建StoreFactory实例
export const driverStoreFactory = new StoreFactory<ReturnType<typeof createDriverStore>>(createDriverStore);

/**
 * 获取指定instanceId的司机Store实例
 * @param instanceId 实例ID，默认为'default'
 * @returns 司机Store实例
 */
export const getDriverStore = (instanceId: string = 'default') => {
    return driverStoreFactory.getInstance(instanceId);
};

// 为了兼容现有代码，保留useDriverStore的导出
export const useDriverStore = getDriverStore('default');
/**
 * 数据Store end
 */
const useRTDriverData = (vehicleListLength: number, instanceId: string = 'default') => {
    // 使用instanceId获取对应的store实例
    const driverStore = getDriverStore(instanceId);
    // 待处理增量数据, vId 作为key
    const rtTodoDataRef = useRef<Record<string, RTDriverData>>({});
    // 待处理全量数据
    const defaultVehicleDriverData = { time: 0, drivers: [] };
    const todoVehicleDriverDataRef = useRef<VehicleDriverData>({ ...defaultVehicleDriverData });
    const firstLoad = useRef(true);
    // 根据全量数据和增量数据聚合车辆状态
    const generateVehicleDriverState = async () => {
        // 存在全量数据，则使用全量数据与增量数据聚合
        const { drivers, time } = todoVehicleDriverDataRef.current;
        let rtTodoData = Object.values(rtTodoDataRef.current);
        // 不存在需要处理的数据
        if (rtTodoData.length === 0 && drivers.length === 0) {
            return {
                data: {},
                changed: false,
            };
        }
        // 清空全量数据
        todoVehicleDriverDataRef.current = { ...defaultVehicleDriverData };
        // 清空增量数据
        rtTodoDataRef.current = {};
        let newDrivers: Omit<RTDriverData, 'time'>[] = [];
        const newDriverState = {};
        if (drivers.length > 0 && time !== 0) {            
            for (let index = 0; index < drivers.length; index++) {
                const i = drivers[index];
                newDriverState[i.vehicleId] = i;                
            }
        }
        // 过滤掉增量中时间小于全量time的,此类数据为过期的信息
        for (let index = 0; index < rtTodoData.length; index++) {
            const i = rtTodoData[index];
            if(Number(i.time) > time) {
                newDriverState[i.vehicleId] = i;
            }
        }
        const changeVIds = Object.keys(newDriverState)
        return {
            data: newDriverState,
            changeData: Object.keys(newDriverState), // 兼容写法，后续使用下面的changed取代
            changed: changeVIds.length > 0 ? {
                data: newDriverState,
                vIds: changeVIds
            } : false,
        };
    };
    const queryAll = () => {
        getDynamicDriverList({
            page: 1,
            pageSize: 1e6,
        })
            .then(({ time, list }) => {
                todoVehicleDriverDataRef.current = {
                    time,
                    drivers: list,
                };
            })
            .finally(() => {
                firstLoad.current = false;
            });
    };
    // 增量消息处理
    const handlerRTData = (data: RTDriverData) => {
        // 过期数据丢弃
        const oldData = rtTodoDataRef.current[data.vehicleId];
        if (oldData && oldData.time >= data.time) {
            return;
        }
        rtTodoDataRef.current[data.vehicleId] = data;
    };
    const rationCountNumber = 5000;    
    const [topic, setTopic] = useState('');
    // 指定车辆状态订阅是否启用
    const vehicleSubscribeEnable = topic === SOCKET_TOPIC.BASE_DRIVER_STATUS_RATION;
    useEffect(()=>{
        let topic = '';
        if (vehicleListLength > rationCountNumber) {
            topic = SOCKET_TOPIC.BASE_DRIVER_STATUS_RATION;
        } else if (vehicleListLength > 0) {
            topic = SOCKET_TOPIC.BASE_DRIVER_STATUS;
        }
        setTopic(topic);
    }, [vehicleListLength]);
    useSubscribe(topic, handlerRTData, queryAll);
    // 限频订阅时，补加视觉范围内车辆订阅
    const { send, subscribe } = useWebsocketMessage();
    const preSubscribeVids = useRef<string[]>([]);
    const noSendVids = useRef<string[]>([]);
    const updateVehicleSubscribe = (subscribeVids: string[])=>{
        // vehicleSubscribeEnable 和订阅都是异步的，可能设置这个先于订阅，所以需要判断
        if(!vehicleSubscribeEnable) {
            noSendVids.current = subscribeVids;
            return;
        }
        if(preSubscribeVids.current === subscribeVids) return;
        preSubscribeVids.current = subscribeVids;
        send(
            [
                'b.subscribe',
                {
                    type: 'b.driver_status_require_vehicle',
                    values: subscribeVids,
                    increment: false,
                },
            ],
            () => {},
            { topic: SOCKET_TOPIC.BASE_DRIVER_STATUS_REQUIRE },
        );
        return;
    };
    useSubscribe(vehicleSubscribeEnable ? SOCKET_TOPIC.BASE_DRIVER_STATUS_REQUIRE : '', handlerRTData, ()=>{
        updateVehicleSubscribe(noSendVids.current);
    });
    // 限频策略结束


    // 外部获取数据函数，不会主动抛出数据
    const updateData = async (): Promise<{changed: boolean}> => {
        // let st = Date.now();
        if (!firstLoad.current) {
            const {data, changed} = await generateVehicleDriverState();
            // return () => {
            //     useDriverStore.getState().updateDriverMap(data.data);
            // };
            await driverStore.getState().updateDriverMap(data);
            // console.warn('数据预处理司机', Date.now() - st);
            return {
                changed
            };
        }
        return new Promise((resolve, reject) => {
            // 定时检测 直到全量查询返回
            const timer = setInterval(async () => {
                if (!firstLoad.current) {
                    clearInterval(timer);
                    try {
                        const {data, changed} = await generateVehicleDriverState();
                        await driverStore.getState().updateDriverMap(data);
                        // console.warn('数据预处理司机', Date.now() - st);
                        resolve({
                            changed
                        });
                    } catch (error) {
                        reject(error);
                    }
                }
            }, 0);
        });
    };
    useSafeStore(driverStore, driverStore.getState().resetStore);
    useEffect(() => {
        queryAll();
    }, []);
    return {
        // updateData,
        useDriverStore: driverStore,
        updateVehicleSubscribe,
        getData: updateData,
        getMergeData
    };
};

export default useRTDriverData;
