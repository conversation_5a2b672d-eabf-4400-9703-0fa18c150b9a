import { message } from '@streamax/poppy';
import {
    cacheKey,
    EnumPerformanceLevel,
    performanceTool,
    usePerformanceStrategy,
} from './usePerformanceStrategy';
import { i18n, StarryStorage } from '@/runtime-lib';
import { registerStrategy } from '@/performance-strategy-register';
export { usePerformanceStrategy };
import { iframeLoadedSendMessage, isInIframe, registerIframeReceive } from './iframeUtils';

window.performanceTool = window.performanceTool || performanceTool;
window.usePerformanceStrategy = window.usePerformanceStrategy || usePerformanceStrategy;

/**
 * 初始化阶段，避免进行档位提示
 */
let init = false;
export const initPerformance = async () => {
    console.log('===[[init]====', init);
    if (init) return;  
    registerIframeReceive();
    const storage = StarryStorage();    
    const cache = await storage.getItem(cacheKey);
    await registerStrategy();
    init = true;
    iframeLoadedSendMessage();
    // iframe中，不需要执行下面逻辑
    if (isInIframe) return;  

    const vehicleCount = async () => {
        try {
            const { staticDetection, realtimeDetection } = usePerformanceStrategy.getState();
            if (cache) {
                usePerformanceStrategy.setState(cache);
            } else {                
                await staticDetection();
            }
            // 延迟一定时间后开启动态探测，这是为了避开初始化阶段的长任务，性能探测对初始化过程的探测，没有实际意义。
            setTimeout(() => {
                realtimeDetection();
            }, 10000);
            
        } catch (error) {
            console.error('error==', error);
            setTimeout(() => {
                vehicleCount();
            }, 100000);
        }
    };
    vehicleCount();
};



