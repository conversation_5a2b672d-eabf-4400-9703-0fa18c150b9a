/*
 * @LastEditTime: 2025-03-21 15:36:30
 */
import type React from 'react';
import type { FormInstance } from '@streamax/poppy/lib/form';

export interface ParamItem {
    alarmType?: string;
    paramName: string;
    paramValue: string;
}

export interface StrategyComponentProps {
    activeKey?: string;
    type?: number;
    title: string | ((editing: boolean) => React.ReactNode);
    authCode: {
        editCode: string;
        copyCode?: string;
        channelTypeCode?: string;
    };
    data?: Record<string, any>;
    updateData: (type: number | string) => void;
    appId?: number;
    openIsWhen?: (flag: boolean) => void;
    detailLoading?: boolean;
    onUploadStatus?: (status: boolean) => void;
    strategyType?: 'default' | 'customEdit' | 'customAdd'
}

export interface FormProps {
    editing: boolean;
    form?: FormInstance<any>;
    inDetailPage: boolean;
    appId?: number | string;
    emailTemplateId?: string;
    alarmType?: string;
    showUser?: boolean;
    onUploadStatus?: (status: boolean) => void;
}
