@import (reference) '~@streamax/poppy-themes/starry/index.less';
@import (reference) '~@streamax/poppy-themes/starry/abroad.less';
.video-close-time-form {
    .switch-item {
        margin-bottom: 24px;
        .poppy-form-item {
            margin-bottom: 0;
            &-control {
                display: none;
            }
        }
        
        .form-tip {
            color: @starry-text-color-placeholder;
            font-size: 14px;
            margin-top: 8px;
        }
    }

    .custom-form-item-box {
        display: flex;
        align-items: center;
        position: relative;

        .suffix {
            margin-left: 8px;
            padding-top: 8px;
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);

            .fold-icon {
                white-space: nowrap;
                user-select: none;
            }
        }
        .suffix::abroad {
            position: unset;
            left: unset;
            top: unset;
            transform: unset;
        }
    }
}