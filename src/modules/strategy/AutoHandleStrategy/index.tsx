import { Divider, Form, message, Select, Switch } from '@streamax/poppy';
import { i18n, useSystemComponentStyle } from '@base-app/runtime-lib';
import { pick, pickBy } from 'lodash';
import StrategyConfig from '../StrategyConfig';
import { paramListTransfer } from './util';
import { formatAlarmTypeGroup } from '../utils';
import { editStrategy } from '@/service/strategy';
import type { StrategyComponentProps } from '../types';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import IconDescribe from '@/components/IconDescrible';
import { RspFormLayout } from '@streamax/responsive-layout';
import HandleEffectiveScope from './HandleEffectiveScope';
import { useState } from 'react';

export default (props: StrategyComponentProps) => {
    const {
        type,
        title,
        data = {},
        updateData,
        authCode,
        openIsWhen,
        activeKey,
        appId,
    } = props;
    const inDetailPage = true;
    const [voiceForm] = Form.useForm();
    const [mapLockForm] = Form.useForm();
    const [videoForm] = Form.useForm();
    const [effectiveScopeForm] = Form.useForm();
    const [switches, setSwitches] = useState({});

    const { isAbroadStyle } = useSystemComponentStyle();

    const getAlarmGroup = (
        list: Record<'label' | 'value', string>[] = [],
        settings: Record<string, any>,
    ) => {
        // 已设置了的报警类型数据
        const settingMap: any = pickBy(settings, (item) => {
            return item.voicePrompt || item.mapVehicleLock || item.openVideo;
        });
        return formatAlarmTypeGroup(list, settingMap);
    };

    const onSubmit = async (values: Record<string, any>) => {
        const { configureId = '', configureName, configureType } = data;
        const paramList = paramListTransfer.toSubmit(values);
        await editStrategy({
            configureId,
            configureName,
            configureType,
            paramList,
            operationModelCode: 'defaultPolicy',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'edit',
            logParams: [{ data: '报警处理设置' }],
        });
        message.success(i18n.t('message', '操作成功'));
        await updateData(type);
    };

    const voiceFormSetting = (editing?: boolean) => (
        <Form
            labelWidth={340}
            form={voiceForm}
            onValuesChange={(value) => {
                setSwitches({
                    ...switches,
                    ...value,
                });
            }}
            layout="vertical"
            key="voice"
        >
            <div className="common-title flex-between">
                <span className="common-title-text">
                    {i18n.t('name', '声音提示')}
                </span>
                <Form.Item
                    label={''}
                    labelWidth={0}
                    name="voicePrompt"
                    className="clear-margin-bottom"
                    valuePropName="checked"
                >
                    <Switch
                        disabled={inDetailPage && !editing}
                        checkedChildren={
                            !isAbroadStyle && i18n.t('state', '开')
                        }
                        unCheckedChildren={
                            !isAbroadStyle && i18n.t('state', '关')
                        }
                    />
                </Form.Item>
            </div>
            <Divider
                style={{
                    color: '#1A000000',
                    margin: '0',
                }}
            />
        </Form>
    );

    const mapLockFormSetting = (editing?: boolean) => {
        return (
            <Form
                labelWidth={340}
                form={mapLockForm}
                layout="vertical"
                key="mapLock"
                onValuesChange={(value) => {
                    setSwitches({
                        ...switches,
                        ...value,
                    });
                }}
            >
                <div className="common-title flex-between">
                    <span className="common-title-text">
                        {i18n.t('name', '地图锁定车辆')}
                    </span>
                    <Form.Item
                        label={''}
                        labelWidth={0}
                        name="mapVehicleLock"
                        valuePropName="checked"
                        className="clear-margin-bottom"
                    >
                        <Switch
                            disabled={inDetailPage && !editing}
                            checkedChildren={
                                !isAbroadStyle && i18n.t('state', '开')
                            }
                            unCheckedChildren={
                                !isAbroadStyle && i18n.t('state', '关')
                            }
                        />
                    </Form.Item>
                </div>
                <Divider
                    style={{
                        color: '#1A000000',
                        margin: '0',
                    }}
                />
            </Form>
        );
    };
    const videoFormSetting = (editing?: boolean) => {
        return (
            <Form
                labelWidth={340}
                form={videoForm}
                layout="vertical"
                scrollToFirstError
                key="video"
                onValuesChange={(value) => {
                    setSwitches({
                        ...switches,
                        ...value,
                    });
                }}
            >
                <div className="common-title flex-between">
                    <span className="common-title-text">
                        {i18n.t('name', '打开视频')}
                    </span>
                    <Form.Item
                        label={''}
                        labelWidth={0}
                        name="openVideo"
                        valuePropName="checked"
                        className="clear-margin-bottom"
                    >
                        <Switch
                            disabled={inDetailPage && !editing}
                            checkedChildren={
                                !isAbroadStyle && i18n.t('state', '开')
                            }
                            unCheckedChildren={
                                !isAbroadStyle && i18n.t('state', '关')
                            }
                        />
                    </Form.Item>
                </div>
                <Divider
                    style={{
                        color: '#1A000000',
                        margin: '0',
                    }}
                />
            </Form>
        );
    };

    const handleEffectiveScope = (editing?: boolean) => {
        const show = Object.values(switches).some((item) => item);
        return show ? (
            <HandleEffectiveScope
                appId={appId}
                inDetailPage={inDetailPage}
                editing={editing}
                form={effectiveScopeForm}
            />
        ) : null;
    };
    const onTypeChange = (currentTypeData: Record<string, boolean>) => {
        const switchData = pick(currentTypeData, ['mapVehicleLock', 'openVideo', 'voicePrompt']);
        setSwitches(switchData);
    };
    return (
        <StrategyConfig
            activeKey={activeKey}
            title={title}
            hasAlarmList
            configData={paramListTransfer.toRender(data.paramList || [])}
            inDetailPage={inDetailPage}
            authCode={authCode}
            onTypeChange={onTypeChange}
            defaultSetting={{
                messageReceiverSwitch: false,
                messageUserReceiverSwitch: false,
            }}
            childs={[
                {
                    renderForm: voiceFormSetting,
                    ref: voiceForm,
                },
                {
                    renderForm: mapLockFormSetting,
                    ref: mapLockForm,
                },
                {
                    renderForm: videoFormSetting,
                    ref: videoForm,
                },
                {
                    renderForm: handleEffectiveScope,
                    ref: effectiveScopeForm,
                },
            ]}
            getAlarmGroup={getAlarmGroup}
            onSubmit={onSubmit}
            openIsWhen={openIsWhen}
            checkEdit={true}
            modalType="autoHandle"
            appId={appId}
            className="autoHandle-scope"
        />
    );
};
