/*
 * @LastEditTime: 2025-04-01 13:58:15
 */
import IconDescribe from '@/components/IconDescrible';
import { i18n, StarryAbroadFormItem as AFormItem } from '@base-app/runtime-lib';
import { Form, Switch } from '@streamax/poppy';
import { RspFormLayout } from '@streamax/responsive-layout';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import React, { useEffect, useRef } from 'react';
import './index.less';
import AuthRoleSelect from '@/runtime-lib/components/AuthRoleSelect';
import AuthUserSelect from '@/runtime-lib/components/AuthUserSelect';
import { FormInstance } from '@streamax/poppy/lib/form';
import { MAX_USER_ROL_SELECT_NUMBER } from '@/utils/constant';
export interface HandleEffectiveScopeProps {
    editing: boolean;
    form?: FormInstance<any>;
    inDetailPage: boolean;
    appId?: number | string;
    showUser?: boolean;
    alarmType?: string;
    components?: {
        roleSelect?: React.Component;
        userSelect?: React.Component;
    };
}
const HandleEffectiveScope = (props: HandleEffectiveScopeProps) => {
    const {
        editing,
        form,
        inDetailPage,
        appId,
        showUser = true,
        components,
    } = props;
    const validatorMaxRole = (_, value) => {
        if (value?.length > MAX_USER_ROL_SELECT_NUMBER) {
            return Promise.reject(
                new Error(
                    i18n.t('message', '最多选择{number}个生效角色', {
                        number: MAX_USER_ROL_SELECT_NUMBER,
                    }),
                ),
            );
        }
        return Promise.resolve();
    };
    const renderFormItem = () => {
        return (
            <div className="common-title effectiveScope-form-item-wrap">
                <div className="common-title-text">
                    <div className="common-title-text-name">
                        {i18n.t('name', '生效范围设置')}
                    </div>
                    <div className="common-title-text-tip">
                        {i18n.t(
                            'name',
                            '* 当前设置默认对所有用户生效，若需限定生效范围，可单独配置指定的生效角色和用户',
                        )}
                    </div>
                </div>
                <div className="effective-row-item">
                    <div className="effective-row-item-label">
                        <OverflowEllipsisContainer>
                            {i18n.t('name', '部分角色生效')}
                        </OverflowEllipsisContainer>
                    </div>
                    <Form.Item
                        label={i18n.t('name', '部分角色生效')}
                        name="messageRolePush"
                        valuePropName="checked"
                        noStyle
                    >
                        <Switch disabled={inDetailPage && !editing} />
                    </Form.Item>
                    <IconDescribe
                        disabled={inDetailPage && !editing}
                        content={i18n.t(
                            'message',
                            '开启开关后，产生报警时该设置会对配置的部分角色生效',
                        )}
                    />
                </div>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) => {
                        return (
                            prevValues.messageRolePush !==
                            curValues.messageRolePush
                        );
                    }}
                    noStyle
                >
                    {({ getFieldValue }) => {
                        return !getFieldValue('messageRolePush') ? (
                            <>
                                <div className="empty-height"></div>
                            </>
                        ) : null;
                    }}
                </Form.Item>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) => {
                        return (
                            prevValues.messageRolePush !==
                            curValues.messageRolePush
                        );
                    }}
                    noStyle
                >
                    {({ getFieldValue }) => {
                        return getFieldValue('messageRolePush') ? (
                            <>
                                <RspFormLayout layoutType="fixed">
                                    <RspFormLayout.Col>
                                        <div className="effective-row-item effective-row-item-form">
                                            {components?.roleSelect ? (
                                                components?.roleSelect
                                            ) : (
                                                <AFormItem
                                                    name="messageRoleReceiver"
                                                    className="form-item"
                                                    rules={[
                                                        {
                                                            validator:
                                                                validatorMaxRole,
                                                        },
                                                        {
                                                            required: true,
                                                            message: i18n.t(
                                                                'message',
                                                                '生效角色不能为空',
                                                            ),
                                                        },
                                                    ]}
                                                >
                                                    <AuthRoleSelect
                                                        appId={appId}
                                                        disabled={
                                                            inDetailPage &&
                                                            !editing
                                                        }
                                                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                                    />
                                                </AFormItem>
                                            )}
                                        </div>
                                    </RspFormLayout.Col>
                                </RspFormLayout>
                            </>
                        ) : null;
                    }}
                </Form.Item>
                {showUser ? (
                    <>
                        <div className="effective-row-item effective-row-item-user">
                            <div className="effective-row-item-label">
                                <OverflowEllipsisContainer>
                                    {i18n.t('name', '部分用户生效')}
                                </OverflowEllipsisContainer>
                            </div>
                            <Form.Item
                                label={i18n.t('name', '部分用户生效')}
                                name="messageUserPush"
                                valuePropName="checked"
                                noStyle
                            >
                                <Switch disabled={inDetailPage && !editing} />
                            </Form.Item>
                            <IconDescribe
                                disabled={inDetailPage && !editing}
                                content={i18n.t(
                                    'message',
                                    '开启开关后，产生报警时该设置会对配置的部分用户生效',
                                )}
                            />
                        </div>
                        <Form.Item
                            shouldUpdate={(prevValues, curValues) => {
                                return (
                                    prevValues.messageUserPush !==
                                    curValues.messageUserPush
                                );
                            }}
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                return getFieldValue('messageUserPush') ? (
                                    <>
                                        <RspFormLayout layoutType="fixed">
                                            <RspFormLayout.Col>
                                                <div className="effective-row-item">
                                                    {components?.userSelect ? (
                                                        components?.userSelect
                                                    ) : (
                                                        <AFormItem
                                                            name="messageUserReceiver"
                                                            className="form-item"
                                                            rules={[
                                                                {
                                                                    validator: (
                                                                        _,
                                                                        value,
                                                                    ) => {
                                                                        if (
                                                                            value?.length >
                                                                            MAX_USER_ROL_SELECT_NUMBER
                                                                        ) {
                                                                            return Promise.reject(
                                                                                new Error(
                                                                                    i18n.t(
                                                                                        'message',
                                                                                        '最多选择{number}个生效用户',
                                                                                        {
                                                                                            number: MAX_USER_ROL_SELECT_NUMBER,
                                                                                        },
                                                                                    ),
                                                                                ),
                                                                            );
                                                                        }
                                                                        return Promise.resolve();
                                                                    },
                                                                },
                                                                {
                                                                    required:
                                                                        true,
                                                                    message:
                                                                        i18n.t(
                                                                            'message',
                                                                            '生效用户不能为空',
                                                                        ),
                                                                },
                                                            ]}
                                                        >
                                                            <AuthUserSelect
                                                                disabled={
                                                                    inDetailPage &&
                                                                    !editing
                                                                }
                                                                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                                            />
                                                        </AFormItem>
                                                    )}
                                                </div>
                                            </RspFormLayout.Col>
                                        </RspFormLayout>
                                    </>
                                ) : null;
                            }}
                        </Form.Item>
                    </>
                ) : null}
            </div>
        );
    };
    return form ? (
        <Form
            labelWidth={340}
            form={form}
            layout="vertical"
            scrollToFirstError
            key="effectiveScope"
        >
            {renderFormItem()}
        </Form>
    ) : (
        renderFormItem()
    );
};
export default HandleEffectiveScope;
