/*
 * @LastEditTime: 2024-08-15 19:43:24
 */
import { ParamItem } from '../types';

// 转换paramList参数
export const paramListTransfer = {
    booleanKeys: ['needScreenSnap', 'mediaSwitch', 'blackBoxSwitch', 'aiFrameSwitch'],
    arrayKeys: ['picChannels', 'mediaChannels', 'networkTypes'],
    toSubmit: (uploadSetting: any) => {
        // 组装为数组结构
        const paramList: ParamItem[] = [];
        Object.keys(uploadSetting).forEach((alarmType) => {
            const itemVal: any = uploadSetting[alarmType];
            Object.keys(itemVal).forEach((key) => {
                // 如下 key 的值转为字符串（后端只存 string）
                let paramValue = itemVal[key];
                if (paramListTransfer.booleanKeys.includes(key)) {
                    paramValue = paramValue ? '1' : '0';
                }
                if (paramListTransfer.arrayKeys.includes(key)) {
                    paramValue = (paramValue || []).join(',');
                }
                paramList.push({
                    alarmType,
                    paramName: key,
                    paramValue,
                });
            });
        });
        return paramList;
    },
    toRender: (paramList: any) => {
        const uploadSetting: any = {};
        paramList.forEach((item: any) => {
            const { alarmType, paramName } = item;
            let paramValue = item.paramValue;
            // 如下 key 的值转为字符串（后端只存 string）
            if (paramListTransfer.booleanKeys.includes(paramName)) {
                paramValue = paramValue === '1';
            }
            if (paramListTransfer.arrayKeys.includes(paramName)) {
                if (paramName === 'networkTypes') {
                    // 网络模式的回填直接返回项为字符串的数组，项为数字存在交互性问题
                    paramValue = paramValue.split(',');
                } else {
                    paramValue = paramValue.split(',').map((item: string) => Number(item) || item);
                }
            }
            if (!uploadSetting.hasOwnProperty(alarmType)) {
                uploadSetting[alarmType] = {};
            }
            uploadSetting[alarmType][paramName] = paramValue;
        });
        return uploadSetting;
    },
};
