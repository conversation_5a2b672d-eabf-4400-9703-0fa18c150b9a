import { useEffect, useState } from 'react';
import { Checkbox, Divider, Form, InputNumber, message, Radio, Switch } from '@streamax/poppy';
import { i18n, StarryAbroadFormItem, useConfigParameter, useSystemComponentStyle } from '@base-app/runtime-lib';
import StrategyConfig from '../StrategyConfig';
import AllCheckbox from '@/components/AllCheckbox';
import { get, pickBy } from 'lodash';
import { paramListTransfer } from './util';
import { filterEvidenceParamData, formatAlarmTypeGroup } from '../utils';
import { editStrategy } from '@/service/strategy';
import type { StrategyComponentProps } from '../types';
import './index.less';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { checkboxOptions } from '@/utils/constant';
import { FormEdit } from '@/types/pageReuse/pageReuseBase';
import { useChannelData } from '@/hooks/useChannelData';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { EvidenceUploadReuse } from '@/types/pageReuse/startegy';
import { getCustomItems } from '@/utils/pageReuse';
export type EvidenceStrategyShareProps = FormEdit & EvidenceUploadReuse;
const EvidenceStrategy = (props: StrategyComponentProps & EvidenceStrategyShareProps) => {
    // 定制
    const { injectFormSubmit, getDefaultEvidenceUploadRightBlock } = props;
    // 定制
    const { type, title, data = {}, updateData, authCode, openIsWhen, activeKey, appId } = props;
    const inDetailPage = true;
    const [mediaForm] = Form.useForm();
    const [pictureForm] = Form.useForm();
    const [blackBoxForm] = Form.useForm();
    const { channelOptions } = useChannelData({ appId });
    const { isAbroadStyle } = useSystemComponentStyle();

    const { data: aiFrame = false } = useConfigParameter({ key: 'EXTRACT.EVIDENCE.AI.FRAME', options: {
        formatFn: (params) => {
            // @ts-ignore
            if (params == 1) {
                return true;
            }
            return false;
        },
    }});
    const getAlarmGroup = (
        list: Record<'label' | 'value', string>[] = [],
        settings: Record<string, any>,
    ) => {
        // 已设置了的报警类型数据
        const settingMap: any = pickBy(settings, (item) => {
            const { blackBoxSwitch, mediaSwitch, needScreenSnap } = item;
            return blackBoxSwitch || mediaSwitch || needScreenSnap;
        });
        // 为了兼容历史数据，把脏数据剔除，以免导致分组异常
        const filterSettingMap = filterEvidenceParamData(settingMap);
        return formatAlarmTypeGroup(list, filterSettingMap);
    };

    const onSubmit = async (values: any) => {
        const { configureId = '', configureName, configureType } = data;
        const paramList = paramListTransfer.toSubmit(values);
        const strategyConfig = {
            configureId,
            configureName,
            configureType,
            paramList,
            operationModelCode: 'defaultPolicy',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'edit',
            logParams: [{ data: '证据上传设置' }],
        };
        // 定制提交接口
        if (injectFormSubmit) {
            await injectFormSubmit({ ...strategyConfig });
        } else {
            await editStrategy(strategyConfig);
        }
        message.success(i18n.t('message', '操作成功'));
        await updateData(type);
    };

    const mediaFormSetting = (editing?: boolean) => {
        return (
            <Form
                labelWidth={340}
                form={mediaForm}
                layout="vertical"
                scrollToFirstError
                key="media"
            >
                <div className="common-title flex-between">
                    <span className="common-title-text">{i18n.t('name', '录像')}</span>
                    <Form.Item
                        label={''}
                        labelWidth={0}
                        name="mediaSwitch"
                        valuePropName="checked"
                        className="clear-margin-bottom"
                    >
                        <Switch
                            disabled={inDetailPage && !editing}
                            checkedChildren={!isAbroadStyle&&i18n.t('state', '开')}
                            unCheckedChildren={!isAbroadStyle&&i18n.t('state', '关')}
                        />
                    </Form.Item>
                </div>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.mediaSwitch !== curValues.mediaSwitch
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        return getFieldValue('mediaSwitch') ? (
                            <>
                                <Form.Item
                                    label={
                                        <span>
                                            <span>{i18n.t('name', '码流')}</span>
                                        </span>
                                    }
                                    rules={[{ required: true }]}
                                    name="streamType"
                                    initialValue={'1'}
                                >
                                    <Radio.Group disabled={inDetailPage && !editing}>
                                        <Radio value="1">
                                            <OverflowEllipsisContainer>{i18n.t('state', '主码流')}</OverflowEllipsisContainer>
                                        </Radio>
                                        <Radio value="2">
                                            <OverflowEllipsisContainer>{i18n.t('state', '子码流')}</OverflowEllipsisContainer>
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                                <Form.Item
                                    label={i18n.t('name', '上传通道')}
                                    rules={[{ required: true }]}
                                    name="mediaChannels"
                                >
                                    <AllCheckbox
                                        disabled={inDetailPage && !editing}
                                        options={channelOptions}
                                        label={
                                            <span>
                                                <span>{i18n.t('state', '全选')}</span>
                                                <span
                                                    className="form-label-tip"
                                                    title={i18n.t(
                                                        'message',
                                                        '（注：非权限通道不会生效）',
                                                    )}
                                                >
                                                    {i18n.t(
                                                        'message',
                                                        '（注：非权限通道不会生效）',
                                                    )}
                                                </span>
                                            </span>
                                        }
                                    />
                                </Form.Item>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '报警前（s）')}
                                    name="beginAt"
                                    className="form-item"
                                    rules={[{ required: true }]}
                                >
                                    <InputNumber
                                        disabled={inDetailPage && !editing}
                                        min={1}
                                        max={600}
                                        precision={0}
                                        placeholder={i18n.t('message', '请输入1-600的数字')}
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '报警后（s）')}
                                    name="endAt"
                                    className="form-item"
                                    rules={[{ required: true }]}
                                >
                                    <InputNumber
                                        disabled={inDetailPage && !editing}
                                        min={1}
                                        max={600}
                                        precision={0}
                                        placeholder={i18n.t('message', '请输入1-600的数字')}
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '网络模式')}
                                    name="networkTypes"
                                    className="form-item"
                                    style={{ width: '100%' }}
                                    rules={[{
                                        required: true,
                                        message: i18n.t('message', '网络模式不能为空'),
                                    }]}
                                    initialValue={checkboxOptions.map(item => item.value)}
                                >
                                    <Checkbox.Group
                                        disabled={inDetailPage && !editing}
                                    >
                                        {checkboxOptions.map((item: any) => {
                                            return (
                                                <Checkbox value={item.value} key={item.value}>
                                                    <OverflowEllipsisContainer>{item.label}</OverflowEllipsisContainer>
                                                </Checkbox>
                                            );
                                        })}
                                    </Checkbox.Group>
                                </StarryAbroadFormItem>
                                {aiFrame ? (
                                    <div className="ai-frame-wrap">
                                        <div className="ai-frame-title">
                                            {i18n.t('name', 'AI帧信息')}
                                        </div>
                                        <Form.Item
                                            noStyle
                                            name="aiFrameSwitch"
                                            valuePropName="checked"
                                            className="form-item"
                                        >
                                            <Switch disabled={inDetailPage && !editing} />
                                        </Form.Item>
                                    </div>
                                ) : null}
                            </>
                        ) : null;
                    }}
                </Form.Item>
                <Divider
                    style={{
                        color: '#1A000000',
                        margin: '0',
                    }}
                />
            </Form>
        );
    };

    const pictureFormSetting = (editing?: boolean) => {
        return (
            <Form labelWidth={340} form={pictureForm} layout="vertical" key="picture">
                <div className="common-title flex-between">
                    <span className="common-title-text">{i18n.t('name', '抓拍图片')}</span>
                    <Form.Item
                        label={''}
                        labelWidth={0}
                        name="needScreenSnap"
                        valuePropName="checked"
                        className="clear-margin-bottom"
                    >
                        <Switch
                            disabled={inDetailPage && !editing}
                            checkedChildren={!isAbroadStyle&&i18n.t('state', '开')}
                            unCheckedChildren={!isAbroadStyle&&i18n.t('state', '关')}
                        />
                    </Form.Item>
                </div>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.needScreenSnap !== curValues.needScreenSnap
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        return getFieldValue('needScreenSnap') ? (
                            <Form.Item
                                label={i18n.t('name', '抓拍通道')}
                                name="picChannels"
                                rules={[{ required: true }]}
                            >
                                <AllCheckbox
                                    disabled={inDetailPage && !editing}
                                    options={channelOptions}
                                />
                            </Form.Item>
                        ) : null;
                    }}
                </Form.Item>
                <Divider
                    style={{
                        color: '#1A000000',
                        margin: '0',
                    }}
                />
            </Form>
        );
    };

    const blackBoxFormSetting = (editing?: boolean) => (
        <Form labelWidth={340} form={blackBoxForm} layout="vertical" key="blackBox">
            <div className="common-title flex-between">
                <span className="common-title-text">
                    {i18n.t('name', '黑匣子（GPS信息、ACC信息、报警日志信息、OBD）')}
                </span>
                <Form.Item
                    label={''}
                    labelWidth={0}
                    name="blackBoxSwitch"
                    className="clear-margin-bottom"
                    valuePropName="checked"
                >
                    <Switch
                        disabled={inDetailPage && !editing}
                        checkedChildren={!isAbroadStyle&&i18n.t('state', '开')}
                        unCheckedChildren={!isAbroadStyle&&i18n.t('state', '关')}
                    />
                </Form.Item>
            </div>
        </Form>
    );

    const childList = getCustomItems(getDefaultEvidenceUploadRightBlock, [
        {
            renderForm: mediaFormSetting,
            ref: mediaForm,
            key: 'mediaForm',
        },
        {
            renderForm: pictureFormSetting,
            ref: pictureForm,
            key: 'pictureForm',
        },
        {
            renderForm: blackBoxFormSetting,
            ref: blackBoxForm,
            key: 'blackBoxForm',
        },
    ], {
        mediaForm,
        pictureForm,
        blackBoxForm,
        channelOptions,
        aiFrame
    });

    return (
        <StrategyConfig
            activeKey={activeKey}
            className="evidence"
            title={title}
            hasAlarmList
            configData={paramListTransfer.toRender(data.paramList || [])}
            inDetailPage={inDetailPage}
            authCode={authCode}
            childs={childList}
            getAlarmGroup={getAlarmGroup}
            onSubmit={onSubmit}
            openIsWhen={openIsWhen}
            checkEdit={true}
            modalType="evidence"
            appId={appId as number}
        />
    );
};
export default withSharePropsHOC(EvidenceStrategy);
