import { ParamItem } from '../types';

// 转换paramList参数
export const paramListTransfer = {
    toSubmit: (setting: any) => {
        // 组装为数组结构
        const newParamList: ParamItem[] = [];
        Object.keys(setting).forEach((item) => {
            let paramValue = setting[item];
            if (['captureMode', 'timingMode'].includes(item)) {
                return;
            }
            if (item === 'capturePictureChannels') {
                paramValue = paramValue.join();
            }
            if (item === 'capturePictureEnable') {
                paramValue = paramValue ? '1' : '0';
            }
            newParamList.push({
                paramName: item,
                paramValue,
            });
        });
        return newParamList;
    },
    toRender: (paramList: any) => {
        const setting: any = {};
        paramList.forEach((item: any) => {
            const { paramName } = item;
            let paramValue = item.paramValue;

            if (paramName === 'capturePictureChannels') {
                paramValue = paramValue.split(',')?.map(item=>Number(item));
            }
            if (paramName === 'capturePictureEnable') {
                paramValue = paramValue === '1';
            }
            setting[paramName] = paramValue;
        });
        setting['timingMode'] = true;
        setting['captureMode'] = {
            timingMode: true,
        };
        return setting;
    },
};
