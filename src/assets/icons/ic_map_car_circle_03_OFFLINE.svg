<?xml version="1.0" encoding="UTF-8"?>
<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <filter x="-29.2%" y="-30.0%" width="158.3%" height="160.0%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="备份" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="100_车队管理-车辆监控-默认" transform="translate(-782.000000, -557.000000)">
            <g id="icon_map_car_current_03" transform="translate(790.000000, 565.486486)">
                <g id="ic_car_bg_g备份-5" filter="url(#filter-1)" fill="#888888" stroke="#FFFFFF">
                    <ellipse id="Oval备份" cx="18" cy="17.5135135" rx="17.5" ry="17.0135135"></ellipse>
                </g>
                <g id="icon_map_car_current备份-2" transform="translate(6.000000, 5.513514)">
                    <rect id="矩形" x="0" y="0" width="24" height="24"></rect>
                    <path d="M23.3454545,7.88888889 L21.0545455,7.88888889 C20.9454545,7.88888889 20.8363636,7.88888889 20.7272727,8 L19.7454545,4.77777778 C19.5272727,2.88888889 18.3272727,2 16.8,2 L7.2,2 C5.56363636,2 4.47272727,3.22222222 4.25454545,4.77777778 L3.16363636,8 C3.05454545,7.88888889 2.94545455,7.88888889 2.83636364,7.88888889 L0.654545455,7.88888889 C0.327272727,7.88888889 0,8.22222222 0,8.55555556 L0,9.11111111 C0,9.44444444 0.327272727,9.77777778 0.654545455,9.77777778 L1.52727273,9.88888889 C1.2,10.4444444 1.09090909,11.1111111 1.09090909,11.8888889 L0.763636364,15.6666667 L0.763636364,20.7777778 C0.763636364,21.4444444 1.30909091,22 1.85454545,22 L3.6,22 C4.25454545,22 4.69090909,21.4444444 4.69090909,20.7777778 L4.69090909,19.3333333 L19.3090909,19.3333333 L19.3090909,20.7777778 C19.3090909,21.4444444 19.8545455,22 20.4,22 L22.1454545,22 C22.8,22 23.2363636,21.4444444 23.2363636,20.7777778 L23.2363636,16.2222222 L23.2363636,16 L23.2363636,15.6666667 L22.9090909,11.8888889 C22.9090909,11.1111111 22.8,10.5555556 22.4727273,10 L23.3454545,9.77777778 C23.6727273,9.77777778 24,9.44444444 24,9.11111111 L24,8.55555556 C24,8.22222222 23.6727273,7.88888889 23.3454545,7.88888889 Z M4.90909091,7.22222222 L5.67272727,5.11111111 C5.78181818,4.66666667 6,4.55555556 6.21818182,4.22222222 L17.8909091,4.22222222 C18.1090909,4.55555556 18.3272727,4.66666667 18.3272727,5 L19.0909091,7.11111111 L19.4181818,8.33333333 C19.3090909,9.22222222 18.4363636,9.88888889 17.5636364,9.88888889 L6.54545455,9.88888889 C5.78181818,9.88888889 4.8,9.22222222 4.69090909,8.33333333 L4.90909091,7.22222222 L4.90909091,7.22222222 Z M5.45454545,17.2222222 C4.47272727,17.2222222 3.70909091,16.4444444 3.70909091,15.4444444 C3.70909091,14.4444444 4.47272727,13.6666667 5.45454545,13.6666667 C6.43636364,13.6666667 7.2,14.4444444 7.2,15.4444444 C7.2,16.3333333 6.43636364,17.2222222 5.45454545,17.2222222 Z M18.5454545,17.2222222 C17.5636364,17.2222222 16.8,16.4444444 16.8,15.4444444 C16.8,14.4444444 17.5636364,13.6666667 18.5454545,13.6666667 C19.5272727,13.6666667 20.2909091,14.4444444 20.2909091,15.4444444 C20.2909091,16.3333333 19.5272727,17.2222222 18.5454545,17.2222222 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                </g>
            </g>
        </g>
    </g>
</svg>