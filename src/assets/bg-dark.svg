<?xml version="1.0" encoding="UTF-8"?>
<svg width="170px" height="110px" viewBox="0 0 170 110" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>bg-dark</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="170" height="110"></rect>
        <rect id="path-3" x="0" y="0" width="170" height="21"></rect>
        <filter x="-4.1%" y="-23.8%" width="108.2%" height="166.7%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.25 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="中台/通用设置" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="200_业务中台-主题设置-主题样式" transform="translate(-636.000000, -252.000000)">
            <g id="bg-dark" transform="translate(636.000000, 252.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <use id="蒙版" fill="#D8D8D8" xlink:href="#path-1"></use>
                <g id="编组" mask="url(#mask-2)" fill="#213249">
                    <rect id="矩形" x="0" y="0" width="170" height="110"></rect>
                    <rect id="矩形" x="0" y="21" width="34" height="89"></rect>
                </g>
                <rect id="矩形" fill="#656F7A" opacity="0.5" mask="url(#mask-2)" x="33" y="21" width="1" height="89"></rect>
                <rect id="矩形" fill="#6C7684" mask="url(#mask-2)" x="40" y="27" width="124" height="77" rx="4"></rect>
                <g id="矩形" mask="url(#mask-2)">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="#213249" fill-rule="evenodd" xlink:href="#path-3"></use>
                </g>
                <rect id="矩形" fill="#545E72" opacity="0.5" mask="url(#mask-2)" x="0" y="20" width="170" height="1"></rect>
            </g>
        </g>
    </g>
</svg>