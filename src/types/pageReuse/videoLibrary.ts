/*
 * @LastEditTime: 2024-09-13 17:48:28
 */
import type { JSXBase } from './pageReuseBase';
/** 视频上传设置 */
export type CreateVideo = {
    /** 重写文件类型选项值 */
    getVideoFileTypes?: (fileTypeArray: Record<string, any>[], otherData: any) => string[];
    // 复写视频上传的展示块
    getVideoDrawerBlock?: JSXBase;
    // 复写视频上传的提交按钮
    getVideoDrawerSubmitButton?: JSXBase;
    // 复写提交数据
    injectFormSubmit?: (data: any) => Promise<any>;
};
export type VideoLibraryList = {
    // 获取默认布局
    getDefaultLayoutType?: (layoutType: 'list' | 'card') => 'list' | 'card'
}
