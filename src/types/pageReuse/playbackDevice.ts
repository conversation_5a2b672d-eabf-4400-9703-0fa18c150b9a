/*
 * @LastEditTime: 2025-01-22 15:06:19
 */
import { OnLoadedParam } from '@/components/StarryPlayer/types';
import type { JSXBase } from './pageReuseBase';
import type { Map } from 'leaflet';
type SpeedData = {
    gpsTime: number;
    speed: number; // 需要除以10，使⽤的地⽅⾃⼰处理
};
/**
 * @description: 设备回放地图
 * @return {*}
 */
export type MapOperateButtons = {
    /**地图右上角的操作按钮 */
    getMapOperateButtons?: JSXBase;
};

export type ChartItem = {
    title: string;
    icon: JSXBase;
    main: JSXBase;
};
export type VideoTipItems = {
    /** 播放器时间轴下的tips列表 */
    getVideoTip?: (tipItem: Record<string, any>[]) => Record<string, any>[];
    // 自定义chartNodes
    getCustomChartNodes?: JSXBase;
    // 自定义chartMainNodes
    getCustomChartMainNodes?: JSXBase;
    // 自定义chartMarkLineNodes
    getCustomChartMarkLineNodes?: JSXBase;
    // 当时间轴的时间发生变化时的回调
    onTimeLineChange?: (timeObj: {
        computedStartTime: number;
        computedEndTime: number;
    }) => void;
    // 当容器宽度发生变化时的回调
    onScrollContainerWidthChange?: (width: number) => void;
    // chartTab切换的回调
    onChartTabChange?: (key: 'channel' | 'speed') => void;
};
export type ControlButtons = {
    /** 播放器右下角的操作按钮 */
    getVideoControl?: (
        controlItem: Record<string, any>[],
    ) => Record<string, any>[];
    //获取播放器右侧工具
    getPlayerRightTools?: JSXBase;
    // 通道工具
    getChannelTools?: JSXBase;
    // 控制栏左侧
    renderLeftControl?: (
        controlItem: Record<string, any>[],
    ) => Record<string, any>[];
    // 控制栏中间
    renderCenterControl?: (
        controlItem: Record<string, any>[],
    ) => Record<string, any>[];
};
export type InjectData = {
    /***播放器速度图标源数据**/
    injectSpeedChartData?: (queryParams: any) => Promise<SpeedData[]>;
    /**播放的channel数据和图片数据**/
    getPlayerInitChannelData?: (channelData: {
        channelList: [];
        imageData: Record<string, any>;
        deviceId: string;
    }) => Promise<{
        channelList: [];
        imageData: Record<string, any>;
    }>;
};
export type GetPlayModeSelect = {
    /**播放器播放模式定制**/
    getPlayMode?: (
        PlayModeItem: Record<string, any>[],
    ) => Record<string, any>[];
    onPlayModeChange?: (PlayModeItem: Record<string, any>[]) => void;
};
type PlayerAttr = {
    seekTime: (time: number) => void;
};
export type PlayerHandle = {
    // 播放器加载完成，可以seek了
    onPlayerInit?: (PlayerAttr: PlayerAttr) => void;
};
export type GetImagePreview = {
    /**快照定制**/
    getImagePreview?: JSXBase;
};
export type GpsInfoBlock = {
    /**右侧地图上gps信息**/
    getGpsInfoBlock?: JSXBase;
    /**右侧整个地图**/
    getMapBlock?: JSXBase;
};

export type PlayBackQueryParams = {
    vehicleInfo: any;
    deviceInfo: any;
    startTime: number;
    endTime: number;
};
export type PlayBackOnEvent = {
    // todo。2.15.3版本废弃，更改为新增queryParams变更的回调
    // 所选车辆变化时
    onSelectVehicleChange?: (vehicleInfo: any) => null;
    // 筛选条件变化时
    onQueryParamsChange?: (queryParams: PlayBackQueryParams) => null;
    // 当前播放器实例变化时
    onBaseReusePlayerInstanceUpdate?: (player: any[]) => void;
};
export type LeftTopNodeExtend = {
    getTreeSelectBlock?: JSXBase;
    // 自定义车组基础数据
    getCustomFleets?: (data: any[]) => any[];
    // 自定义车辆基础数据
    getCustomVehicles?: (data: any[]) => any[];
    // 自定义未分组标题
    getCustomUnGroupedTitle?: (title: JSX.Element) => JSX.Element;
    getExpandRightBlock?: JSXBase;
    // 播放模式下拉框
    getPlayModeSelectBlock?: JSXBase;
};
export type leftTopHeader = {
    getLeftTopHeader?: JSXBase;
};
export type PlayBackAlarmBlock = {
    getPlayBackAlarmBlock?: JSXBase;
};


export type VideoEditReuse = {
    // 视频编辑区块
    getVideoEditBlock?: JSXBase;
}

/**
 * @description: 回放地图相关的复用
 */
export type PlayBackMapReuse = {
    /** 获取地图实例 */
    onMapInstance?: (mapInstance: Map | null) => void;
}