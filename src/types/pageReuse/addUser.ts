import type { RoleItem } from '@/components/AuthorizeRoleCom';
import type { DataAuthorizeFormItem } from '@/hooks/useDataAuthorizeTabs';

/**
 * @description 复用类型
 * @description 允许上层定制新增用户流程
 */

/** @description: 角色授权项 */
export type RoleAuthorizeItem = {
    /** 点击添加角色按钮、表格无数据时新增按钮的函数回调 */
    clickAddRole?: (data?: RoleItem[]) => void;
    /** 获取角色授权列 */
    getRoleColumns?: (columns: any[]) => any[];
};

/** @description: 数据授权 */
export type DataAuthorizeItem = {
    /** 获取tab页数组 */
    getFormItems?: (formItems: DataAuthorizeFormItem[]) => DataAuthorizeFormItem[];
};
