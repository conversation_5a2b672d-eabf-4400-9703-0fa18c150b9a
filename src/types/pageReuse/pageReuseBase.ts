/*
 * @LastEditTime: 2025-05-12 13:19:58
 */
import type { ColumnType } from '@streamax/poppy/lib/table';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { MapLayerProps, UseMapLayerToolParams } from './mapCommonTypes';
import type { CheckboxProps } from '@streamax/poppy/lib/checkbox';
import type { Config as DOMPurifyConfig } from 'dompurify';
import type { IFilterXSSOptions } from 'xss';

export type TabConfig = {
    title?: any;
    key?: string | number;
    render?: React.ReactNode;
    tabName?: any;
    tabContent?: React.ReactNode;
};
export type ItemConfig = QueryFormProps['items'][0];
export type JSXBase =
    | ((
        jsxList: React.ReactNode[],
        dataSource?: any,
    ) => React.ReactNode[] | React.ReactNode)
    | undefined;
export declare type IconBtns =
    | ('reload' | 'column-setting' | React.ReactElement | null)[]
    | false;
/**
 * @description: 页面公共属性
 * @return {React.ReactNode}
 */
export type PageBase = {
    children?: React.ReactNode | React.ReactNode[];
    showBreadCrumb?: () => boolean;
};

/**
 * @description: 复用自定义列表公共属性
 * @return {*}
 */

export type ListPageTableBase = {
    // 设置列表columnSetting对象
    getColumnSetting?: () => {
        storageKey?: string;
        disabledKeys?: string[];
    };

    /****获取列表列配置***/
    getColumns?: (
        columns: ColumnType<any>[],
        dataSource: any,
    ) => ColumnType<any>[];
    /***列表勾选改变时触发事件***/
    onSelectRows?: (selectedRows: any[]) => void;
    /***列表左侧按钮渲染，入参所有已存在的按钮jsx，自定义时返回自定义的jsx***/
    getTableLeftRender?: JSXBase;
    getIconBtns?: (jsxList: IconBtns, dataSource?: any) => IconBtns;
    /**列表选框***/
    tableRowSelection?: () => boolean;
    /**列表查询接口**/
    injectSearchList?: (queryParams: any) => Promise<{
        list: any[];
        total: number;
        page: number;
        pageSize: number;
        hasNextPage?: boolean;
    }>;
    injectDataSourceList?: (queryParams: any) => any[];
    /*****修改查询条件***/
    injectSearchData?: (queryParams: any) => any;
    /***用户设置筛选条件的复用 */
    getUserSearchStore?: (storeItems: ItemConfig[]) => ItemConfig[];
    
};

// 试用于表格复选框属性控制和卡片列表复选框属性控制
export type ListItemCheckboxProps = {
    // 列表和卡片的checkbox支持行业自定义属性
    getListItemCheckboxProps?: (
        record: any,
    ) => Partial<
        Omit<CheckboxProps, 'checked' | 'defaultChecked' | 'onChange'>
    >;
};

/**
 * @description: 查询条件自定义
 * @return {*}
 */
export type ListPageQueryForm = {
    getQueryForm?: (items: ItemConfig[]) => ItemConfig[];
    /***查询条件回填时的数据处理***/
    getInitQueryData?: (data: Record<string, any>) => Record<string, any>; 
};
/**
 * @description: 左右布局页面
 * @return {*}
 */
export type LeftAndRightPageLayout = {
    getLeftPageLayout?: JSXBase;
    getRightPageLayout?: JSXBase;
};

/*********************************详情类********************************/

/**
 * @description: 定义详情页面一整坨卡片，主要用于详情页除了展示外，下面还有比较复杂的模块，行业层复用不必去维护视频等复杂模块
 * @return {any}
 */
export type DetailPageBlock = {
    getDetailPageBlock?: JSXBase;
};

/**
 * @description: 详情展示item
 * @return {DescriptionItem[]}
 */
export type DetailPageItems = {
    /**详情item项**/
    getDescriptionItems?: (
        descriptionItem: Record<string, any>[],
        detailData: any,
    ) => Record<string, any>[];
    // 按钮项
    getButtonsItems?: JSXBase;
    /***自定义地图useMapLayerTool的options配置** */
    getUseMapLayerOptions?: (
        params: UseMapLayerToolParams,
    ) => UseMapLayerToolParams;
};

/**
 * @description: 详情右侧buttons
 * @return {React.ReactNode}
 */
export type DetailPageRightButtons = {
    /**详情右侧buttons */
    getDetailRightButtons?: JSXBase;
    /**详情收起状态 */
    defaultCollapsed?: boolean;
};
/**
 * @description: 详情页面 报警证据和基本信息之间的自定义组件，需要自定义border-bottom
 * @return {React.ReactNode}
 */
export type PreAlarmEvidenceCom = {
    /**支持行业层在报警证据和报警信息之间自定义内容 */
    getPreAlarmEvidenceCom?: JSXBase;
};

/**
 * @description: 详情页面 证据视频区域是否自定义展示
 * @return {React.ReactNode}
 */
export type AlarmEvidenceVideoCom = {
    /**支持行业层在报警视频自定义内容 */
    getAlarmEvidenceVideoCom?: JSXBase;
};

/**
 * @description: 列表倍速下载按钮设置
 * @return {React.ReactNode}
 */
export type ListPlaySpeed = {
    /**支持行业层在列表倍速下载自定义内容 */
    getDownloadSetRender?: JSXBase;
};

/**
 * @description: 详情title
 * @return {React.ReactNode}
 */
export type DetailPageTitle = {
    /**详情左侧title */
    getDetailTitle?: JSXBase;
};

/**
 * @description: card卡片操作buttons
 * @return {React.ReactNode}
 */
export type PageCardButtons = {
    getCardButtons?: JSXBase;
    getCardShareMenu?: JSXBase;
};
/**
 * @description: 视频证据操作buttons
 * @return {React.ReactNode}
 */
export type EvidenceButtons = {
    getEvidenceButtons?: JSXBase;
};
/**
 * @description: 抛出方法实例
 * @return {*}
 */
export type Instances = {
    getInstances?: (instances: any) => void;
};

/**
 * @description: 复用导出的车辆对象
 * @return {*}
 */
export type VehicleParams = {
    vehicleId?: string;
    vehicleNumber?: string;
};
/**
 * @description: 复用导出的设备对象
 * @return {*}
 */
export type DeviceParams = {
    deviceId?: string;
    authId?: string;
    deviceName?: string;
};
/**
 * @description: 复用导出的通道对象
 * @return {*}
 */
export type ChannelParams = {
    channel?: number | string;
};

/**
 * @description: 复用接收的监听回调
 * @return {*}
 */
export type Listen = {
    onListen?: (params: VehicleParams & DeviceParams & ChannelParams) => void;
};

export type BeforeIntercomRes = {
    optId?: string;
};
/**
 * @description: 复用接收的监听回调
 * @return {*}
 */
export type Intercom = {
    onBeforeIntercom?: (
        params: VehicleParams & DeviceParams,
    ) => BeforeIntercomRes;
    onIntercom?: (params: VehicleParams & DeviceParams) => void;
    onAfterIntercom?: (params: VehicleParams & DeviceParams) => void;
    onIntercomError?: (params: VehicleParams & DeviceParams) => void;
};

/**
 * @description: 复用接收的实时视频回调
 * @return {*}
 */
export type RealTimeVideo = {
    onRealTimeVideo?: (
        params: VehicleParams & DeviceParams & ChannelParams,
    ) => void;
};

export interface SegmentData {
    key: string;
    segmentName: string;
    segmentContent: React.ReactElement;
    bottomDivider?: boolean;
    display?: boolean;
}

export interface StepData {
    key: string;
    stepName: string;
    segmentList: SegmentData[];
}

/**
 * @description: tab页面
 * @return {React.ReactNode}
 */
export type PageTabs = {
    /**页面中的tab */
    getPageTabs?: JSXBase;
    getPageTabContent?: JSXBase;
    getPageTabsConfigItems?: (
        oldItems: TabConfig[],
        otherData: any,
    ) => TabConfig[];
    // 新增自定义设置的tab复用
    getPageAddTabConfigItems?: (
        oldItems: StepData[],
        otherData: any,
    ) => StepData[];
};
/**
 * @description: 详情页地图模块
 * @return {*}
 */
export type DetailPageMap = {
    getDetailPageMap?: JSXBase;
};

export type FormEdit = {
    onFormSubmit?: (data: any) => void;
    /**自定义表单接口提交**/
    injectFormSubmit?: (params: any) => Promise<any>;
    /*****修改表单提交前数据****/
    changeSubmitData?: (params: any) => any;
};
/***************************************************暂不开放定制*****************************************/
/**
 * @description: 查询详情接口, 存储返回的结果，供详情使用
 * @return {any}
 */
export type DetailPageRequest = {
    getDetailInfo?: (id: string) => Promise<any>;
};

export type MapToolCustom = {
    /***自定义地图useMapLayerTool的options配置** */
    getUseMapLayerOptions?: (
        params: UseMapLayerToolParams,
    ) => UseMapLayerToolParams;
    // 自定义地图MapLayerTool组件的props配置
    getMapLayerToolProps?: () => MapLayerProps;
};

/** xss过滤自定义*/
export type XSSFilter = {
    getXssFilter?: (params: {
        type: 'messageTitle' | 'messageContent';
        data: string;
        config?: DOMPurifyConfig | IFilterXSSOptions;
    }) => {
        config?: DOMPurifyConfig | IFilterXSSOptions;
        data: string;
    };
};

export type DataClearCustom = {
    getCardDetail?: JSXBase;
    getClearBtn?: JSXBase;
    injectSearchList?: (params: any) => Promise<{
        list: any[];
        total: number;
        page: number;
        pageSize: number;
        hasNextPage?: boolean;
        dataSize: {
            totalSize: number;
            total: number;
        }
    }>;
};

export type SpaceVehicleDetailCustom = {
    getCardDetail?: JSXBase;
    injectSearchList?: (params: any) => Promise<{
        list: any[];
        total: number;
        page: number;
        pageSize: number;
        hasNextPage?: boolean;
        dataSize: {
            totalSize: number;
            total: number;
        }
    }>;
}
