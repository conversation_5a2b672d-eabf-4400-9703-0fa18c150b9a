/*
 * @LastEditTime: 2024-10-31 10:45:33
 */
/**
 * 关联配置keys
 */
export type RelationConfigKeys =
    | 'common.config.map'
    | 'common.config.channel'
    | 'language.type.list'
    | 'tenant.config.login'
    | 'vehicle.parking.config'
    | 'face.compare'
    | 'driver.vehicle.max'
    | 'vehicle.driver.max'
    | 'driver.binding.dynamic'
    | 'driver.face.compare.device'
    | 'driver.face.compare.plateform'
    | 'driver.device.offline'
    | 'message.center.jump.page.config'
    | 'tenant.user.info.config'
    | 'tenant.storage.config'
    | 'alarm.classification.config'
    | 'tenant.user.fleet.config'
    | 'driver.dynamic.compare.ibutton'
    | 'driver.dynamic.compare.swipe.card'
    | 'driver.dynamic.binding.scope'
    | 'tenant.vehicle.park.setting'
    | 'driver.dynamic.compare.face.ibutton'
    | 'system.send.email.config'
    | 'tenant.expire.notice.config'
    | 'travel.speed.limit'
    | 'travel.duration.time'
    | 'help.center.config.operatingManual'
    | 'help.center.config.productDocument'
    | 'help.center.config.releaseNote'
    | 'help.center.config.apiDocument'
    | 'help.center.config.interfaceOverview'
    | 'flow.limit.config';