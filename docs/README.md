# 消息和通知模板模块文档

## 文档概览

本文档集合提供了消息和通知模板模块的完整使用指南，帮助开发者快速理解和使用这个强大的消息系统。

## 文档结构

### 📚 [完整使用指南](./MessageTemplate-Guide.md)
**适合人群**: 需要深入了解系统架构和高级功能的开发者

**内容包括**:
- 系统架构详解
- 核心概念和工作原理
- 完整的API参考
- 高级功能和最佳实践
- 性能优化指南
- 测试和调试方法
- 故障排查和版本升级

### 🚀 [快速入门指南](./MessageTemplate-QuickStart.md)
**适合人群**: 新手开发者，需要快速上手的场景

**内容包括**:
- 5分钟快速上手
- 基础代码模板
- 简单示例和配置
- 常见问题解答
- 调试技巧

## 系统特性

### 🔧 插件化架构
- 基于注册机制的模板系统
- 支持中台默认模板和行业层自定义模板
- 通过 `type-appId` 组合实现模板隔离

### 🎨 灵活的UI定制
- 支持自定义消息列表项组件 (MessageItem)
- 支持自定义通知弹窗组件 (NotificationTemplate)
- 丰富的工具函数和公共组件

### 🌍 国际化支持
- 内置国际化机制
- 支持多语言消息模板
- 动态语言切换

### ⚡ 高性能
- 延迟加载机制
- 消息缓存支持
- 批量处理能力

## 快速开始

### 1. 安装依赖
```bash
npm install @base-app/runtime-lib @streamax/poppy @streamax/poppy-icons
```

### 2. 创建模板组件
```typescript
// MessageTemplate/MessageItem.tsx
import { MessageDefaultMessageItem, MessageUtils } from '@base-app/runtime-lib';

export default (props) => {
    // 实现你的消息列表项组件
    return <MessageDefaultMessageItem {...props} />;
};
```

### 3. 注册模板
```typescript
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';
import { MessageItem, NotificationTemplate } from './MessageTemplate';

registerTemplate(NotificationTypeMap.alarm, 1, () => ({
    MessageItem,
    NotificationTemplate
}));
```

## 支持的消息类型

| 类型 | 值 | 描述 |
|------|----|----- |
| alarm | 1 | 报警消息 |
| manualEvidence | 2 | 手动下载证据 |
| videoDownload | 3 | 视频剪辑 |
| fileExport | 4 | 文件导出 |
| tenantApply | 5 | 申请访问租户 |
| tenantStore | 6 | 租户空间预警 |
| vehicleStore | 7 | 车辆空间预警 |
| tenantValidateTimeNotify | 8 | 租户有效期提醒 |
| promptCarousel | 9 | 轮播消息 |
| licenseExpire | 10 | License到期通知 |
| tenantActiveResult | 12 | 租户激活结果 |

## 核心API

### registerTemplate(type, appId, callback)
注册自定义消息模板

```typescript
registerTemplate(
    NotificationTypeMap.alarm,  // 消息类型
    1,                         // 应用ID
    () => ({                   // 模板组件回调
        MessageItem,
        NotificationTemplate
    })
);
```

### MessageUtils 工具函数
```typescript
import { MessageUtils } from '@base-app/runtime-lib';

// 格式化消息内容
MessageUtils.getFormatMessage(title, labels, data);
MessageUtils.getFormatDescription(content, labels, data);

// 页面跳转
MessageUtils.jumpPage(options, history);
```

## 开发工作流

```mermaid
graph TD
    A[创建模板组件] --> B[定义类型和常量]
    B --> C[实现工具函数]
    C --> D[注册模板]
    D --> E[测试验证]
    E --> F[部署上线]
    
    F --> G{需要修改?}
    G -->|是| A
    G -->|否| H[维护监控]
```

## 最佳实践

### ✅ 推荐做法
- 使用 TypeScript 确保类型安全
- 复用系统提供的公共组件和工具函数
- 实现适当的错误处理和边界情况
- 添加单元测试和集成测试
- 遵循统一的代码风格和命名规范

### ❌ 避免做法
- 直接修改系统默认模板
- 在模板组件中执行重量级操作
- 忽略国际化支持
- 硬编码业务逻辑
- 缺少错误处理

## 示例项目

查看 `src/MessageTemplate/` 目录下的示例实现，包含：
- 完整的报警消息模板
- 类型定义和工具函数
- 测试用例

## 技术支持

### 📖 文档资源
- [完整使用指南](./MessageTemplate-Guide.md) - 详细的技术文档
- [快速入门指南](./MessageTemplate-QuickStart.md) - 快速上手教程

### 🐛 问题反馈
如果遇到问题或有改进建议，请：
1. 查看常见问题解答
2. 检查是否有相关的 Issue
3. 提交新的 Issue 或 PR

### 💬 社区交流
- 开发者社区讨论
- 技术分享和最佳实践
- 版本更新和新功能预告

## 版本历史

- **v2.0** - 新增 openPersonalCenter 支持，优化性能
- **v1.5** - 增强国际化支持，新增批量处理功能
- **v1.0** - 初始版本，基础模板注册机制

## 贡献指南

欢迎贡献代码和文档！请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 添加测试用例
5. 更新相关文档
6. 提交 Pull Request

---

**开始使用**: 如果你是新手，建议从 [快速入门指南](./MessageTemplate-QuickStart.md) 开始；如果需要深入了解，请查看 [完整使用指南](./MessageTemplate-Guide.md)。

*最后更新: 2025-01-29*
