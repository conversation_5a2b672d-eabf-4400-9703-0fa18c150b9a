# 消息模板扩展示例

本文档提供了两种主要扩展场景的完整代码示例。

## 场景一：中台新增内置模板类型

### 背景
中台需要新增一个"系统维护通知"的内置消息类型，供所有应用使用。

### 实现步骤

#### 1. 更新消息类型映射

**文件**: `src/runtime-lib/MessageModule/MessageTemplate/index.ts`

```typescript
// 通知类型
export const NotificationTypeMap = {
    alarm: 1,
    manualEvidence: 2,
    videoDownload: 3,
    fileExport: 4,
    tenantApply: 5,
    tenantStore: 6,
    vehicleStore: 7,
    tenantValidateTimeNotify: 8,
    promptCarousel: 9,
    licenseExpire: 10,
    tenantActiveResult: 12,
    systemMaintenance: 13,  // 新增：系统维护通知
};
```

#### 2. 创建默认模板组件

**目录结构**:
```
src/runtime-lib/MessageModule/MessageTemplate/MessageItems/
└── SystemMaintenance/
    ├── index.ts
    ├── MessageItem.tsx
    ├── NotificationTemplate.tsx
    └── utils.ts
```

**文件**: `MessageItems/SystemMaintenance/utils.ts`

```typescript
import i18n from '../../../../i18n';
import { MessageInfo, ModalData } from '../../type';
import { zeroTimeStampToFormatTime } from '../../../../utils/formator';

export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId } = messageInfo;
    return i18n.t(titleId, '系统维护通知');
};

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId } = messageInfo;
    return i18n.t(contentId, '');
};

export const getBtn = (modelData: ModalData) => {
    return {
        text: i18n.t('message', '了解详情'),
        onclick: () => {
            // 跳转到系统维护详情页
            window.open('/system/maintenance/detail', '_blank');
        },
    };
};

// 格式化维护时间
export const formatMaintenanceTime = (startTime: number, endTime: number) => {
    const start = zeroTimeStampToFormatTime(startTime, undefined, 'MM/DD HH:mm');
    const end = zeroTimeStampToFormatTime(endTime, undefined, 'MM/DD HH:mm');
    return `${start} - ${end}`;
};
```

**文件**: `MessageItems/SystemMaintenance/MessageItem.tsx`

```typescript
import React from 'react';
import { MessageDefaultMessageItem, MessageBtnDetail } from '../../../..';
import { MessageItemProps } from '../../type';
import { getMessage, getMessageContent, getBtn, formatMaintenanceTime } from './utils';

export default (props: MessageItemProps) => {
    const { messageInfo, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    
    // 自定义内容格式化
    const formattedContent = content.replace(
        /\{\{maintenanceTime\}\}/g,
        formatMaintenanceTime(modelData.startTime, modelData.endTime)
    );
    
    const btnDetail = showDetailBtn ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <div style={{ borderLeft: '4px solid #faad14' }}>
            <MessageDefaultMessageItem
                messageInfo={messageInfo}
                message={message}
                description={formattedContent}
                btnDetail={btnDetail}
                openPersonalCenter={openPersonalCenter}
            />
        </div>
    );
};
```

**文件**: `MessageItems/SystemMaintenance/NotificationTemplate.tsx`

```typescript
import React from 'react';
import { MessageDefaultNotificationRender, MessageBtnDetail } from '../../../..';
import { NotificationTemplateProps } from '../../type';
import { getMessage, getMessageContent, getBtn, formatMaintenanceTime } from './utils';

export default (props: NotificationTemplateProps) => {
    const { messageInfo, handleClickNotice, prefixCls, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    
    // 自定义内容格式化
    const formattedContent = content.replace(
        /\{\{maintenanceTime\}\}/g,
        formatMaintenanceTime(modelData.startTime, modelData.endTime)
    );
    
    const btnDetail = showDetailBtn ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={message}
            description={formattedContent}
            btnDetail={btnDetail}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

**文件**: `MessageItems/SystemMaintenance/index.ts`

```typescript
export { default as MessageItem } from './MessageItem';
export { default as NotificationTemplate } from './NotificationTemplate';
export * from './utils';
```

#### 3. 更新导出和映射

**文件**: `MessageItems/index.ts`

```typescript
export * as AlarmMessage from './AlarmMessage';
export * as PromptCarousel from './PromptCarousel';
export * as VideoDownload from './VideoDownload';
// ... 其他导出
export * as SystemMaintenance from './SystemMaintenance';  // 新增
```

**文件**: `MessageTemplate/index.ts`

```typescript
import {
    AlarmMessage,
    PromptCarousel,
    VideoDownload,
    // ... 其他导入
    SystemMaintenance,  // 新增导入
} from './MessageItems';

// 中台默认消息模板
const defaultTemplate: DefaultTemplate = {
    [NotificationTypeMap.alarm]: AlarmMessage,
    [NotificationTypeMap.manualEvidence]: ManualEvidence,
    // ... 其他映射
    [NotificationTypeMap.systemMaintenance]: SystemMaintenance,  // 新增映射
};
```

#### 4. 添加类型定义

**文件**: `MessageTemplate/type.ts`

```typescript
// 系统维护通知数据结构
export interface ISystemMaintenanceLabel {
    maintenanceId: string;
    maintenanceType: string;
    startTime: number;
    endTime: number;
    affectedServices: string[];
    impact: 'low' | 'medium' | 'high';
    [key: string]: any;
}

// 更新联合类型
export type LabelKeyType =
    | keyof IManualEvidenceLabel
    | keyof IVideoDownloadLabel
    | keyof IFileExportLabel
    | keyof IAlarmLabel
    | keyof ITenantValidateTimeNotifyLabel
    | keyof ISystemMaintenanceLabel;  // 新增
```

## 场景二：行业层创建自定义模板类型

### 背景
物流行业需要创建一个"货物异常通知"的自定义消息类型。

### 实现步骤

#### 1. 定义自定义消息类型

**文件**: `src/constants/messageTypes.ts`

```typescript
// 自定义消息类型（使用1000+避免冲突）
export const LogisticsNotificationTypeMap = {
    cargoException: 1001,
    deliveryDelay: 1002,
    routeChange: 1003,
    temperatureAlert: 1004,
};
```

#### 2. 创建自定义模板组件

**目录结构**:
```
src/CustomMessageTemplate/
└── CargoException/
    ├── index.ts
    ├── MessageItem.tsx
    ├── NotificationTemplate.tsx
    ├── type.ts
    ├── const.ts
    └── utils.ts
```

**文件**: `CargoException/type.ts`

```typescript
import { 
    MessageInfo as BaseMessageInfo, 
    MessageItemProps as BaseMessageItemProps, 
    NotificationTemplateProps as BaseNotificationTemplateProps 
} from '@base-app/runtime-lib';

// 货物异常数据结构
export interface CargoExceptionData {
    appId: number;
    cargoId: string;
    cargoName: string;
    exceptionType: 'damage' | 'loss' | 'delay' | 'temperature';
    severity: 'low' | 'medium' | 'high' | 'critical';
    occurTime: number;
    location: string;
    driverName: string;
    vehicleNo: string;
    estimatedLoss: number;
    [key: string]: any;
}

export interface MessageInfo extends BaseMessageInfo {
    modelData: CargoExceptionData;
}

export interface MessageItemProps extends BaseMessageItemProps {
    messageInfo: MessageInfo;
}

export interface NotificationTemplateProps extends BaseNotificationTemplateProps {
    messageInfo: MessageInfo;
}
```

**文件**: `CargoException/const.ts`

```typescript
import { utils } from '@base-app/runtime-lib';

const { zeroTimeStampToFormatTime } = utils.formator;

// 货物异常模板标签
export const labels = [
    {
        name: 'cargoName',
        value: '{{cargoName}}',
    },
    {
        name: 'exceptionType',
        value: '{{exceptionType}}',
        format: (type: string) => {
            const typeMap = {
                damage: '货物损坏',
                loss: '货物丢失',
                delay: '运输延误',
                temperature: '温度异常'
            };
            return typeMap[type] || type;
        },
    },
    {
        name: 'severity',
        value: '{{severity}}',
        format: (level: string) => {
            const levelMap = {
                low: '轻微',
                medium: '中等',
                high: '严重',
                critical: '紧急'
            };
            return levelMap[level] || level;
        },
    },
    {
        name: 'occurTime',
        value: '{{occurTime}}',
        format: (time: number) => {
            return zeroTimeStampToFormatTime(time) || '-';
        },
    },
    {
        name: 'location',
        value: '{{location}}',
    },
    {
        name: 'driverName',
        value: '{{driverName}}',
    },
    {
        name: 'vehicleNo',
        value: '{{vehicleNo}}',
    },
    {
        name: 'estimatedLoss',
        value: '{{estimatedLoss}}',
        format: (amount: number) => {
            return amount ? `¥${amount.toLocaleString()}` : '-';
        },
    },
];

export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};

// 异常严重程度颜色
export const SEVERITY_COLORS = {
    low: '#52c41a',
    medium: '#faad14',
    high: '#ff7a45',
    critical: '#ff4d4f',
};

// 异常类型图标
export const EXCEPTION_ICONS = {
    damage: '🔧',
    loss: '📦',
    delay: '⏰',
    temperature: '🌡️',
};
```
