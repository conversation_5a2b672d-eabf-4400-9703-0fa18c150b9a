# 消息模板快速入门指南

## 5分钟快速上手

### 1. 创建模板文件

在你的项目中创建以下文件结构：

```
src/MessageTemplate/
├── index.ts
├── MessageItem.tsx
├── NotificationTemplate.tsx
├── type.ts
├── const.ts
└── utils.ts
```

### 2. 复制基础代码

#### type.ts
```typescript
export interface MessageInfo {
    notificationType: number;
    modelData: {
        appId: number;
        [key: string]: any;
    };
    titleId?: string;
    title?: string;
    contentId?: string;
    content?: string;
    showDetailBtn?: number;
    [key: string]: any;
}

export interface MessageItemProps {
    messageInfo: MessageInfo;
    openPersonalCenter: any;
}

export interface NotificationTemplateProps {
    messageInfo: MessageInfo;
    handleClickNotice?: (messageInfo: MessageInfo) => void;
    prefixCls?: string;
    history?: any;
    openPersonalCenter?: any;
}
```

#### const.ts
```typescript
// 详情按钮显示控制
export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};

// 消息模板标签（根据你的业务需求修改）
export const labels = [
    {
        name: 'vehicleNo',
        value: '{{vehicleNo}}',
    },
    {
        name: 'alarmTypeName',
        value: '{{alarmTypeName}}',
    },
    // 添加更多标签...
];
```

#### utils.ts
```typescript
import { i18n } from '@base-app/runtime-lib';
import { MessageInfo } from "./type";

export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId, title } = messageInfo;
    return titleId ? i18n.t(titleId, title) : title || '消息通知';
};

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId, content } = messageInfo;
    return contentId ? i18n.t(contentId, content) : content || '';
};

export const getBtn = (modelData: any) => {
    return {
        text: '查看详情',
        onclick: () => {
            // 处理点击事件
            console.log('查看详情', modelData);
        },
    };
};
```

#### MessageItem.tsx
```typescript
import { MessageDefaultMessageItem, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { MessageItemProps } from "./type";
import { getMessage, getMessageContent, getBtn } from './utils';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: MessageItemProps) => {   
    const { messageInfo, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    // 获取并格式化消息内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);
    
    // 生成详情按钮
    const btnDetail = showDetailBtn === DETAIL_BTN.SHOW ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultMessageItem
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={btnDetail}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

#### NotificationTemplate.tsx
```typescript
import { MessageDefaultNotificationRender, MessageUtils, MessageBtnDetail } from '@base-app/runtime-lib';
import { NotificationTemplateProps } from "./type";
import { getMessage, getMessageContent, getBtn } from './utils';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: NotificationTemplateProps) => {
    const {
        messageInfo, 
        handleClickNotice,
        prefixCls,
        openPersonalCenter
    } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    // 获取并格式化消息内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);
    
    // 生成详情按钮
    const btnDetail = showDetailBtn === DETAIL_BTN.SHOW ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={btnDetail}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

#### index.ts
```typescript
export { default as MessageItem } from './MessageItem';
export { default as NotificationTemplate } from './NotificationTemplate';
export * from './type';
export * from './const';
export * from './utils';
```

### 3. 注册模板

在你的应用入口文件中（如 `src/runtime-pages/index.message.demo.ts`）添加：

```typescript
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';
import { NotificationTemplate, MessageItem } from '../MessageTemplate';

// 注册报警消息模板
registerTemplate(NotificationTypeMap.alarm, 1, () => {
    return {
        NotificationTemplate,
        MessageItem
    }
});

// 注册其他类型的消息模板
// registerTemplate(NotificationTypeMap.videoDownload, 1, () => {
//     return {
//         NotificationTemplate: VideoNotificationTemplate,
//         MessageItem: VideoMessageItem
//     }
// });
```

### 4. 测试验证

启动你的应用，当收到对应类型的消息时，应该能看到你的自定义模板生效。

## 常用消息类型

```typescript
export const NotificationTypeMap = {
    alarm: 1,                        // 报警
    manualEvidence: 2,               // 手动下载证据
    videoDownload: 3,                // 视频剪辑
    fileExport: 4,                   // 文件导出
    tenantApply: 5,                  // 申请访问租户
    tenantStore: 6,                  // 租户空间预警
    vehicleStore: 7,                 // 车辆空间预警
    tenantValidateTimeNotify: 8,     // 租户有效期提醒
    promptCarousel: 9,               // 轮播消息
    licenseExpire: 10,               // License到期通知
    tenantActiveResult: 12,          // 租户激活结果
};
```

## 自定义样式

如果需要自定义样式，可以在组件中添加自定义的 CSS 类：

```typescript
// 在 MessageItem.tsx 中
return (
    <div className="custom-message-item">
        <MessageDefaultMessageItem
            // ... 其他属性
            className="my-custom-style"
        />
    </div>
);
```

然后在对应的 CSS 文件中定义样式：

```css
.custom-message-item {
    border-left: 4px solid #1890ff;
    background-color: #f6ffed;
}

.my-custom-style {
    font-weight: bold;
}
```

## 调试技巧

1. **开启调试日志**：
```typescript
// 在浏览器控制台执行
localStorage.setItem('DEBUG_MESSAGE_TEMPLATE', 'true');
```

2. **查看注册的模板**：
```typescript
// 在 mountCustomTemplate 函数中取消注释
console.log('==========【customTemplate】========', keys);
```

3. **检查消息数据**：
```typescript
// 在组件中添加
console.log('MessageInfo:', messageInfo);
console.log('ModelData:', modelData);
```

## 下一步

- 查看 [完整文档](./MessageTemplate-Guide.md) 了解更多高级功能
- 学习如何处理复杂的业务逻辑
- 了解性能优化和最佳实践

## 常见问题

**Q: 模板没有生效怎么办？**
A: 检查注册时机，确保在系统初始化前完成注册，并且消息类型和应用ID匹配。

**Q: 如何处理国际化？**
A: 使用 `titleId` 和 `contentId` 而不是直接的 `title` 和 `content`，系统会自动进行国际化处理。

**Q: 如何添加自定义按钮？**
A: 在 `utils.ts` 的 `getBtn` 函数中定义按钮的文本和点击事件处理逻辑。

**Q: 消息格式化不正确怎么办？**
A: 检查 `const.ts` 中的 `labels` 配置，确保标签名称和格式化函数正确。
