# 消息和通知模板模块使用指南

## 概述

消息和通知模板模块是一个基于插件化架构的消息系统，支持中台默认模板和行业层自定义模板。通过注册机制，不同应用可以为特定消息类型提供自定义的UI渲染组件，实现消息展示的个性化定制。

## 核心概念

### 1. 模板类型

每个消息模板包含两个核心组件：

- **NotificationTemplate**: 通知弹窗组件，用于实时消息通知
- **MessageItem**: 消息列表项组件，用于消息中心的消息展示

### 2. 消息类型映射

系统预定义了多种消息类型：

```typescript
export const NotificationTypeMap = {
    alarm: 1,                        // 报警
    manualEvidence: 2,               // 手动下载证据
    videoDownload: 3,                // 视频剪辑
    fileExport: 4,                   // 文件导出
    tenantApply: 5,                  // 申请访问租户
    tenantStore: 6,                  // 租户空间预警
    vehicleStore: 7,                 // 车辆空间预警
    tenantValidateTimeNotify: 8,     // 租户有效期提醒
    promptCarousel: 9,               // 轮播消息
    licenseExpire: 10,               // License到期通知
    tenantActiveResult: 12,          // 租户激活结果
};
```

### 3. 模板注册机制

- **注册阶段**: 行业层调用 `registerTemplate` 注册自定义模板
- **挂载阶段**: 系统启动时调用 `mountCustomTemplate` 挂载所有注册的模板
- **调度阶段**: 根据消息类型和应用ID动态选择合适的模板进行渲染

## 快速开始

### 1. 基本使用

```typescript
// 1. 导入必要模块
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';
import { NotificationTemplate, MessageItem } from '../MessageTemplate';

// 2. 注册自定义模板
registerTemplate(NotificationTypeMap.alarm, 1, () => {
    return {
        NotificationTemplate,  // 通知弹窗组件
        MessageItem           // 消息列表项组件
    }
});
```

### 2. 创建自定义模板组件

#### MessageItem 组件示例

```typescript
// MessageTemplate/MessageItem.tsx
import { MessageDefaultMessageItem, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { MessageItemProps } from "./type";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: MessageItemProps) => {   
    const { messageInfo } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    // 获取消息标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    
    // 格式化消息内容
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);
    
    // 自定义详情按钮
    const getMessageItemBtnDetail = (messageInfo: MessageInfo) => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    };

    return (
        <MessageDefaultMessageItem
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={showDetailBtn ? getMessageItemBtnDetail(messageInfo) : null}
        />
    );
};
```

#### NotificationTemplate 组件示例

```typescript
// MessageTemplate/NotificationTemplate.tsx
import { MessageDefaultNotificationRender, MessageUtils } from '@base-app/runtime-lib';
import { NotificationTemplateProps } from "./type";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: NotificationTemplateProps) => {
    const { messageInfo, handleClickNotice, prefixCls } = props;
    const { modelData, showDetailBtn } = messageInfo;
    
    // 获取消息标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    
    // 格式化消息内容
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
        />
    );
};
```

### 3. 定义类型和常量

```typescript
// MessageTemplate/type.ts
export interface MessageInfo {
    notificationType: number;
    modelData: ModalData;
    titleId?: string;
    title?: string;
    contentId?: string;
    content?: string;
    showDetailBtn?: number;
    [key: string]: any;
}

export interface MessageItemProps {
    messageInfo: MessageInfo;
    openPersonalCenter: OpenPersonalCenter;
}

export interface NotificationTemplateProps {
    messageInfo: MessageInfo;
    handleClickNotice?: (messageInfo: MessageInfo) => void;
    prefixCls?: string;
    history?: any;
}
```

```typescript
// MessageTemplate/const.ts
import { MessageUtils } from '@base-app/runtime-lib';

// 模板标签定义（用于消息内容格式化）
export const labels = [
    {
        name: 'alarmTypeName',
        value: '{{alarmTypeName}}',
    },
    {
        name: 'vehicleNo', 
        value: '{{vehicleNo}}',
    },
    {
        name: 'alarmTime',
        value: '{{alarmTime}}',
        format: (text: any) => {
            return utils.formator.zeroTimeStampToFormatTime(text) || '-';
        },
    }
];

// 详情按钮显示控制
export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};
```

## 高级功能

### 1. 消息内容格式化

系统提供了强大的消息格式化功能，支持模板变量替换：

```typescript
// 模板内容示例
const messageTitle = '【{{vehicleNo}}】-【{{alarmTypeName}}】-【{{alarmTime}}】';
const messageContent = '车辆【{{vehicleNo}}】于【{{alarmTime}}】发生【{{alarmTypeName}}】，请尽快处理！';

// 使用工具函数格式化
const formatMessage = MessageUtils.getFormatMessage(messageTitle, labels, modelData);
const formatDescription = MessageUtils.getFormatDescription(messageContent, labels, modelData);
```

### 2. 自定义按钮组件

```typescript
// 使用公共按钮组件
import { MessageBtnDetail } from '@base-app/runtime-lib';

const btn = {
    text: '查看详情',
    onclick: () => {
        // 处理点击事件
        jumpToDetailPage();
    },
    handleDownloadExpire: true  // 是否处理下载过期逻辑
};

<MessageBtnDetail btn={btn} messageInfo={messageInfo} />
```

### 3. 页面跳转配置

```typescript
// 跳转配置
const jumpOptions = {
    pageType: 'alarm',
    suffixPath: `/detail/${alarmId}`,
    appId: modelData.appId,
    pageStatus: 'success'
};

// 使用工具函数跳转
MessageUtils.jumpPage(jumpOptions, history);
```

## 工具函数和公共组件

### 1. MessageUtils 工具函数

```typescript
import { MessageUtils } from '@base-app/runtime-lib';

// 消息格式化
MessageUtils.getFormatMessage(messageTitle, labels, modelData);
MessageUtils.getFormatDescription(messageContent, labels, modelData);

// 页面跳转
MessageUtils.jumpPage(jumpOptions, history);

// 速度单位转换
MessageUtils.getUnitName();
MessageUtils.convertSpeed(value);

// 文件下载
MessageUtils.download(fileId, type);
```

### 2. 公共组件

```typescript
// 默认消息列表项组件
import { MessageDefaultMessageItem } from '@base-app/runtime-lib';

// 默认通知渲染组件  
import { MessageDefaultNotificationRender } from '@base-app/runtime-lib';

// 详情按钮组件
import { MessageBtnDetail } from '@base-app/runtime-lib';
```

## 最佳实践

### 1. 组件结构建议

```
MessageTemplate/
├── index.ts              # 导出入口
├── MessageItem.tsx       # 消息列表项组件
├── NotificationTemplate.tsx  # 通知弹窗组件
├── type.ts              # 类型定义
├── const.ts             # 常量定义
└── utils.ts             # 工具函数
```

### 2. 注册时机

建议在应用入口文件中进行模板注册：

```typescript
// src/runtime-pages/index.message.demo.ts
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';

// 在应用启动时注册
registerTemplate(NotificationTypeMap.alarm, APP_ID, () => {
    return {
        NotificationTemplate,
        MessageItem
    }
});
```

### 3. 错误处理

```typescript
// 在组件中添加错误边界
const getMessage = (messageInfo: MessageInfo) => {
    try {
        const { titleId, title } = messageInfo;
        return titleId ? i18n.t(titleId, title) : title || '未知消息';
    } catch (error) {
        console.error('获取消息标题失败:', error);
        return '消息解析错误';
    }
};
```

### 4. 国际化支持

```typescript
// 支持多语言
const getMessage = (messageInfo: MessageInfo) => {
    const { titleId, title } = messageInfo;
    return titleId ? i18n.t(titleId, title) : title;
};
```

## 调试和排查

### 1. 开启调试日志

在 `mountCustomTemplate` 函数中取消注释日志输出：

```typescript
export const mountCustomTemplate = () => {
    // ...
    // 增加自定义模板的日志打印，方便排查
    console.log('==========【customTemplate】========', keys);
};
```

### 2. 常见问题

1. **模板未生效**: 检查注册时机，确保在系统初始化前完成注册
2. **消息格式化失败**: 检查 labels 配置和 modelData 数据结构
3. **按钮点击无响应**: 检查事件处理函数和权限配置
4. **国际化不生效**: 检查 titleId/contentId 配置和语言包

## 扩展开发

### 1. 中台如何新增内置模板类型

当需要在中台层面新增一个内置的消息模板类型时，需要按以下步骤操作：

#### 步骤1: 更新消息类型映射

在 `src/runtime-lib/MessageModule/MessageTemplate/index.ts` 中添加新的消息类型：

```typescript
// 通知类型
export const NotificationTypeMap = {
    alarm: 1,
    manualEvidence: 2,
    videoDownload: 3,
    fileExport: 4,
    tenantApply: 5,
    tenantStore: 6,
    vehicleStore: 7,
    tenantValidateTimeNotify: 8,
    promptCarousel: 9,
    licenseExpire: 10,
    tenantActiveResult: 12,
    newInternalType: 13,  // 新增内置类型
};
```

#### 步骤2: 创建默认模板组件

在 `src/runtime-lib/MessageModule/MessageTemplate/MessageItems/` 目录下创建新的模板组件：

```
MessageItems/
├── NewInternalType/
│   ├── index.ts
│   ├── MessageItem.tsx
│   ├── NotificationTemplate.tsx
│   └── utils.ts
```

**MessageItems/NewInternalType/MessageItem.tsx**:
```typescript
import React from 'react';
import { MessageDefaultMessageItem, MessageBtnDetail } from '../../../..';
import { MessageItemProps } from '../../type';
import { getMessage, getMessageContent, getBtn } from './utils';

export default (props: MessageItemProps) => {
    const { messageInfo, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;

    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);

    const btnDetail = showDetailBtn ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultMessageItem
            messageInfo={messageInfo}
            message={message}
            description={content}
            btnDetail={btnDetail}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

**MessageItems/NewInternalType/NotificationTemplate.tsx**:
```typescript
import React from 'react';
import { MessageDefaultNotificationRender, MessageBtnDetail } from '../../../..';
import { NotificationTemplateProps } from '../../type';
import { getMessage, getMessageContent, getBtn } from './utils';

export default (props: NotificationTemplateProps) => {
    const { messageInfo, handleClickNotice, prefixCls, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;

    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);

    const btnDetail = showDetailBtn ? (() => {
        const btn = getBtn(modelData);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={message}
            description={content}
            btnDetail={btnDetail}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

**MessageItems/NewInternalType/utils.ts**:
```typescript
import i18n from '../../../../i18n';
import { MessageInfo, ModalData } from '../../type';

export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId } = messageInfo;
    return i18n.t(titleId, '新类型消息通知');
};

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId } = messageInfo;
    return i18n.t(contentId, '');
};

export const getBtn = (modelData: ModalData) => {
    return {
        text: i18n.t('message', '查看详情'),
        onclick: () => {
            // 处理新类型消息的详情跳转逻辑
            console.log('查看新类型消息详情', modelData);
        },
    };
};
```

**MessageItems/NewInternalType/index.ts**:
```typescript
export { default as MessageItem } from './MessageItem';
export { default as NotificationTemplate } from './NotificationTemplate';
export * from './utils';
```

#### 步骤3: 更新导出和默认模板映射

在 `src/runtime-lib/MessageModule/MessageTemplate/MessageItems/index.ts` 中添加导出：

```typescript
export * as AlarmMessage from './AlarmMessage';
export * as PromptCarousel from './PromptCarousel';
// ... 其他导出
export * as NewInternalType from './NewInternalType';  // 新增导出
```

在 `src/runtime-lib/MessageModule/MessageTemplate/index.ts` 中更新导入和默认模板映射：

```typescript
import {
    AlarmMessage,
    PromptCarousel,
    // ... 其他导入
    NewInternalType,  // 新增导入
} from './MessageItems';

// 中台默认消息模板
const defaultTemplate: DefaultTemplate = {
    [NotificationTypeMap.alarm]: AlarmMessage,
    [NotificationTypeMap.manualEvidence]: ManualEvidence,
    // ... 其他映射
    [NotificationTypeMap.newInternalType]: NewInternalType,  // 新增映射
};
```

#### 步骤4: 添加类型定义（如需要）

如果新的消息类型有特殊的数据结构，在相关的 type.ts 文件中添加类型定义：

```typescript
// 新内置类型消息数据结构
export interface INewInternalTypeLabel {
    bizId: string;
    typeName: string;
    createTime: number;
    status: number;
    [key: string]: any;
}

// 更新联合类型
export type LabelKeyType =
    | keyof IManualEvidenceLabel
    | keyof IVideoDownloadLabel
    | keyof IFileExportLabel
    | keyof IAlarmLabel
    | keyof ITenantValidateTimeNotifyLabel
    | keyof INewInternalTypeLabel;  // 新增
```

### 2. 行业层如何新增自定义模板类型

行业层开发者可以为自己的业务场景创建完全自定义的消息类型，而不需要修改中台代码。

#### 步骤1: 定义自定义消息类型

```typescript
// 在行业层项目中定义自定义消息类型
export const CustomNotificationTypeMap = {
    // 使用较大的数值避免与内置类型冲突
    businessAlarm: 1001,
    workflowNotify: 1002,
    customReport: 1003,
    // ... 更多自定义类型
};
```

#### 步骤2: 创建自定义模板组件

在行业层项目中创建模板组件：

```
src/CustomMessageTemplate/
├── BusinessAlarm/
│   ├── index.ts
│   ├── MessageItem.tsx
│   ├── NotificationTemplate.tsx
│   ├── type.ts
│   ├── const.ts
│   └── utils.ts
```

**BusinessAlarm/type.ts**:
```typescript
import { MessageInfo as BaseMessageInfo, MessageItemProps as BaseMessageItemProps, NotificationTemplateProps as BaseNotificationTemplateProps } from '@base-app/runtime-lib';

// 业务报警特有的数据结构
export interface BusinessAlarmData {
    appId: number;
    businessId: string;
    businessType: string;
    businessName: string;
    alarmLevel: 'low' | 'medium' | 'high' | 'critical';
    triggerTime: number;
    affectedUsers: string[];
    [key: string]: any;
}

export interface MessageInfo extends BaseMessageInfo {
    modelData: BusinessAlarmData;
}

export interface MessageItemProps extends BaseMessageItemProps {
    messageInfo: MessageInfo;
}

export interface NotificationTemplateProps extends BaseNotificationTemplateProps {
    messageInfo: MessageInfo;
}
```

**BusinessAlarm/const.ts**:
```typescript
import { utils } from '@base-app/runtime-lib';

const { zeroTimeStampToFormatTime } = utils.formator;

// 业务报警模板标签
export const labels = [
    {
        name: 'businessName',
        value: '{{businessName}}',
    },
    {
        name: 'businessType',
        value: '{{businessType}}',
    },
    {
        name: 'alarmLevel',
        value: '{{alarmLevel}}',
        format: (level: string) => {
            const levelMap = {
                low: '低',
                medium: '中',
                high: '高',
                critical: '严重'
            };
            return levelMap[level] || level;
        },
    },
    {
        name: 'triggerTime',
        value: '{{triggerTime}}',
        format: (time: number) => {
            return zeroTimeStampToFormatTime(time) || '-';
        },
    },
    {
        name: 'affectedUsers',
        value: '{{affectedUsers}}',
        format: (users: string[]) => {
            return users?.join(', ') || '-';
        },
    },
];

export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};

// 报警级别颜色映射
export const ALARM_LEVEL_COLORS = {
    low: '#52c41a',
    medium: '#faad14',
    high: '#ff7a45',
    critical: '#ff4d4f',
};
```

**BusinessAlarm/utils.ts**:
```typescript
import { i18n } from '@base-app/runtime-lib';
import { MessageInfo, BusinessAlarmData } from './type';

export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId, title } = messageInfo;
    return titleId ? i18n.t(titleId, title) : title || '业务报警通知';
};

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId, content } = messageInfo;
    return contentId ? i18n.t(contentId, content) : content || '';
};

export const getBtn = (modelData: BusinessAlarmData, history: any) => {
    return {
        text: '处理报警',
        onclick: () => {
            // 跳转到业务报警处理页面
            history.push(`/business-alarm/handle/${modelData.businessId}`);
        },
    };
};

// 获取报警级别样式
export const getAlarmLevelStyle = (level: string) => {
    const { ALARM_LEVEL_COLORS } = require('./const');
    return {
        color: ALARM_LEVEL_COLORS[level] || '#666',
        fontWeight: 'bold',
    };
};
```

**BusinessAlarm/MessageItem.tsx**:
```typescript
import React from 'react';
import { MessageDefaultMessageItem, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { MessageItemProps } from './type';
import { getMessage, getMessageContent, getBtn, getAlarmLevelStyle } from './utils';
import { DETAIL_BTN, labels } from './const';

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: MessageItemProps) => {
    const { messageInfo, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;
    const history = useHistory();

    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);

    const btnDetail = showDetailBtn === DETAIL_BTN.SHOW ? (() => {
        const btn = getBtn(modelData, history);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    // 自定义渲染，添加报警级别标识
    const customContent = (
        <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <span>{formatMessage}</span>
                <span
                    style={{
                        ...getAlarmLevelStyle(modelData.alarmLevel),
                        marginLeft: 8,
                        padding: '2px 6px',
                        borderRadius: 4,
                        fontSize: 12,
                        backgroundColor: '#f0f0f0',
                    }}
                >
                    {labels.find(l => l.name === 'alarmLevel')?.format?.(modelData.alarmLevel)}
                </span>
            </div>
            <div>{formatDescription}</div>
        </div>
    );

    return (
        <MessageDefaultMessageItem
            messageInfo={messageInfo}
            message={customContent}
            description=""
            btnDetail={btnDetail}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

**BusinessAlarm/NotificationTemplate.tsx**:
```typescript
import React from 'react';
import { MessageDefaultNotificationRender, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { NotificationTemplateProps } from './type';
import { getMessage, getMessageContent, getBtn, getAlarmLevelStyle } from './utils';
import { DETAIL_BTN, labels } from './const';

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: NotificationTemplateProps) => {
    const { messageInfo, handleClickNotice, prefixCls, history, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;

    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);

    const btnDetail = showDetailBtn === DETAIL_BTN.SHOW ? (() => {
        const btn = getBtn(modelData, history);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={btnDetail}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

**BusinessAlarm/index.ts**:
```typescript
export { default as MessageItem } from './MessageItem';
export { default as NotificationTemplate } from './NotificationTemplate';
export * from './type';
export * from './const';
export * from './utils';
```

#### 步骤3: 注册自定义模板

在行业层应用入口文件中注册：

```typescript
// src/runtime-pages/index.message.demo.ts
import { registerTemplate } from '@base-app/runtime-lib';
import { CustomNotificationTypeMap } from './constants';
import { MessageItem as BusinessAlarmMessageItem, NotificationTemplate as BusinessAlarmNotificationTemplate } from '../CustomMessageTemplate/BusinessAlarm';

const APP_ID = 1; // 你的应用ID

// 注册业务报警模板
registerTemplate(CustomNotificationTypeMap.businessAlarm, APP_ID, () => {
    return {
        NotificationTemplate: BusinessAlarmNotificationTemplate,
        MessageItem: BusinessAlarmMessageItem
    }
});

// 注册其他自定义类型...
registerTemplate(CustomNotificationTypeMap.workflowNotify, APP_ID, () => {
    return {
        NotificationTemplate: WorkflowNotificationTemplate,
        MessageItem: WorkflowMessageItem
    }
});
```

### 3. 扩展工具函数

```typescript
// MessageTemplate/utils.ts
export const customFormatFunction = (data: any) => {
    // 自定义格式化逻辑
    return formattedData;
};
```

## 完整示例

### 报警消息模板完整实现

以下是一个完整的报警消息模板实现示例：

#### 1. 项目结构
```
src/MessageTemplate/
├── index.ts
├── MessageItem.tsx
├── NotificationTemplate.tsx
├── type.ts
├── const.ts
└── utils.ts
```

#### 2. 类型定义 (type.ts)
```typescript
import { MessageInfo as BaseMessageInfo, MessageItemProps as BaseMessageItemProps, NotificationTemplateProps as BaseNotificationTemplateProps } from '@base-app/runtime-lib';

// 扩展基础消息信息
export interface MessageInfo extends BaseMessageInfo {
    titleId?: string;
    title?: string;
    contentId?: string;
    content?: string;
    showDetailBtn?: number;
}

// 报警相关数据结构
export interface AlarmModelData {
    appId: number;
    alarmId: string;
    alarmTypeName: string;
    vehicleNo: string;
    driverName: string;
    deviceNo: string;
    alarmTime: number;
    alarmSpeed: number;
    [key: string]: any;
}

export interface MessageItemProps extends BaseMessageItemProps {
    messageInfo: MessageInfo;
}

export interface NotificationTemplateProps extends BaseNotificationTemplateProps {
    messageInfo: MessageInfo;
}
```

#### 3. 常量定义 (const.ts)
```typescript
import { MessageUtils } from '@base-app/runtime-lib';
import { utils } from '@base-app/runtime-lib';

const { getSpeedFormat, zeroTimeStampToFormatTime } = utils.formator;
const { getUnitName } = MessageUtils;

// 报警消息模板标签
export const labels = [
    {
        name: 'alarmTypeName',
        value: '{{alarmTypeName}}',
    },
    {
        name: 'vehicleNo',
        value: '{{vehicleNo}}',
    },
    {
        name: 'driverName',
        value: '{{driverName}}',
    },
    {
        name: 'deviceNo',
        value: '{{deviceNo}}',
    },
    {
        name: 'alarmTime',
        value: '{{alarmTime}}',
        format: (text: any) => {
            return zeroTimeStampToFormatTime(text) || '-';
        },
    },
    {
        name: 'alarmSpeed',
        value: '{{alarmSpeed}}',
        format: (text: any) => {
            return getSpeedFormat(text / 10) + getUnitName();
        },
    },
];

// 详情按钮显示控制
export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};
```

#### 4. 工具函数 (utils.ts)
```typescript
import { i18n, MessageUtils } from '@base-app/runtime-lib';
import { Btn, MessageInfo, AlarmModelData, JumpOptions } from "./type";
import { queryAlarmExist } from '@/service/alarm';

const { jumpPage } = MessageUtils;

// 检查报警是否存在
export const checkAlarmExist = async (alarmId: string): Promise<boolean> => {
    try {
        const result = await queryAlarmExist({ alarmId });
        return result?.exist || false;
    } catch (error) {
        console.error('检查报警存在性失败:', error);
        return false;
    }
};

// 获取消息标题
export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId, title } = messageInfo;
    return titleId ? i18n.t(titleId, title) : title || '报警通知';
};

// 获取消息内容
export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId, content } = messageInfo;
    return contentId ? i18n.t(contentId, content) : content || '';
};

// 生成详情按钮
export const getBtn = (modelData: AlarmModelData, pageConfig: any, history: any): Btn => {
    return {
        text: i18n.t('message', '查看详情'),
        onclick: async () => {
            const alarmExist = await checkAlarmExist(modelData.alarmId);
            if (!alarmExist) {
                message.error(i18n.t('message', '报警记录不存在或已被删除'));
                return;
            }

            const jumpOptions: JumpOptions = {
                pageType: 'alarm',
                suffixPath: `/detail/${modelData.alarmId}`,
                appId: modelData.appId,
            };
            jumpPage(jumpOptions, history, pageConfig);
        },
    };
};
```

#### 5. 消息列表项组件 (MessageItem.tsx)
```typescript
import { MessageDefaultMessageItem, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { MessageInfo, MessageItemProps } from "./type";
import { getMessage, getMessageContent, getBtn } from './utils';
import { useHistory } from '@base-app/runtime-lib/core';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: MessageItemProps) => {
    const { messageInfo, openPersonalCenter } = props;
    const { modelData, showDetailBtn } = messageInfo;
    const history = useHistory();

    // 获取消息标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);

    // 格式化消息内容
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);

    // 生成详情按钮
    const getMessageItemBtnDetail = (messageInfo: MessageInfo, pageConfig: any) => {
        const btn = getBtn(modelData, pageConfig, history);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    };

    return (
        <MessageDefaultMessageItem
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={showDetailBtn === DETAIL_BTN.SHOW ? getMessageItemBtnDetail(messageInfo, null) : null}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

#### 6. 通知弹窗组件 (NotificationTemplate.tsx)
```typescript
import { MessageDefaultNotificationRender, MessageUtils, MessageBtnDetail } from '@base-app/runtime-lib';
import { NotificationTemplateProps } from "./type";
import { getMessage, getMessageContent, getBtn } from './utils';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils;

export default (props: NotificationTemplateProps) => {
    const {
        messageInfo,
        handleClickNotice,
        prefixCls,
        history,
        openPersonalCenter
    } = props;
    const { modelData, showDetailBtn } = messageInfo;

    // 获取消息标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);

    // 格式化消息内容
    const formatMessage = getFormatMessage(message, labels, modelData);
    const formatDescription = getFormatDescription(content, labels, modelData);

    // 生成详情按钮
    const btnDetail = showDetailBtn === DETAIL_BTN.SHOW ? (() => {
        const btn = getBtn(modelData, null, history);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    })() : null;

    return (
        <MessageDefaultNotificationRender
            messageInfo={messageInfo}
            message={formatMessage}
            description={formatDescription}
            btnDetail={btnDetail}
            handleClickNotice={handleClickNotice}
            prefixCls={prefixCls}
            openPersonalCenter={openPersonalCenter}
        />
    );
};
```

#### 7. 入口文件 (index.ts)
```typescript
export { default as MessageItem } from './MessageItem';
export { default as NotificationTemplate } from './NotificationTemplate';
export * from './type';
export * from './const';
export * from './utils';
```

#### 8. 注册模板
```typescript
// 在应用入口文件中注册
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';
import { NotificationTemplate, MessageItem } from '../MessageTemplate';

const APP_ID = 1; // 应用ID

registerTemplate(NotificationTypeMap.alarm, APP_ID, () => {
    return {
        NotificationTemplate,
        MessageItem
    }
});
```

## API 参考

### 核心函数

#### registerTemplate(type, appId, callback)
注册自定义消息模板

**参数:**
- `type: number` - 消息类型，参考 NotificationTypeMap
- `appId: number | string` - 应用ID
- `callback: RegisterTemplateCallback` - 返回模板组件的回调函数

**示例:**
```typescript
registerTemplate(NotificationTypeMap.alarm, 1, () => ({
    NotificationTemplate: MyNotificationTemplate,
    MessageItem: MyMessageItem
}));
```

#### mountCustomTemplate()
挂载所有已注册的自定义模板（系统内部调用）

#### getMessageItem(props)
获取消息列表项组件

**参数:**
- `props: HandleMessageTypeProps` - 包含消息类型和消息信息的属性对象

#### getMessageNotificationRender(props)
获取通知弹窗组件

### 工具函数

#### MessageUtils.getFormatMessage(messageTitle, labels, modelData)
格式化消息标题

**参数:**
- `messageTitle: string` - 消息标题模板
- `labels?: Labels` - 标签配置数组
- `modelData?: ModalData` - 消息数据

**返回:** `string` - 格式化后的消息标题

#### MessageUtils.getFormatDescription(messageContent, labels, modelData)
格式化消息描述

**参数:**
- `messageContent: string | (() => string)` - 消息内容模板或函数
- `labels?: Labels` - 标签配置数组
- `modelData?: ModalData` - 消息数据

**返回:** `string` - 格式化后的消息描述

#### MessageUtils.jumpPage(jumpOptions, history, pageConfig?)
页面跳转

**参数:**
- `jumpOptions: JumpOptions` - 跳转配置
- `history: any` - 路由历史对象
- `pageConfig?: IJumpConfig` - 页面配置（可选）

### 类型定义

#### TemplateItem
```typescript
type TemplateItem = {
    NotificationTemplate: ReactNode;
    MessageItem: ReactNode;
};
```

#### MessageInfo
```typescript
type MessageInfo = {
    notificationType: number;
    modelData: ModalData;
    titleId?: string;
    title?: string;
    contentId?: string;
    content?: string;
    showDetailBtn?: number;
    [key: string]: any;
};
```

#### Labels
```typescript
type Labels = {
    name: string;
    value: string;
    format?: (text: any) => string;
}[];
```

## 总结

消息和通知模板模块通过插件化架构实现了高度的可扩展性和复用性。行业层开发者只需要：

1. 创建自定义的 MessageItem 和 NotificationTemplate 组件
2. 调用 registerTemplate 注册模板
3. 利用提供的工具函数和公共组件快速开发

这种设计既保证了系统的统一性，又提供了足够的灵活性来满足不同业务场景的需求。通过本指南提供的完整示例和API参考，新人可以快速上手并实现自定义的消息模板。

## 配置和部署

### 1. 环境配置

确保项目中已正确配置消息模块相关依赖：

```json
// package.json
{
  "dependencies": {
    "@base-app/runtime-lib": "^x.x.x",
    "@streamax/poppy": "^x.x.x",
    "@streamax/poppy-icons": "^x.x.x"
  }
}
```

### 2. 系统集成

在应用主入口文件中确保正确初始化：

```typescript
// src/main.ts 或 src/index.ts
import { lifecycle } from '@base-app/runtime-lib';

// 确保在系统启动前注册所有模板
import './runtime-pages/index.message.demo';

// 系统启动
lifecycle.start();
```

### 3. WebSocket 配置

消息通知依赖 WebSocket 连接，确保相关配置正确：

```typescript
// 在应用配置中确保 WebSocket 相关配置
const config = {
    websocket: {
        url: 'ws://your-websocket-server',
        reconnectInterval: 5000,
        maxReconnectAttempts: 10
    }
};
```

### 4. 权限配置

确保消息相关的权限码正确配置：

```typescript
// 权限码示例
const authCodes = {
    messageView: '@base:@page:message.center@action:view',
    messageDetail: '@base:@page:message.center@action:detail',
    messageExport: '@base:@page:message.center@action:export'
};
```

## 性能优化

### 1. 组件懒加载

对于复杂的消息模板组件，建议使用懒加载：

```typescript
import { lazy } from 'react';

const MessageItem = lazy(() => import('./MessageItem'));
const NotificationTemplate = lazy(() => import('./NotificationTemplate'));

registerTemplate(NotificationTypeMap.alarm, APP_ID, () => ({
    NotificationTemplate,
    MessageItem
}));
```

### 2. 消息缓存

对于频繁访问的消息数据，建议实现缓存机制：

```typescript
// 简单的消息缓存实现
class MessageCache {
    private cache = new Map();
    private maxSize = 100;

    get(key: string) {
        return this.cache.get(key);
    }

    set(key: string, value: any) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
}

const messageCache = new MessageCache();
```

### 3. 批量处理

对于大量消息的处理，建议使用批量处理机制：

```typescript
// 批量更新消息状态
const batchUpdateMessageStatus = async (messageIds: string[]) => {
    const batchSize = 50;
    const batches = [];

    for (let i = 0; i < messageIds.length; i += batchSize) {
        batches.push(messageIds.slice(i, i + batchSize));
    }

    const promises = batches.map(batch =>
        updateMessageStatus({ messageIds: batch })
    );

    await Promise.all(promises);
};
```

## 测试指南

### 1. 单元测试

```typescript
// MessageItem.test.tsx
import { render, screen } from '@testing-library/react';
import MessageItem from '../MessageItem';

describe('MessageItem', () => {
    const mockMessageInfo = {
        notificationType: 1,
        modelData: {
            appId: 1,
            alarmId: 'test-alarm-id',
            vehicleNo: '京A12345',
            alarmTypeName: '超速报警'
        }
    };

    it('should render message item correctly', () => {
        render(<MessageItem messageInfo={mockMessageInfo} />);
        expect(screen.getByText('京A12345')).toBeInTheDocument();
        expect(screen.getByText('超速报警')).toBeInTheDocument();
    });
});
```

### 2. 集成测试

```typescript
// MessageTemplate.integration.test.tsx
import { registerTemplate, NotificationTypeMap } from '@base-app/runtime-lib';
import { MessageItem, NotificationTemplate } from '../MessageTemplate';

describe('MessageTemplate Integration', () => {
    beforeAll(() => {
        registerTemplate(NotificationTypeMap.alarm, 1, () => ({
            NotificationTemplate,
            MessageItem
        }));
    });

    it('should register template successfully', () => {
        // 测试模板注册是否成功
        // 这里需要根据实际的测试环境进行调整
    });
});
```

### 3. E2E 测试

```typescript
// message-notification.e2e.test.ts
describe('Message Notification E2E', () => {
    it('should display notification when message received', async () => {
        // 模拟接收消息
        await page.evaluate(() => {
            window.dispatchEvent(new CustomEvent('websocket-message', {
                detail: {
                    type: 'notification',
                    data: {
                        notificationType: 1,
                        modelData: { /* 测试数据 */ }
                    }
                }
            }));
        });

        // 验证通知是否显示
        await expect(page.locator('.notification-popup')).toBeVisible();
    });
});
```

## 故障排查

### 1. 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 模板未生效 | 注册时机错误 | 确保在系统初始化前完成注册 |
| 消息格式化失败 | labels 配置错误 | 检查 labels 数组配置和数据结构 |
| 按钮点击无响应 | 事件处理函数错误 | 检查 onclick 函数实现 |
| 国际化不生效 | titleId/contentId 配置错误 | 检查国际化配置和语言包 |
| WebSocket 连接失败 | 网络或配置问题 | 检查网络连接和 WebSocket 配置 |

### 2. 调试技巧

```typescript
// 开启详细日志
localStorage.setItem('DEBUG_MESSAGE_TEMPLATE', 'true');

// 在组件中添加调试信息
console.log('MessageItem props:', props);
console.log('Formatted message:', formatMessage);
console.log('Model data:', modelData);
```

### 3. 性能监控

```typescript
// 添加性能监控
const performanceMonitor = {
    startTime: Date.now(),

    mark(label: string) {
        console.log(`[Performance] ${label}: ${Date.now() - this.startTime}ms`);
    }
};

// 在关键节点添加监控
performanceMonitor.mark('Template registered');
performanceMonitor.mark('Component rendered');
```

## 版本升级指南

### 从 v1.x 升级到 v2.x

1. **API 变更**
   - `registerTemplate` 函数签名保持不变
   - 新增 `MessageUtils.getIsExpire` 函数用于处理过期逻辑

2. **组件属性变更**
   - `MessageItemProps` 新增 `openPersonalCenter` 属性
   - `NotificationTemplateProps` 新增 `openPersonalCenter` 属性

3. **升级步骤**
   ```bash
   # 1. 更新依赖
   npm update @base-app/runtime-lib

   # 2. 更新组件代码
   # 在 MessageItem 和 NotificationTemplate 中添加 openPersonalCenter 支持

   # 3. 测试验证
   npm test
   ```

## 社区和支持

### 1. 文档资源
- [官方文档](https://docs.example.com/message-template)
- [API 参考](https://api-docs.example.com/message-template)
- [示例代码库](https://github.com/example/message-template-examples)

### 2. 技术支持
- 技术支持邮箱: <EMAIL>
- 开发者社区: https://community.example.com
- 问题反馈: https://github.com/example/issues

### 3. 贡献指南
欢迎提交 PR 和 Issue 来改进消息模板模块。请遵循以下规范：
- 代码风格遵循项目 ESLint 配置
- 提交前运行完整的测试套件
- 更新相关文档和示例代码

---

*本文档最后更新时间: 2025-01-29*
