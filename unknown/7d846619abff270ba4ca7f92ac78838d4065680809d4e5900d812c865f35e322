import { useState, useEffect, useRef } from 'react';
import {
    getAppGlobalData,
    i18n,
    reLoadLanguage,
    RouterPrompt,
    utils,
    StarryAbroadFormItem,
    StarryAbroadLRLayout,
    useSystemComponentStyle
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { Input, Form, Select, message, Space, Button, Container } from '@streamax/poppy';
// @ts-ignore
import { SketchPicker } from 'react-color';
import { StarryCard, StarryBreadcrumb } from '@base-app/runtime-lib'; // 使用公共组件
import InternationalInput from '../../../../components/InternationalInput';
import { addAlarmLevel, editAlarmLevel, fetchAlarmLevelDetail } from '../../../../service/alarm';
import { useAppList } from '../../../../hooks';
import { checkFieldSpace } from '@/utils/commonFun';
import './index.less';
import { RspFormLayout, useResponsiveShow } from '@streamax/responsive-layout';

export default (props: any) => {
    // BasePlatform和运维应用 需要过滤掉
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const history = useHistory();
    const { levelId, levelCode, appId } = props.location.query;
    const [loading, setLoading] = useState<boolean>(false);
    const [color, setColor] = useState<string>('#D0021B');
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [originData, setOriginData] = useState<any>({});
    const [erthValues, setErthValues] = useState<any>({});
    const [when, setWhen] = useState(true);
    const [form] = Form.useForm();
    const pickerRef = useRef<any>();
    const {
        isAbroadStyle,
      } = useSystemComponentStyle();
    const ColorDiv = (
        <div
            className="color-div"
            style={{ background: color }}
            onClick={() => setIsOpen((state) => !state)}
        />
    );
    const changeColor = (colorObj: any) => {
        setColor(colorObj.hex);
    };

    useEffect(() => {
        // 编辑回填表单
        if (levelId) {
            fetchAlarmLevelDetail({
                levelId,
            }).then((data: any) => {
                setColor(data.levelColor || '#D0021B');
                setOriginData(data);
                form.setFieldsValue({
                    ...data,
                    levelName: i18n.t(`@i18n:@alarmLevel__${data.uniqueCode}`, data.levelName),
                });
            });
        }else{
            form.setFieldsValue({
                appId: appId ? Number(appId) : (appList.length && appList[0] || null),
            });
        }
    }, []);
    useEffect(() => {
        form.setFieldsValue({ levelColor: color });
    }, [color]);
    // 点击空白关闭拾色器
    useEffect(() => {
        const handler = (e: Event) => {
            if (!pickerRef.current?.contains(e.target)) {
                if (isOpen) setIsOpen(false);
            }
        };
        window.addEventListener('click', handler);
        return () => window.removeEventListener('click', handler);
    }, [isOpen]);

    const handleInternationalInputSave = (values: any) => {
        setErthValues(values);
    };

    function onFinish(values: any) {
        const { langId, objectName, translationList, langKey } = erthValues;
        const params = {
            ...values,
            levelId: parseInt(levelId, 10),
            levelName:
                objectName ||
                (i18n.exists(`@i18n:@alarmLevel__${levelCode}`)
                    ? originData.levelName
                    : values.levelName),
        };
        setLoading(true);
        setWhen(false);
        if (levelId) {
            editAlarmLevel({
                ...params,
                appId: inSaaS ? params.appId : getAppGlobalData('APP_ID'),
                saveLanguageRequest: {
                    id: langId,
                    languageName: objectName,
                    langKey,
                    languageList: JSON.parse(
                        JSON.stringify(translationList).replace(/translationValue/g, 'translation'),
                    ),
                },
            })
                .then(async () => {
                    await reLoadLanguage(true, true);
                    history.goBack();
                    message.success(i18n.t('message', '操作成功'));
                })
                .finally(() => {
                    setLoading(false);
                });
        } else {
            // 新增
            if (Object.keys(erthValues).length !== 0) {
                const saveLanguageRequest: any = {
                    languageList: JSON.parse(
                        JSON.stringify(
                            translationList.filter((item: any) => !!item.translationValue),
                        ).replace(/translationValue/g, 'translation'),
                    ),
                    id: langId,
                    languageName: objectName,
                };
                params.saveLanguageRequest = saveLanguageRequest;
            }
            addAlarmLevel({
                ...params,
                appId: params.appId || getAppGlobalData('APP_ID'),
            })
                .then(async () => {
                    await reLoadLanguage(true, true);
                    history.goBack();
                    message.success(i18n.t('message', '操作成功'));
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    }
    const checkSpace = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '报警等级名称不能为空'));
    };
    const isResponsive = useResponsiveShow({
        xs: true,
        sm: true,
        md: false,
        lg: false,
        xl: false,
        xxl: false,
    });    
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <Container>
            <StarryCard title={i18n.t('name', '基本信息')}>
                <div className="general-setting-alarm-add">
                        <Form form={form} layout="vertical" onFinish={onFinish}>
                        <RspFormLayout layoutType='fixed'>
                                <RspFormLayout.Col>
                                {inSaaS ? (
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '归属应用')}
                                    name={'appId'}
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    className="form-item"
                                >
                                    <Select disabled={!!levelId} options={appList} />
                                </StarryAbroadFormItem>
                                    ) : null}
                                                               <StarryAbroadFormItem
                                label={i18n.t('name', '报警等级名称')}
                                name={'levelName'}
                                rules={[
                                    {
                                        required: true,
                                        validator: checkSpace,
                                    },
                                    {
                                        type: 'string',
                                        max: 50,
                                    },
                                    {
                                        validator: utils.validator.illegalCharacter,
                                    },
                                ]}
                                className="form-item international-form-input"
                                style={{width:isResponsive?'calc(100% - 35px)':'100%'}}        
                            >
                                <InternationalInput
                                    maxLength={50}
                                    allowClear
                                    modalType={levelId ? 'edit' : 'add'}
                                    internationalType="alarmLevel"
                                    entryKey={(originData || {}).levelName}
                                    // alarmLevel、alarmCategory的langKey是后面拼接uniqueCode
                                    entryIdOrCode={levelCode}
                                    onSave={(values) => handleInternationalInputSave(values)}
                                    placeholder={i18n.t('message', '请输入报警等级名称')}
                                />
                                    </StarryAbroadFormItem>
                                    <div className='color-picker-box'>
                               <StarryAbroadFormItem
                                    label={i18n.t('name', '报警颜色')}
                                    name={'levelColor'}
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    initialValue={color}
                                    className="form-item"
                                >
                                    <Input suffix={ColorDiv} readOnly />
                                
                                </StarryAbroadFormItem> 
                                <div
                                    className="color-picker"
                                    ref={pickerRef}
                                    style={{ display: isOpen ? 'block' : 'none' }}
                                >
                                    <SketchPicker
                                        width={'calc(100% - 20px)'}
                                        color={color}
                                        onChangeComplete={changeColor}
                                        presetColors={['#252324']}
                                    />
                                </div>
                            </div>
                        </RspFormLayout.Col>
                        </RspFormLayout>
                        <Form.Item className='btn-box'>
                                <StarryAbroadLRLayout>
                                    <Button
                                        onClick={() => {
                                            history.goBack();
                                        }}
                                    >
                                        {i18n.t('action', '取消')}
                                    </Button>
                                    <Button loading={loading} type="primary" htmlType="submit">
                                        {i18n.t('action', '保存')}
                                    </Button>
                                </StarryAbroadLRLayout>
                         </Form.Item>
                    </Form>
                </div>
            </StarryCard>
            </Container>
        </StarryBreadcrumb>
    );
};
